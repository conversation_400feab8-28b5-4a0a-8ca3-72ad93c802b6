{"tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "arrowParens": "always", "bracketSameLine": false, "bracketSpacing": true, "embeddedLanguageFormatting": "auto", "endOfLine": "lf", "htmlWhitespaceSensitivity": "ignore", "jsxSingleQuote": false, "printWidth": 120, "proseWrap": "preserve", "quoteProps": "as-needed", "singleAttributePerLine": false, "trailingComma": "all", "vueIndentScriptAndStyle": false}