# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

.idea

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# next.js
.next/
.swc/
out/
build

# expo
.expo

# misc
.DS_Store
*.pem
dist

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# turbo
.turbo
.yarn/cache/
.yarn/install-state.gz
