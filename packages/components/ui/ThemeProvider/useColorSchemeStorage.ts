import { useColorScheme as useColorSchemeNative } from "nativewind";
import { useState } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
export type Mode = "light" | "dark" | "system";
export function useColorSchemeStorage() {

  const [mode, setMode] = useState<Mode>("light");
  const {colorScheme: colorSchemeNative, setColorScheme: setColorSchemeNative} = useColorSchemeNative();

  function setColorScheme(newMode: Mode) {
    setMode(newMode);
    setColorSchemeNative(newMode);
    AsyncStorage.setItem("themeMode", newMode);
  }
  
  return {
    colorScheme: colorSchemeNative,
    mode,
    setColorScheme,
  };
}
