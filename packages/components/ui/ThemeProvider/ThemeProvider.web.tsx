// components/ui/ThemeProvider/ThemeProvider.tsx
'use client';

import React, { createContext, useState, useEffect, useContext } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useColorScheme } from 'nativewind';
import { useColorSchemeStorage } from './useColorSchemeStorage';
import { useSafeLayoutEffect } from '../gluestack-ui-provider/index.web';
type Mode = 'light' | 'dark' | 'system';
type Theme = 'light' | 'dark' | undefined;

interface ThemeContextType {
  theme: Theme;
  mode: Mode;
  toggleTheme: () => void;
}

export const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
  const { colorScheme, setColorScheme, mode } = useColorSchemeStorage();

  useEffect(() => {
    const initializeTheme = async () => {
      try {
        // const savedMode = await AsyncStorage.getItem("themeMode");
        const savedMode = 'light';
        if (savedMode && (['light', 'dark', 'system'] as Mode[]).includes(savedMode as Mode)) {
          setColorScheme((savedMode as Mode) || 'system');
          // 同步到 localStorage
          if (typeof window !== 'undefined') {
            localStorage.setItem('themeMode', savedMode);
          }
        }
      } catch (error) {
        console.error('Failed to initialize theme:', error);
      }
    };

    initializeTheme();
  }, []);

  const toggleTheme = async () => {
    let newMode: Mode = 'light';
    switch (mode) {
      case 'light':
        newMode = 'dark';
        break;
      case 'dark':
        newMode = 'system';
        break;
      case 'system':
        newMode = 'light';
        break;
    }
    setColorScheme(newMode);
    try {
      // 同步到 localStorage
      if (typeof window !== 'undefined') {
        await AsyncStorage.setItem('themeMode', newMode);
        localStorage.setItem('themeMode', newMode);
      }
    } catch (error) {
      console.error('Failed to save theme:', error);
    }
  };

  useSafeLayoutEffect(() => {
    if (mode && colorScheme) {
      const newColorScheme = mode === 'system' ? colorScheme : mode;
      document.documentElement.classList.remove(newColorScheme === 'light' ? 'dark' : 'light');
      document.documentElement.classList.add(newColorScheme);
      document.documentElement.style.colorScheme = newColorScheme;
    }
  }, [mode, colorScheme]);
  return <ThemeContext.Provider value={{ theme: colorScheme, toggleTheme, mode }}>{children}</ThemeContext.Provider>;
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
