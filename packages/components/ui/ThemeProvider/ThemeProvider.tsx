// components/ui/ThemeProvider/ThemeProvider.tsx
"use client";

import React, { createContext, useState, useEffect, useContext } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useColorScheme } from "nativewind";
import { useColorSchemeStorage } from "./useColorSchemeStorage";

type Mode = "light" | "dark" | "system";
type Theme = "light" | "dark" | undefined;

interface ThemeContextType {
  theme: Theme;
  mode: Mode;
  toggleTheme: () => void;
}

export const ThemeContext = createContext<ThemeContextType | undefined>(
  undefined
);

export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
  const {colorScheme, setColorScheme, mode} = useColorSchemeStorage();
  useEffect(() => {
    (async () => {
      // const savedMode = (await AsyncStorage.getItem("themeMode")) as
      //   | Mode
      //   | "light";
      const savedMode = 'light'
      if (savedMode) {
        setColorScheme(savedMode);
        AsyncStorage.setItem("themeMode", savedMode);
      }
    })();
  }, []);

  const toggleTheme = () => {
    let newMode: Mode = "light";
    switch (mode) {
      case "light":
        newMode = "dark";
        break;
      case "dark":
        newMode = "system";
        break;
      case "system":
        newMode = "light";
        break;
    }
    setColorScheme(newMode);
    AsyncStorage.setItem("themeMode", newMode);
  };

  return (
    <ThemeContext.Provider value={{ theme: colorScheme, mode, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};