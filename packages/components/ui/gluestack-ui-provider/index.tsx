import React from 'react';
import { config } from './config';
import { View } from 'react-native';
import { OverlayProvider } from '@gluestack-ui/overlay';
import { ToastProvider } from '@gluestack-ui/toast';
type ModeType = 'light' | 'dark' | 'system';

export function GluestackUIProvider({
  theme = 'light',
  mode = 'light',
  ...props
}: {
  theme?: 'light' | 'dark';
  mode?: ModeType;
  children?: any;
}) {
  return (
    <View
      style={[
        config[mode === 'system' ? theme : mode],
        { flex: 1, height: '100%', width: '100%' },
        // @ts-ignore
        props.style,
      ]}
    >
      <OverlayProvider>
        <ToastProvider>{props.children}</ToastProvider>
      </OverlayProvider>
    </View>
  );
}
