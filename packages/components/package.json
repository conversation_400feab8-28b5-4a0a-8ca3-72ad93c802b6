{"name": "@quantum/components", "version": "0.0.0", "main": "index.ts", "devDependencies": {"@types/react": "^18.2.46", "@types/react-native": "^0.73.0", "jscodeshift": "0.15.2", "typescript": "^5.3.3"}, "peerDependencies": {"nativewind": ">=4.0", "react": "^18.3.1", "react-native": "^0.76.7"}, "dependencies": {"@expo/html-elements": "latest", "@gluestack-ui/accordion": "latest", "@gluestack-ui/actionsheet": "latest", "@gluestack-ui/alert": "latest", "@gluestack-ui/alert-dialog": "latest", "@gluestack-ui/avatar": "^0.1.16", "@gluestack-ui/button": "latest", "@gluestack-ui/checkbox": "latest", "@gluestack-ui/divider": "latest", "@gluestack-ui/fab": "latest", "@gluestack-ui/form-control": "latest", "@gluestack-ui/icon": "latest", "@gluestack-ui/image": "latest", "@gluestack-ui/input": "latest", "@gluestack-ui/link": "latest", "@gluestack-ui/menu": "latest", "@gluestack-ui/modal": "latest", "@gluestack-ui/nativewind-utils": "latest", "@gluestack-ui/overlay": "latest", "@gluestack-ui/popover": "latest", "@gluestack-ui/pressable": "latest", "@gluestack-ui/progress": "latest", "@gluestack-ui/radio": "latest", "@gluestack-ui/select": "latest", "@gluestack-ui/slider": "latest", "@gluestack-ui/spinner": "latest", "@gluestack-ui/switch": "latest", "@gluestack-ui/textarea": "latest", "@gluestack-ui/toast": "latest", "@gluestack-ui/tooltip": "latest", "@legendapp/motion": "latest", "@unitools/image": "^0.0.4", "expo-image": "^1.12.9", "nativewind": "4.0.36", "react-native-reanimated": "~3.16.1", "react-native-svg": "^15.11.2", "tailwindcss": "3.4.3"}}