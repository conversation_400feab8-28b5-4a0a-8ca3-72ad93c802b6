{"include": ["*.tsx", "*.ts", "**/*.tsx", "**/*.ts"], "exclude": ["node_modules", "example"], "path": {}, "compilerOptions": {"ignoreDeprecations": "5.0", "noEmit": false, "declaration": true, "allowJs": true, "allowUnreachableCode": false, "allowUnusedLabels": true, "esModuleInterop": true, "verbatimModuleSyntax": true, "forceConsistentCasingInFileNames": true, "jsx": "preserve", "jsxImportSource": "nativewind", "lib": ["esnext", "dom"], "module": "esnext", "moduleResolution": "node", "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noImplicitUseStrict": false, "noStrictGenericChecks": false, "noUnusedLocals": false, "noUnusedParameters": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "esnext", "outDir": "."}}