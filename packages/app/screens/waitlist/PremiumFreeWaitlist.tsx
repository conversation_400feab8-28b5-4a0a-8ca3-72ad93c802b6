import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import { Button, ButtonText } from '@quantum/components/ui/button';
import Link from '@unitools/link';
import { useRequest } from 'ahooks';
import { authStore } from '../../store/auth.store';
import { joinWaitlist } from '../../services/waitlist/waitlist.api';
import ModalLogin from '../../components/auth/ModalLogin';
import { waitlistStore } from '../../store/waitlist.store';
import { Toast, ToastTitle, ToastDescription, useToast } from '@quantum/components/ui/toast';
import { useCustomToast } from '../../components/Toast/ToastProvider';
import { Trans, useTranslation } from 'react-i18next';
export default function PremiumFreeWaitlist() {
  const { showToast } = useCustomToast();
  const { run, loading } = useRequest(
    async () => {
      if (!authStore.isLoggedIn) {
        authStore.setShowLoginModal(true);
        return;
      }
      try {
        const res = await joinWaitlist();
        await waitlistStore.waitlistStatus.reload();
      } catch (error: any) {
        console.log(error);
        showToast({
          title: t('failed'),
          description: error.msg || t('join_failed'),
          status: 'error',
        });
      }
    },
    {
      manual: true,
    },
  );
  const { t } = useTranslation();
  return (
    <HStack className="w-full px-[32px] py-[20px] justify-between items-center rounded-[18px] shadow-[0px_3px_14px_0px_rgba(74,58,255,0.03),0px_-2px_4px_0px_rgba(20,20,43,0.02),0px_12px_24px_0px_rgba(20,20,43,0.04)]">
      <VStack className="gap-2">
        <Text className="text-[#0A0A0A] text-[20px] font-[700] leading-[24px]">
          {t('waitlist.home_page.premium_free_waitlist_title')}
        </Text>
        <HStack className="gap-2">
          <Text className="text-[#05C697] text-[20px] font-not-italic font-[500] leading-[24px]">
            <Trans
              i18nKey="waitlist.home_page.premium_free_waitlist"
              components={{
                h: <Text className="text-[#CCC] text-[20px]  font-[500] leading-[24px] line-through" />,
              }}
            />
          </Text>
        </HStack>
      </VStack>

      {waitlistStore.waitlistStatus.data?.result ? (
        <Button
          className="flex pt-[16px] pb-[17px] pl-[28px] pr-[28px] justify-end items-center gap-[10px] rounded-[50px] bg-[#ccc] data-[disabled]:bg-[#ccc] data-[hover=true]:bg-[#ccc]"
          disabled={true}
        >
          <Text className="text-[#FFFFFF] text-[16px]  font-[700] leading-[21px]">
            {t('waitlist.home_page.premium_free_waitlist_button_joined')}
          </Text>
        </Button>
      ) : (
        <Button
          className="flex pt-[16px] pb-[17px] pl-[28px] pr-[28px] justify-end items-center gap-[10px] rounded-[50px] bg-[rgba(5,198,151,0.10)]"
          onPress={() => {
            run();
          }}
          loading={loading}
        >
          <ButtonText className="text-[#05C697] text-[16px]  font-[700] leading-[21px]">
            {t('waitlist.home_page.premium_free_waitlist_button')}
          </ButtonText>
        </Button>
      )}
    </HStack>
  );
}
