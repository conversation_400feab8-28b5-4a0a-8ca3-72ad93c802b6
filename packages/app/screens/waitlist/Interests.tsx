import { Box } from '@quantum/components/ui/box';
import { Grid, GridItem } from '@quantum/components/ui/grid';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import Icon1 from '@quantum/shared/assets/waitlist/icon-1.svg';
import Icon2 from '@quantum/shared/assets/waitlist/icon-2.svg';
import Icon3 from '@quantum/shared/assets/waitlist/icon-3.svg';
import Icon4 from '@quantum/shared/assets/waitlist/icon-4.svg';
import Icon5 from '@quantum/shared/assets/waitlist/icon-5.svg';
import Image from '@unitools/image';
import { useTranslation } from 'react-i18next';
import PremiumFreeWaitlist from './PremiumFreeWaitlist';
import TypeWriterTitle from './TypeWriterTitle';

function InterestItem({ interest }: { interest: any }) {
  return (
    <VStack className="h-[100%] p-[48px] gap-[24px] flex-[1_0_0] rounded-[24px] bg-[#FFF] shadow-[0px_2px_12px_0px_rgba(20,20,43,0.08)]">
      <Image source={interest.icon} alt={interest.title} height={32} width={32} />
      <Text className="text-[#170F49] text-[24px] font-not-italic font-[700] leading-[32px]">{interest.title}</Text>
      <Text className="self-stretch text-[#4D4D4D] text-[18px] font-not-italic font-[400] leading-[24px]">
        {interest.description}
      </Text>
    </VStack>
  );
}

function Interests() {
  const { t } = useTranslation();
  const interests = [
    {
      icon: Icon1,
      title: t('waitlist.home_page.interests.follow'),
      description: t('waitlist.home_page.interests.follow_description'),
      span: 4,
    },
    {
      icon: Icon2,
      title: t('waitlist.home_page.interests.track'),
      description: t('waitlist.home_page.interests.track_description'),
      span: 4,
    },
    {
      icon: Icon3,
      title: t('waitlist.home_page.interests.trade_signal'),
      description: t('waitlist.home_page.interests.trade_signal_description'),
      span: 4,
    },
    {
      icon: Icon4,
      title: t('waitlist.home_page.interests.al_powered_tools'),
      description: t('waitlist.home_page.interests.al_powered_tools_description'),
      span: 8,
    },
    {
      icon: Icon5,
      title: t('waitlist.home_page.interests.portfolio_tracker'),
      description: t('waitlist.home_page.interests.portfolio_tracker_description'),
      span: 4,
    },
  ];
  return (
    <Grid
      className="gap-10"
      _extra={{
        className: 'grid-cols-12',
      }}
    >
      {interests.map((interest, index) => (
        <GridItem
          key={index}
          _extra={{
            className: `col-span-12 md:col-span-4 ${index === 3 ? 'md:col-span-8' : ''}`,
          }}
          // style={
          //   {
          //     'grid-column': `span ${interest.span} / span ${interest.span}`,
          //   } as any
          // }
        >
          <InterestItem interest={interest} />
        </GridItem>
      ))}
      <GridItem
        _extra={{
          className: 'col-span-12',
        }}
      >
        <Box className="relative overflow-hidden h-[100%] px-[32px] py-[40px] flex-col items-center gap-[40px] rounded-[18px] bg-[#FFF] shadow-[0px_3px_14px_0px_rgba(74,58,255,0.03),0px_-2px_4px_0px_rgba(20,20,43,0.02),0px_12px_24px_0px_rgba(20,20,43,0.04)]">
          <Box className="absolute top-0 left-[50%] translate-x-[-50%] w-full h-[100%]">
            <Image
              source={require('@quantum/shared/assets/waitlist/bottom-bg.png')}
              alt="bottom-bg"
              height={'100%'}
              width={787}
              style={{
                height: '100%',
                margin: 'auto',
              }}
            />
          </Box>
          <VStack className="gap-10">
            <Text className="mx-auto w-full max-w-[663px] text-[#0A0A0A] text-center text-[40px] font-not-italic font-[800] leading-[48px]">
              <TypeWriterTitle />
            </Text>
            <PremiumFreeWaitlist />
          </VStack>
        </Box>
      </GridItem>
    </Grid>
  );
}

export default Interests;
