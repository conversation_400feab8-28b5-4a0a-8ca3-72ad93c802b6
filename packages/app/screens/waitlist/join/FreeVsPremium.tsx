'use client';
import { Box } from '@quantum/components/ui/box';
import {
  Table,
  TableBody,
  TableData,
  TableHead,
  TableHeader,
  TableRow,
  TableFooter,
} from '@quantum/components/ui/table';
import { Text } from '@quantum/components/ui/text';
import { RiCheckboxCircleFill, RiCloseCircleFill } from 'react-icons/ri';
import { useMemo } from 'react';
import { HStack } from '@quantum/components/ui/hstack';
import { useTranslation } from 'react-i18next';
// 定义特权配置类型
type PrivilegeStatus = boolean | string;

interface Privilege {
  id: string;
  name: string;
  free: PrivilegeStatus;
  premium: PrivilegeStatus;
}

export default function FreeVsPremium() {
  const { t } = useTranslation();
  // 定义特权配置
  const privileges = useMemo<Privilege[]>(
    () => [
      {
        id: 'real-time-quotes',
        name: t('waitlist.join_page.table.privilege1'),
        free: true,
        premium: true,
      },
      {
        id: 'global-trader-analysis',
        name: t('waitlist.join_page.table.privilege2'),
        free: true,
        premium: true,
      },
      {
        id: 'trading-signals',
        name: t('waitlist.join_page.table.privilege3'),
        free: true,
        premium: true,
      },
      {
        id: 'custom-tracking',
        name: t('waitlist.join_page.table.privilege4'),
        free: false,
        premium: true,
      },
      {
        id: 'signal-bot',
        name: t('waitlist.join_page.table.privilege5'),
        free: false,
        premium: true,
      },
      {
        id: 'tradegpt-usage',
        name: t('waitlist.join_page.table.privilege6'),
        free: t('waitlist.join_page.table.privilege_price'),
        premium: true,
      },
      {
        id: 'ai-report',
        name: t('waitlist.join_page.table.privilege7'),
        free: t('waitlist.join_page.table.privilege_price'),
        premium: true,
      },
    ],
    [t],
  );

  // 渲染特权状态
  const renderStatus = (status: PrivilegeStatus) => {
    if (typeof status === 'string') {
      return (
        <TableData className="text-center">
          <Text className='text-[#808080] text-[18px] font-not-italic font-[500] leading-[24px]'>{status}</Text>
        </TableData>
      );
    }

    return (
      <TableData>
        <HStack className="justify-center">
          {status ? (
            <RiCheckboxCircleFill color="#05C697" size={20} />
          ) : (
            <RiCloseCircleFill color="#808080" size={20} />
          )}
        </HStack>
      </TableData>
    );
  };

  return (
    <Box className="flex w-full px-[32px] py-[40px] flex-col items-center gap-[40px] rounded-[18px] bg-[rgba(255,255,255,0.69)] shadow-[0px_3px_14px_0px_rgba(74,58,255,0.03),0px_-2px_4px_0px_rgba(20,20,43,0.02),0px_12px_24px_0px_rgba(20,20,43,0.04)] backdrop-blur-[50px]">
      <Text className="text-[#0A0A0A] text-center text-[40px] font-not-italic font-[800] leading-[48px]">
        {t('waitlist.join_page.free_vs_premium')}
      </Text>
      <Box className="mt-10 w-full">
        <Table className="w-full">
          <TableHeader>
            <TableRow className="bg-[rgba(5,198,151,0.03)] border-0 h-[60px]">
              <TableHead className="text-left  ">
                <Text className="text-[#808080] text-[18px] font-[500] leading-[24px]">
                  {t('waitlist.join_page.table.title1')}
                </Text>
              </TableHead>
              <TableHead className="text-center text-[#808080] text-[18px] font-[500] leading-[24px]">
                {t('waitlist.join_page.table.title2')}
              </TableHead>
              <TableHead className="text-center text-[#808080] text-[18px] font-[500] leading-[24px]">
                {t('waitlist.join_page.table.title3')}
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {privileges.map((privilege) => (
              <TableRow key={privilege.id} className="bg-transparent even:bg-[rgba(5,198,151,0.03)]  h-[60px] border-0">
                <TableData>
                  <Text className="flex-[1_0_0] text-[#0A0A0A] text-[18px] font-not-italic font-[600] leading-[24px]">
                    {privilege.name}
                  </Text>
                </TableData>
                {renderStatus(privilege.free)}
                {renderStatus(privilege.premium)}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Box>
    </Box>
  );
}
