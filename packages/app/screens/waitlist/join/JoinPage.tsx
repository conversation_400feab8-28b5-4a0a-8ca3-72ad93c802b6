'use client';
import { But<PERSON>, ButtonText, ButtonIcon } from '@quantum/components/ui/button';
import { HStack } from '@quantum/components/ui/hstack';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { RiArrowLeftLine } from 'react-icons/ri';
import { View } from '@quantum/components/ui/view';
import { VStack } from '@quantum/components/ui/vstack';
import FreeVsPremium from './FreeVsPremium';
import UserInfo from './UserInfo';
import useRouter from '@unitools/router';
import { waitlistStore } from '../../../store/waitlist.store';
import { useRequest } from 'ahooks';
import { joinWaitlist } from '../../../services/waitlist/waitlist.api';
import { useToast, ToastTitle, ToastDescription } from '@quantum/components/ui/toast';
import { authStore } from '../../../store/auth.store';
import { observer } from 'mobx-react-lite';
import { Trans, useTranslation } from 'react-i18next';
import { useCustomToast } from '../../../components/Toast/ToastProvider';
function JoinPage() {
  const router = useRouter();

  const { showToast } = useCustomToast();
  const { run, loading } = useRequest(
    async () => {
      if (!authStore.isLoggedIn) {
        authStore.setShowLoginModal(true);
        return;
      }
      try {
        const res = await joinWaitlist();
        waitlistStore.waitlistStatus.reload();
      } catch (error: any) {
        showToast(error.message);
      }
    },
    {
      manual: true,
    },
  );
  const { data: waitlistStatus } = useRequest(
    async () => {
      if (!authStore.isLoggedIn) {
        waitlistStore.waitlistStatus.setData(null);
        return;
      }
      const res = await waitlistStore.waitlistStatus.fetch();
      return res;
    },
    {
      refreshDeps: [authStore.isLoggedIn, authStore.currentToken],
    },
  );

  const { t } = useTranslation();
  return (
    <Box className="py-7">
      <Box className="inline-block">
        <Button
          onPress={() => router.back()}
          variant="solid"
          action="primary"
          className="inline-flex h-[64px] p-[16px] rounded-[99px] bg-[#FFF] hover:bg-[#fff] shadow-[0px_3px_14px_0px_rgba(74,58,255,0.03),0px_-2px_4px_0px_rgba(20,20,43,0.02),0px_12px_24px_0px_rgba(20,20,43,0.04)]"
        >
          <HStack className=" gap-[12px] items-center ">
            <RiArrowLeftLine size={32} className="text-[#000000]" />
            <ButtonText className="text-[#0A0A0A] text-center text-[18px] font-not-italic font-[700] leading-[24px]">
              {t('waitlist.join_page.back_to_home')}
            </ButtonText>
          </HStack>
        </Button>
      </Box>
      {waitlistStore.waitlistStatus.data?.result ? (
        <Box className="mt-[40px]">
          <UserInfo />
        </Box>
      ) : (
        <Box className="mt-[40px] ">
          <Box className="flex w-full px-[32px] py-[40px] flex-col items-center gap-[48px] rounded-[18px] bg-[rgba(255,255,255,0.69)] shadow-[0px_3px_14px_0px_rgba(74,58,255,0.03),0px_-2px_4px_0px_rgba(20,20,43,0.02),0px_12px_24px_0px_rgba(20,20,43,0.04)] backdrop-blur-[50px]">
            <VStack className="gap-[16px] items-center">
              <Text className="w-[663px] text-[#0A0A0A] text-center text-[40px] font-not-italic font-[800] leading-[48px]">
                {t('waitlist.join_page.join_premium_free_waitlist')}
              </Text>
              <HStack>
              
                <Text className="text-[#808080] text-center text-[24px] font-[500] leading-[30px]">
                 
                  <Trans
                  i18nKey="waitlist.join_page.get_30_days_of_free"
                  components={{
                    Text: <Text className="text-[#CCC] text-center text-[24px] font-[500] leading-[30px] line-through"/>,
                  }}
                />
                </Text>
                
              </HStack>
            </VStack>
            <Button
              className="flex min-w-[200px] pt-[16px] pb-[17px] pl-[28px] pr-[28px] justify-center items-center gap-[10px] rounded-[50px] bg-[rgba(5,198,151,0.10)] h-[54px]"
              onPress={run}
              loading={loading}
            >
              <Text className="text-[#05C697] text-[16px] font-not-italic font-[700] leading-[21px]">
                {t('waitlist.home_page.premium_free_waitlist_button')}
              </Text>
            </Button>
          </Box>
        </Box>
      )}

      <Box className="mt-[88px]">
        <FreeVsPremium />
      </Box>
    </Box>
  );
}

export default observer(JoinPage);
