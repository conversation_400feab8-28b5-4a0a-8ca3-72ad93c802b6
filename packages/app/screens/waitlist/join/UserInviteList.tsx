import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { Table, TableBody, TableData, TableHead, TableHeader, TableRow } from '@quantum/components/ui/table';
import type { ReferralItem } from '../../../services/waitlist/waitlist.types';
import dayjs from 'dayjs';
import Empty from '@quantum/app/components/Empty/Empty';
import { useTranslation } from 'react-i18next';
export default function InviteList({ list = [] }: { list: ReferralItem[] }) {
  const { t } = useTranslation();
  return (
    <Box>
      <Table className="w-full">
        <TableHeader>
          <TableRow className="bg-[#ffffff] border-0 h-[40px]">
            <TableHead className="text-center py-0">
              <Text className="text-[#808080] text-center text-[14px] font-not-italic font-[500] leading-[20px]">
                {t('waitlist.join_page.invitation_email')}
              </Text>
            </TableHead>
            <TableHead className="text-center py-0">
              <Text className="text-[#808080] text-[14px] font-not-italic font-[500] leading-[20px]">
                {t('waitlist.join_page.time')}
              </Text>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {list.map((privilege, index) => (
            <TableRow key={index} className="bg-[#ffffff] odd:bg-transparent  h-[40px] border-0">
              <TableData className="text-center py-0">
                <Text className="text-[#0A0A0A] text-[14px] font-not-italic font-[500] leading-[20px]">
                  {privilege.email}
                </Text>
              </TableData>
              <TableData className="text-center py-0">
                <Text className="text-[#0A0A0A] text-[14px] font-not-italic font-[500] leading-[20px]">
                  {dayjs.unix(Number(privilege.join_ts)).format('YYYY-MM-DD HH:mm:ss')}
                </Text>
              </TableData>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <Box className='bg-[#fff]'>{list.length === 0 && <Empty content={t('empty')} />}</Box>
    </Box>
  );
}
