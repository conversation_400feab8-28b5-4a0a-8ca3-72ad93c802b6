import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { useRequest } from 'ahooks';
import { useMemo } from 'react';
import { RiTwitterXFill } from 'react-icons/ri';
import { getWaitlistInfo, getWaitlistInviteList } from '../../../services/waitlist/waitlist.api';
import { userStore } from '../../../store/user.store';
import { averageNumber } from '../../../uitls/number';
import ProgressBar from './ProgressBar';
import InviteList from './UserInviteList';
import { useCustomToast } from '../../../components/Toast/ToastProvider';

import { CopyToClipboard } from 'react-copy-to-clipboard';
import { TwitterShareButton } from 'react-share';
import { Trans, useTranslation } from 'react-i18next';
import { authStore } from '../../../store/auth.store';
export default function UserInfo() {
  const { t } = useTranslation();
  const inviteUrl = useMemo(() => {
    return `${window.location.origin}/waitlist?inviteCode=${userStore.currentUserInfo?.invite_code || ''}`;
  }, [userStore.currentUserInfo]);

  const { data: info } = useRequest(
    async () => {
      const res = await getWaitlistInfo();
      return res;
    }
  );
  const { data: list = [] } = useRequest(
    async () => {
      if (!authStore.isLoggedIn) {
        return [];
      }
      const res = await getWaitlistInviteList();
      return res;
    },
    {
      refreshDeps: [authStore.currentToken, authStore.isLoggedIn],
    },
  );

  const days = useMemo(() => {
    return Math.min((list.length || 0) + 1, 3) * 10;
  }, [list]);

  const { showToast } = useCustomToast();
  const handleCopy = () => {
    showToast({
      title: t('copy_link_success'),
      description: t('copy_link_success_description'),
      status: 'success',
    });
  };
  return (
    <Box className="flex p-[40px] flex-col items-center rounded-[18px] bg-[rgba(255,255,255,0.69)] shadow-[0px_3px_14px_0px_rgba(74,58,255,0.03),0px_-2px_4px_0px_rgba(20,20,43,0.02),0px_12px_24px_0px_rgba(20,20,43,0.04)] backdrop-blur-[50px]">
      <VStack className="gap-10 w-full">
        <HStack className="w-full p-[12px] justify-center items-center gap-[10px] rounded-[12px] bg-[#E6F9F4]">
          <Text className="flex-[1_0_0] text-[#4D4D4D] text-center text-[16px] font-not-italic font-[500] leading-[20px] capitalize">
            <Text className="text-[#05C697]">{averageNumber(info?.total || 0)} </Text> users have claimed their free
            {t('waitlist.join_page.free_premium_claim')}
          </Text>
        </HStack>
        <VStack className="gap-6 w-full items-center">
          <Text className="text-[#0A0A0A] text-center text-[40px] font-not-italic font-[800] leading-[48px]">
            {t('waitlist.join_page.congratulation')}
          </Text>
          <Text className="max-w-[400px] text-[#4D4D4D] text-center text-[16px] font-not-italic font-[700] leading-[20px] ">
            <Trans
              i18nKey="waitlist.join_page.congratulation_description"
              components={{
                s: <Text className="underline text-[#05C697]" />,
              }}
              values={{
                num: days,
              }}
            />
          </Text>
        </VStack>
      </VStack>
      <VStack className="w-full mt-[100px] gap-9">
        <Text className="text-[#0A0A0A] text-center text-[40px] font-not-italic font-[800] leading-[48px]">
          {t('waitlist.join_page.invite_your_friends')}
        </Text>

        <VStack className="gap-4 w-full items-center">
          <Text className="text-[#4D4D4D] text-center text-[16px] font-not-italic font-[700] leading-[20px] ">
            <Trans
              i18nKey="waitlist.join_page.invite_your_friends_description"
              components={{
                s: <Text className="underline text-[#05C697]" />,
              }}
            />
          </Text>
          <ProgressBar value={((list.length || 0) + 1)} total={3} />
        </VStack>

        <Box className="px-[20px] py-[32px] rounded-[12px] bg-[rgba(0,0,0,0.03)]">
          <VStack className="gap-10">
            <HStack className="gap-10 h-[84px]">
              <VStack className="h-full w-[228px] justify-between items-center">
                <Text className="self-stretch text-[#0A0A0A] text-center text-[18px] font-not-italic font-[700] leading-[24px]">
                  {t('waitlist.join_page.referrals')}
                </Text>
                <Text className="self-stretch text-[#05C697] text-center text-[32px] font-not-italic font-[700] leading-[40px]">
                  {averageNumber(list.length)}
                </Text>
              </VStack>

              <VStack className="h-full w-full justify-between">
                <Text className="self-stretch text-[#0A0A0A] text-[18px] font-not-italic font-[700] leading-[24px]">
                  {t('waitlist.join_page.invite_links')}
                </Text>
                <HStack className="gap-3">
                  <HStack className="gap-3 items-center justify-between px-[16px] py-[14px]  flex-[1_0_0] rounded-[55px] bg-[#F0F0F0]">
                    <Text className="flex-[1_0_0] text-[#4D4D4D] text-[16px] font-not-italic font-[400] leading-[20px]">
                      {inviteUrl}
                    </Text>
                    <CopyToClipboard text={inviteUrl} onCopy={handleCopy}>
                      <button>
                        <Text className="text-[#05C697] text-[16px] font-not-italic font-[600] leading-[20px] cursor-pointer">
                          {t('waitlist.join_page.copy_link')}
                        </Text>
                      </button>
                    </CopyToClipboard>
                  </HStack>
                  <TwitterShareButton
                    url={inviteUrl}
                    title={t('waitlist.join_page.invite_your_friends_title_tips')}
                  >
                    <Box className="flex w-[48px] h-[48px] px-[16px] py-[14px] justify-center items-center gap-[269px] rounded-[55px] bg-[#F0F0F0]">
                      <RiTwitterXFill size={20} className="text-[#000000]" />
                    </Box>
                  </TwitterShareButton>
                </HStack>
              </VStack>
            </HStack>

            <VStack className="gap-4">
              <Text className="w-full text-[#0A0A0A] text-center text-[18px] font-not-italic font-[700] leading-[24px]">
                {t('waitlist.join_page.my_referrals')}
              </Text>

              <InviteList list={list} />
            </VStack>
          </VStack>
        </Box>
      </VStack>
    </Box>
  );
}
