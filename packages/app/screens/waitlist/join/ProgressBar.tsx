import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
export default function ProgressBar({ value, total }: { value: number; total: number }) {
  const progress = Math.min(value / total, 1);
  return (
    <VStack className="w-full gap-[6px]">
      <Box className="relative">
        <Box className="w-full h-[10px] rounded-[10px] bg-[rgba(5,198,151,0.25)]"></Box>
        <Box
          className="w-[10px] h-[10px] rounded-[10px] bg-[#05C697] absolute left-0 top-0 transition-all duration-500 ease-in-out"
          style={{
            width: `${progress * 100}%`,
          }}
        >
          {value > 0 && (
            <Box className="absolute right-0 top-0  translate-y-[-2px] w-[14px] h-[14px] rounded-full bg-[#05C697] border-[4px] border-[#fff]"></Box>
          )}
        </Box>
      </Box>

      <HStack className="w-full justify-end relative">
        {progress < 1 && (
          <Box
            className="absolute left-0 top-0 transition-all duration-500 ease-in-out"
            style={{
              width: `${progress * 100}%`,
            }}
          >
            <Text className="w-[47.839px] text-[#808080] text-center text-[12px] font-not-italic font-[500] leading-[16px] absolute right-0 translate-x-[50%]">
              {value * 10} days
            </Text>
          </Box>
        )}

        <Text className="w-[47.839px] text-[#808080] text-center text-[12px] font-not-italic font-[500] leading-[16px]">
          30 days
        </Text>
      </HStack>
    </VStack>
  );
}
