'use client';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { HStack } from '@quantum/components/ui/hstack';
import IconPlanFree from '@quantum/shared/assets/waitlist/plan-free.svg';
import IconPlanPremium from '@quantum/shared/assets/waitlist/plan-premium.svg';
import Image from '@unitools/image';
import { Grid, GridItem } from '@quantum/components/ui/grid';
import { VStack } from '@quantum/components/ui/vstack';
import { GoCheckCircleFill } from 'react-icons/go';
import { Button } from '@quantum/components/ui/button';
import Link from '@unitools/link';
import { useTranslation } from 'react-i18next';
function ItemPlan({ plan }: { plan: any }) {
  const { t } = useTranslation();
  const textColor = plan.isGreen ? '#FFFFFF' : '#170F49';
  const bgColor = plan.isGreen ? '#05C697' : '#FFF';
  const buttonColor = plan.isGreen ? '#FFFFFF' : '#05C697';
  const buttonTextColor = plan.isGreen ? '#05C697' : '#FFFFFF';
  return (
    <VStack
      className={`w-full h-[100%] p-[48px] gap-[64px] justify-between rounded-[24px] shadow-[0px_2px_12px_0px_rgba(20,20,43,0.08)]`}
      style={{
        backgroundColor: bgColor,
      }}
    >
      <VStack className="gap-[64px]">
        <HStack className="justify-between items-center">
          <HStack className="gap-8 items-center">
            <Image source={plan.icon} alt="IconPlanFree" height={72} width={72} />
            <Text
              className={`text-[40px] font-not-italic font-[700] leading-[48px]`}
              style={{
                color: textColor,
              }}
            >
              {plan.title}
            </Text>
          </HStack>

          {!!plan.label && (
            <HStack className="items-center justify-center w-[98px] h-[40px] shrink-0 rounded-[10px] bg-[rgba(255,255,255,0.20)]">
              <Text className="text-[#FFF] text-[14px] font-not-italic font-[600] leading-[14px]">{plan.label}</Text>
            </HStack>
          )}
        </HStack>
        <VStack className="gap-6">
          <Text
            className={`text-[24px] font-not-italic font-[700] leading-[32px]`}
            style={{
              color: textColor,
            }}
          >
            {plan.description}
          </Text>
          <VStack className="gap-4">
            {plan.features.map((feature: string, index: number) => (
              <HStack key={index} className="gap-[14px]">
                <GoCheckCircleFill
                  size={26}
                  style={{
                    color: textColor,
                  }}
                />
                <Text
                  className={`flex-1 text-[18px] font-not-italic font-[400] leading-[20px]`}
                  style={{
                    color: textColor,
                  }}
                >
                  {feature}
                </Text>
              </HStack>
            ))}
          </VStack>
        </VStack>
      </VStack>

      <Box>
        <Link href={plan.href}>
          <Button
            className="w-full h-[72px] justify-center items-center rounded-[96px]"
            style={{
              backgroundColor: buttonColor,
            }}
          >
            <Text
              className="text-center text-[18px] font-not-italic font-[700] leading-[20px]"
              style={{
                color: buttonTextColor,
              }}
            >
              {t('waitlist.home_page.get_started')}
            </Text>
          </Button>
        </Link>
      </Box>
    </VStack>
  );
}

export default function WaitListPlans() {
  const { t } = useTranslation();
  const plans = [
    {
      icon: IconPlanFree,
      title: t('waitlist.home_page.plan_free.title'),
      label: '',
      description: t('waitlist.home_page.plan_free.description'),
      features: [
        t('waitlist.home_page.plan_free.features_1'),
        t('waitlist.home_page.plan_free.features_2'),
        t('waitlist.home_page.plan_free.features_3'),
        t('waitlist.home_page.plan_free.features_4'),
        t('waitlist.home_page.plan_free.features_5'),
      ],
      isGreen: false,
      href: '/waitlist/join',
    },
    {
      icon: IconPlanPremium,
      title: t('waitlist.home_page.plan_premium.title'),
      label: t('waitlist.home_page.plan_premium.label'),
      description: t('waitlist.home_page.plan_premium.description'),
      features: [
        t('waitlist.home_page.plan_premium.features_1'),
        t('waitlist.home_page.plan_premium.features_2'),
        t('waitlist.home_page.plan_premium.features_3'),
        t('waitlist.home_page.plan_premium.features_4'),
        t('waitlist.home_page.plan_premium.features_5'),
      ],
      isGreen: true,
      href: '/waitlist/join',
    },
  ];
  return (
    <Box>
      <Text className="text-[#05C697] text-center  text-[18px] font-not-italic font-[700] leading-[20px] tracking-1.8px uppercase">
        {t('waitlist.home_page.our_pricing_plans')}
      </Text>
      <Text className="mt-4 self-stretch text-[#0A0A0A] text-center text-[50px] font-not-italic font-[800] leading-[62px]">
        {t('waitlist.home_page.stockbits_free_vs_premium')}
      </Text>
      <Grid
        className="mt-[64px] gap-6"
        _extra={{
          className: 'grid-cols-12',
        }}
      >
        {plans.map((plan, index) => (
          <GridItem
            key={index}
            _extra={{
              className: 'col-span-12 md:col-span-6',
            }}
          >
            <ItemPlan plan={plan} />
          </GridItem>
        ))}
      </Grid>
    </Box>
  );
}
