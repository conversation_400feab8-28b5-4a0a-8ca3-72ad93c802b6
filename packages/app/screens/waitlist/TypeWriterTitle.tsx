import { Trans } from 'react-i18next';
import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import { TypeWriter } from '../../components/TypeWriter';
import { useTranslation } from 'react-i18next';
import { useMemo, useState } from 'react';
import { Text } from '@quantum/components/ui/text';

export default function TypeWriterTitle() {
  const { t } = useTranslation();
  const printTexts = useMemo(() => {
    return [
      {
        value: 'stock',
        label: t('stock_market'),
      },
      {
        value: 'crypto',
        label: t('crypto_market'),
      },
    ];
  }, [t]);
  const [printValue, setPrintValue] = useState<string>('stock');
  const tootleTypeWriter = () => {
    if (printValue === 'crypto') {
      setPrintValue('stock');
    } else {
      setPrintValue('crypto');
    }
  };
  return (
    <>
      <Trans
        i18nKey="waitlist.home_page.title"
        components={{
          Box: <Box />,
          HStack: <HStack className="items-center justify-center" />,
          TypeWriter: (
            <TypeWriter
              texts={[printTexts.find((item) => item.value === printValue)?.label || '']}
              onComplete={() => {
                tootleTypeWriter();
              }}
              completeDelay={1000}
            />
          ),
        }}
      />
      <Text className="hidden">
        {t('stock_market')} {t('crypto_market')}
      </Text>
    </>
  );
}
