'use client';
import { Box } from '@quantum/components/ui/box';
import { Tabs } from '../../../components/Tabs/Tabs';
import { Text } from '@quantum/components/ui/text';
import { CommentList } from './CommentList';
import { Market, SocialMediaSort } from '../../../services/media/media.types';
import type { SocialMediaItem } from '../../../services/media/media.types';
import { useTranslation } from 'react-i18next';
import { observer } from 'mobx-react-lite';
import { authStore } from '../../../store/auth.store';
import { useEffect } from 'react';
import { watchlistStore } from '../../../store/watchlist.store';
const Comments = ({
  market,
  symbol,
  defaultData,
  showFavorites,
}: {
  market?: Market;
  symbol?: string;
  defaultData?: SocialMediaItem[];
  showFavorites?: boolean;
}) => {
  const { t } = useTranslation();


  return (
    <Tabs defaultIndex={0}>
      <Tabs.Tab label={t('home.comments.newest')}>
        <CommentList key="1" sort={SocialMediaSort.NEWEST} market={market} symbol={symbol} defaultData={defaultData} />
      </Tabs.Tab>
      <Tabs.Tab label={t('home.comments.hotest')}>
        <CommentList key="2" sort={SocialMediaSort.HOTTEST} market={market} symbol={symbol} defaultData={[]} />
      </Tabs.Tab>
      {/* <Tabs.Tab label={t('home.comments.hotest')}>
        <CommentList key="3" sort={SocialMediaSort.HOTTEST} market={market} symbol={symbol}/>
      </Tabs.Tab> */}  

      {
        authStore.isLoggedIn && showFavorites ? (
          <Tabs.Tab label={t('home.comments.favorites')}>
            <CommentList key="4" isFavorites={true}/>
          </Tabs.Tab>
        ) : <></>
      }
    </Tabs>
  );
};

export default observer(Comments);
