'use client';
import { Box } from '@quantum/components/ui/box';
import { Divider } from '@quantum/components/ui/divider';
import { HStack } from '@quantum/components/ui/hstack';
import { ScrollView } from '@quantum/components/ui/scroll-view';
import { Skeleton } from '@quantum/components/ui/skeleton';
import { Spinner } from '@quantum/components/ui/spinner';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { useDebounceFn, useRequest } from 'ahooks';
import dayjs from 'dayjs';
import 'dayjs/locale/en';
import 'dayjs/locale/zh-cn';
import { useEffect, useMemo, useRef, useState } from 'react';
import { RiChat1Line, RiHeartLine } from 'react-icons/ri';
import type { NativeScrollEvent, NativeSyntheticEvent } from 'react-native';
import { Platform } from 'react-native';
import Empty from '../../../components/Empty/Empty';
import ExpandableText from '../../../components/ExpandableText';
import { SkeletonImage } from '../../../components/SkeletonImage';
import TextOrImageLogo from '../../../components/TextOrImageLogo';
import { followKol, getSocialMedias, unfollowKol, getFollowingMedias } from '../../../services/media/media.api';
import {
  SocialMediaSort,
  SocialPlatform,
  type Market,
  type SocialMediaItem,
} from '../../../services/media/media.types';
import { useTranslation } from 'react-i18next';
import { authStore } from '../../../store/auth.store';
import { Button, ButtonText } from '@quantum/components/ui/button';
import { useCustomToast } from '../../../components/Toast/ToastProvider';
import { observer } from 'mobx-react-lite';
import { watchlistStore } from '../../../store/watchlist.store';

function CommentItemSkeleton() {
  return (
    <VStack className="gap-2">
      <HStack className="gap-[10px] items-center">
        <Skeleton variant="circular" className="w-[36px] h-[36px]" />
        <Skeleton className="w-[80px] h-[20px]" />
      </HStack>
      <HStack className="gap-[10px] items-center">
        <Box className="w-[36px] h-[36px] rounded-full overflow-hidden"></Box>
        <VStack className="gap-2 flex-1">
          <Skeleton className="w-[550px] h-[20px]" />
          <Skeleton className="w-[50%] h-[20px]" />
          <Skeleton className="w-[40%] h-[20px]" />
          <Skeleton className="w-[70%] h-[20px]" />
          <Skeleton className="w-[50%] h-[120px]" />
          <Skeleton className="w-[200px] h-[20px]" />
        </VStack>
      </HStack>
    </VStack>
  );
}

function CommentItem({ item, defaultFollowed }: { item: SocialMediaItem, defaultFollowed: boolean }) {
  const [isFollowed, setIsFollowed] = useState(!!defaultFollowed);

  const { showToast } = useCustomToast();
  const { run } = useDebounceFn(
    async (res: boolean) => {
      try {
        if (res) {
          await followKol({
            platform: item.author_platform,
            link: item.author_profile_url,
          });
        } else {
          await unfollowKol({
            platform: item.author_platform,
            link: item.author_profile_url,
          });
        }
      } catch (error) {
        console.log(error);
        setIsFollowed((prev) => !prev);
        showToast({
          title: res ? t('comments.followFailed') : t('comments.unfollowFailed'),
          description: t('comments.tryAgainLater'),
          status: 'error',
        });
      } finally {
        // watchlistStore.followingKol.reload();
      }
    },
    {
      wait: 500,
    },
  );

  const handleFollow = () => {
    if (!authStore.isLoggedIn) {
      authStore.setShowLoginModal(true);
      return;
    }
    const res = !isFollowed;
    setIsFollowed(res);
    run(res);
  };

  const  followingKol = useMemo(() => watchlistStore.followingKol.data, [watchlistStore.followingKol.data]);
  useEffect(() => {
    if (defaultFollowed) return;
    const isFollowedDefault = followingKol?.some((kol) => kol.profile_url === item.author_profile_url || kol.creator_name === item.creator_name);
    setIsFollowed(!!isFollowedDefault);
  }, [followingKol]);
  const { t } = useTranslation();
  return (
    <VStack className="gap-2">
      <HStack className="gap-2 items-center justify-between">
        <HStack className="gap-[10px] items-center">
          <TextOrImageLogo imageUrl={item?.creator_picture || ''} text={item?.creator_name || ''} size={36} />

          <Text className="text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[600] leading-[20px]">
            {item.creator_name}
          </Text>
        </HStack>

        {isFollowed ? (
          <Button
            variant="outline"
            className="w-min-[68px] h-[28px] px-[12px] py-[6px] justify-center items-center gap-[4px] rounded-[60px] border-[1px] border-solid border-[#B3B3B3]"
            onPress={handleFollow}
          >
            <ButtonText className="text-[#0A0A0A] font-[Poppins] text-[12px] font-not-italic font-500 leading-[16px]">
              {
                t('comments.followed')
              }
            </ButtonText>
          </Button>
        ) : (
          <Button
            className="w-min-[68px] h-[28px] px-[12px] py-[6px] justify-center items-center gap-[4px] rounded-[60px] bg-[rgba(0,0,0,0.08)]"
            onPress={handleFollow}
          >
            <ButtonText className="text-[#0A0A0A] font-[Poppins] text-[12px] font-not-italic font-500 leading-[16px]">
              {t('comments.follow')}
            </ButtonText>
          </Button>
        )}
      </HStack>
      <HStack className="gap-[10px] items-center">
        <Box className="w-[36px] h-[36px] rounded-full overflow-hidden"></Box>
        <VStack className="gap-2 flex-1">
          <Text className="self-stretch text-[#4D4D4D] font-Inter text-[14px] font-not-italic font-[500] leading-[20px]">
            {item.title}
          </Text>
          <a href={item.src || ''} target="_blank">
            <Text className="self-stretch text-[#4D4D4D] font-Inter text-[14px] font-not-italic font-[500] leading-[20px]">
              <ExpandableText text={item.content} />
            </Text>
          </a>
          {item.image?.startsWith('http') && (
            <a href={item.image} target="_blank" className="self-start">
              <SkeletonImage alt="" source={item.image || ''} className="w-[316px] h-[180px]" />
            </a>
          )}
          <Text className="text-[#808080] font-Inter text-[12px] font-not-italic font-[500] leading-[16px]">
            {dayjs(item.create_ts * 1000)
              .locale('en')
              .format('h:mm A · MMM D, YYYY')}
          </Text>
          <HStack className="gap-4 items-center">
            {[
              {
                icon: <RiChat1Line size={16} className="text-[#808080]" />,
                text: item.comments_count,
              },
              {
                icon: <RiHeartLine size={16} className="text-[#808080]" />,
                text: item.likes_count,
              },
            ].map((item, index) => {
              return (
                <HStack className="gap-1 p-2 items-center" key={index}>
                  {item.icon}
                  <Text className="text-[#4D4D4D] font-Inter text-[12px] font-not-italic font-[500] leading-[16px]">
                    {item.text}
                  </Text>
                </HStack>
              );
            })}
          </HStack>
        </VStack>
      </HStack>
    </VStack>
  );
}

const CommentItemComp = observer(CommentItem);

export const CommentList = ({
  sort,
  market,
  symbol,
  defaultData,
  isFavorites,
}: {
  sort?: SocialMediaSort;
  market?: Market;
  symbol?: string;
  defaultData?: SocialMediaItem[];
  isFavorites?: boolean;
}) => {
  const [list, setList] = useState<SocialMediaItem[]>(defaultData || []);
  const [nextCursor, setNextCursor] = useState<string | null | undefined>(undefined);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [hasRemovedFilters, setHasRemovedFilters] = useState<boolean>(false);
  const containerRef = useRef<any>(null);
  const isWeb = Platform.OS === 'web';

  useEffect(() => {
    if (!authStore.isLoggedIn) {
      watchlistStore.followingKol.setData(null);
      return;
    }
    watchlistStore.followingKol.fetch();
  }, [authStore.isLoggedIn, authStore.activeUserId]);

  type ResultType = { list: SocialMediaItem[]; nextCursor: string | null | undefined };

  const { loading, run } = useRequest<ResultType, [string?]>(
    async (cursor?: string) => {
      if (!sort) {
        return { list: [], nextCursor: null };
      }
      // 首次加载使用所有过滤条件，翻页时不使用 market 和 symbol
      const useFilters = cursor === undefined && !hasRemovedFilters;
      const res = await getSocialMedias(
        sort,
        10,
        cursor,
        useFilters ? market : undefined,
        useFilters ? symbol : undefined,
      );
      const newData = res.items || [];
      const newCursor = res.next_cursor;

      // 如果首次请求返回空数组且还没有尝试过移除过滤条件，则移除过滤条件重新请求
      if (cursor === undefined && newData.length === 0 && !hasRemovedFilters && (market || symbol)) {
        setHasRemovedFilters(true);
        // 重新请求，但不带 market 和 symbol
        run(undefined);
        return { list: [], nextCursor: null };
      }

      if (cursor === undefined) {
        // 首次加载或刷新
        setList(newData);
      } else {
        // 加载更多
        setList((prevList) => [...prevList, ...newData]);
      }

      setNextCursor(newCursor);
      setHasMore(!!newCursor);

      return { list: newData, nextCursor: newCursor };
    },
    {
      ready: !isFavorites,
      refreshDeps: [sort, market, symbol],
      manual: false,
    },
  );

  const { loading: favoritesLoading, run: favoritesRun } = useRequest<ResultType, [string?]>(
    async (cursor?: string) => {
  
      // 首次加载使用所有过滤条件，翻页时不使用 market 和 symbol
      const useFilters = cursor === undefined && !hasRemovedFilters;
      const res = await getFollowingMedias(
        SocialMediaSort.NEWEST,
        10,
        cursor,
        useFilters ? market : undefined,
        useFilters ? symbol : undefined,
      );
      const newData = res.items || [];
      const newCursor = res.next_cursor;

      // 如果首次请求返回空数组且还没有尝试过移除过滤条件，则移除过滤条件重新请求
      if (cursor === undefined && newData.length === 0 && !hasRemovedFilters && (market || symbol)) {
        setHasRemovedFilters(true);
        // 重新请求，但不带 market 和 symbol
        favoritesRun(undefined);
        return { list: [], nextCursor: null };
      }

      if (cursor === undefined) {
        // 首次加载或刷新
        setList(newData);
      } else {
        // 加载更多
        setList((prevList) => [...prevList, ...newData]);
      }

      setNextCursor(newCursor);
      setHasMore(!!newCursor);

      return { list: newData, nextCursor: newCursor };
    },
    {
      ready: !!isFavorites,
      refreshDeps: [sort, market, symbol],
      manual: false,
    },
  );

  // 处理内部 ScrollView 的滚动
  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const paddingToBottom = 20; // 距离底部多少开始加载

    if (
      !loading &&
      !favoritesLoading &&
      hasMore &&
      layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom
    ) {
      // 滚动到底部，加载更多

      if (isFavorites) {
        loadMoreFavorites();
      } else {
        loadMore();
      }
    }
  };

  // 处理窗口滚动（仅用于 Web）
  const handleWindowScroll = () => {
    if (!isWeb || loading || favoritesLoading || !hasMore) return;

    const scrollHeight = document.documentElement.scrollHeight;
    const scrollTop = window.scrollY || document.documentElement.scrollTop;
    const clientHeight = window.innerHeight || document.documentElement.clientHeight;
    const paddingToBottom = 20;

    if (scrollTop + clientHeight >= scrollHeight - paddingToBottom) {
      if (isFavorites) {
        loadMoreFavorites();
      } else {
        loadMore();
      }
    }
  };

  // 添加窗口滚动监听（仅用于 Web）
  useEffect(() => {
    if (isWeb) {
      window.addEventListener('scroll', handleWindowScroll);

      return () => {
        window.removeEventListener('scroll', handleWindowScroll);
      };
    }
  }, [loading, hasMore, nextCursor]); // 依赖这些状态，确保回调函数获取最新状态

  const loadMore = () => {
    if (!loading && hasMore && nextCursor) {
      run(nextCursor);
    }
  };

  const loadMoreFavorites = () => {
    if (!favoritesLoading && hasMore && nextCursor) {
      favoritesRun(nextCursor);
    }
  };

  const { t } = useTranslation();
  return (
    <ScrollView ref={containerRef} onScroll={handleScroll} scrollEventThrottle={16}>
      <VStack className="gap-4">
        {loading && list.length === 0 ? (
          <VStack className="gap-2">
            {Array.from({ length: 5 }).map((_, index) => (
              <CommentItemSkeleton key={index} />
            ))}
          </VStack>
        ) : (
          <>
            {list.length > 0 ? (
              <>
                {list.map((item: SocialMediaItem, index) => (
                  <VStack key={`${item.create_ts}-${item.creator_name}-${index}`} className="gap-4">
                    <CommentItemComp item={item} defaultFollowed={isFavorites} />
                    <Divider className="w-full h-[1px] bg-[#f5f5f5]" />
                  </VStack>
                ))}
              </>
            ) : (
              !loading && <Empty className="mt-[74px]" content={t('empty')} />
            )}
            {loading && (
              <HStack className="justify-center p-4">
                <Spinner size="small" />
                <Text className="ml-2 text-gray-500">{t('loading')}</Text>
              </HStack>
            )}
          </>
        )}
      </VStack>
    </ScrollView>
  );
};
