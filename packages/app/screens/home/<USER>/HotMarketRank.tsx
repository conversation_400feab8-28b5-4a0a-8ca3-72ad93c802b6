'use client';
import { HStack } from '@quantum/components/ui/hstack';
import { AddIcon, ChevronDownIcon, GlobeIcon, Icon, PlayIcon, SettingsIcon } from '@quantum/components/ui/icon';
import { Menu, MenuItem, MenuItemLabel } from '@quantum/components/ui/menu';
import { ScrollView } from '@quantum/components/ui/scroll-view';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';

import { Button, ButtonIcon, ButtonText } from '@quantum/components/ui/button';
import { Box } from '@quantum/components/ui/box';
import { RiArrowDownSLine, RiCheckLine, RiStarSFill } from 'react-icons/ri';
import { useState } from 'react';
import {
  Table,
  TableBody,
  TableData,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@quantum/components/ui/table';
import { useRequest } from 'ahooks';
import { getMarketTrending } from '../../../services/asset/asset.api';
import { Market, MarketTrendingSort, type TrendingItem } from '../../../services/asset/asset.types';
import { ButtonStar } from '../../../components/ButtonStar/ButtonStar';
import { useToggleStar } from '../../../hooks/useToggleStar';
import { watchlistStore } from '../../../store/watchlist.store';
import { observer } from 'mobx-react-lite';
import { sleep } from '../../../uitls';
import useRouter from '@unitools/router';
import { averageNumber } from '../../../uitls/number';
import { marketList } from '../../../consts';
import Empty from '../../../components/Empty/Empty';
import { Skeleton } from '@quantum/components/ui/skeleton';
import { useTranslation } from 'react-i18next';
import ChangeValue from '../../../components/ChangeValue';
function HotMarketRankSkeleton() {
  return (
    <TableRow className="h-[40px] items-center border-0 even:bg-[rgba(64,195,130,0.05)] cursor-pointer">
      <TableData className="hidden  md:table-cell p-1 w-[16px] shrink-0 text-[#808080] text-center font-Inter text-[14px] font-not-italic font-[400] leading-[20px] text-center">
        <Skeleton className="w-[16px] h-[20px]" />
      </TableData>
      <TableData className="p-1 text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[500] leading-[20px]  w-[150px]  px-4">
        <Skeleton className="w-[100px] h-[20px]" />
      </TableData>
      <TableData className="hidden  md:table-cell p-1  text-[#808080] font-Inter text-[14px] font-not-italic font-[500] leading-[20px] w-[250px]">
        <Skeleton className="w-[200px] h-[20px]" />
      </TableData>
      <TableData className="hidden  md:table-cell p-1 text-[#111827] font-Inter text-[14px] font-not-italic font-[500] leading-[20px]">
        <Skeleton className="w-[80px] h-[20px]" />
      </TableData>
      <TableData className="p-1  text-[#00AD6B] font-Inter text-[14px] font-not-italic font-[400] leading-[20px]">
        <Skeleton className="w-[80px] h-[20px]" />
      </TableData>
      <TableData className="p-1 text-[#111827] font-Inter text-[14px] font-not-italic font-[500] leading-[20px]">
        <Skeleton className="w-[80px] h-[20px]" />
      </TableData>
      <TableData className="hidden  md:table-cell p-1  text-[#111827] font-Inter text-[14px] font-not-italic font-[500] leading-[20px]">
        <Skeleton className="w-[80px] h-[20px]" />
      </TableData>
      <TableData className="p-1">
        <Box className="flex w-full h-full items-end">
          <Skeleton className="w-[16px] h-[16px]" />
        </Box>
      </TableData>
    </TableRow>
  );
}

function HotMarketRankItemComponent({ item, index }: { item: TrendingItem; index: number }) {
  const { runAsync: callOnToggleStar } = useToggleStar(item.market, item.symbol);
  const defaultStar = watchlistStore.watchlist.data?.some((ele) => {
    return ele.market === item.market && ele.symbol === item.symbol;
  });
  const handleOnToggleStar = async (value: boolean) => {
    await callOnToggleStar(value);
    await sleep(1000);
    await watchlistStore.watchlist.reload();
  };
  const router = useRouter();
  return (
    <TableRow
      className="h-[40px] items-center border-0 even:bg-[rgba(64,195,130,0.05)] cursor-pointer"
      onClick={() => {
        console.log('item', item);
        router.push(`/detail/${item.market}/${item.symbol}`);
      }}
    >
      <TableData className="hidden  md:table-cell p-1 w-[16px] shrink-0 text-[#808080] font-Inter text-[14px] font-not-italic font-[400] leading-[20px] text-center">
        {index + 1}
      </TableData>
      <TableData className="p-1 w-[150px]  px-4">
        <VStack>
          <Text className='text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[500] leading-[20px]'>{item.symbol}</Text>
          <Text className='md:hidden text-[#808080] font-[Inter] text-[12px] font-not-italic font-[500] leading-[20px]'>
          {item.name}
          </Text>
        </VStack>
      </TableData>
      <TableData className="hidden  md:table-cell p-1  text-[#808080] font-Inter text-[14px] font-not-italic font-[500] leading-[20px] w-[250px]">
        {item.name}
      </TableData>
      <TableData className="p-1 text-[#111827] font-Inter text-[14px] font-not-italic font-[500] leading-[20px]">
        {averageNumber(item.price)}
      </TableData>
      <TableData className="p-1">
        <ChangeValue
          hideIcon={true}
          change={item.change_rate + '%'}
          className="font-Inter text-[14px] font-not-italic font-[400] leading-[20px]"
        />
      </TableData>
      <TableData className="hidden  md:table-cell p-1 text-[#111827] font-Inter text-[14px] font-not-italic font-[500] leading-[20px]">
        {averageNumber(item.value, 2)}
      </TableData>
      <TableData className="hidden  md:table-cell p-1  text-[#111827] font-Inter text-[14px] font-not-italic font-[500] leading-[20px]">
        {averageNumber(item.market_cap, 2)}
      </TableData>
      <TableData className="p-1">
        <ButtonStar
          className="flex w-full h-full items-end"
          size={16}
          defaultStar={defaultStar}
          onToggleStar={handleOnToggleStar}
        />
      </TableData>
    </TableRow>
  );
}
const HotMarketRankItem = observer(HotMarketRankItemComponent);

export default function HotMarketRank() {
  const { t, ready, i18n } = useTranslation();
  const [market, setMarket] = useState<Market>(Market.US);
  const [sort, setSort] = useState<MarketTrendingSort>(MarketTrendingSort.CHANGE_RATE_DESC);
  const {
    data: list = [],
    loading,
    mutate,
  } = useRequest(
    async () => {
      try {
        const res = await getMarketTrending(market, sort);
        return res || [];
      } catch (error) {
        return [];
      }
    },
    {
      ready: !!ready,
      refreshDeps: [market, sort, i18n.language],
    },
  );

  const tabList = [
    {
      label: t('home.hot_market_rank_desc'),
      value: MarketTrendingSort.CHANGE_RATE_DESC,
    },
    {
      label: t('home.hot_market_rank_asc'),
      value: MarketTrendingSort.CHANGE_RATE_ASC,
    },
    {
      label: t('home.hot_market_rank_value_desc'),
      value: MarketTrendingSort.VALUE_DESC,
    },
  ];

  return (
    <VStack className="gap-0 md:gap-[23px]">
      <HStack className="justify-between min-h-[24px] px-2 py-3 md:p-0 md:pr-4 ">
        <HStack className="gap-4 items-center">
          <Text className="text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[700] leading-[20px]">
            {t('home.hot_market_rank')}
          </Text>

          <Box>
            <Menu
              placement="bottom left"
              offset={5}
              trigger={({ ...triggerProps }) => {
                return (
                  <Button variant="link" {...triggerProps} className="h-4">
                    <Text className="text-[#0A0A0A] font-Inter text-[12px] font-not-italic font-[400] leading-[16px]">
                      {t(marketList.find((item) => item.value === market)?.i18nKey || '')}
                    </Text>
                    <RiArrowDownSLine size={16} className="text-[#808080]" />
                  </Button>
                );
              }}
              className="p-[10px] rounded-[6px] border-[1px] border-solid border-[#E6E6E6] bg-[#FFF] shadow-[0px_8px_20px_0px_rgba(0,0,0,0.10)]"
              selectionMode="single"
            >
              {marketList.map((item) => (
                <MenuItem
                  onPress={() => {
                    mutate([]);
                    setMarket(item.value);
                  }}
                  key={item.value}
                  textValue={item.value}
                  className="px-[8px] py-[10px] items-center gap-[16px] self-stretch rounded-[6px] bg-[#FFF] data-[hover=true]:bg-[rgba(0,173,107,0.05)]"
                >
                  <HStack className="items-center justify-between w-full">
                    <Text className="text-[#4D4D4D] font-Poppins text-[14px] font-not-italic font-[400] leading-[20px]">
                      {t(item.i18nKey || '')}
                    </Text>
                    {market == item.value && <RiCheckLine size={16} className="text-[#40C382]" />}
                  </HStack>
                </MenuItem>
              ))}
            </Menu>
          </Box>
        </HStack>
        <HStack className="gap-3">
          {tabList.map((item, index) => (
            <Button
              key={index}
              className={`h-6 px-[8px] py-[4px] justify-center items-center gap-[6px] rounded-[60px] data-[hover=true]:bg-[#40C382]  ${
                sort == item.value ? 'bg-[#40C382] ' : 'bg-[#F0F0F0]'
              }`}
              onPress={() => {
                mutate([]);
                setSort(item.value);
              }}
            >
              <ButtonText
                className={`text-[12px] font-not-italic font-[400] leading-[16px] data-[hover=true]:text-[#f5f5f5] ${
                  sort == item.value ? 'text-[#f5f5f5]' : 'text-[#808080]'
                }`}
              >
                {item.label}
              </ButtonText>
            </Button>
          ))}
        </HStack>
      </HStack>
      <ScrollView className="flex-1 pr-4">
        <Table className="w-full">
          <TableHeader className="h-[24px] items-center sticky top-0 z-10 bg-[#fff]">
            <TableRow className="h-[24px] border-0 bg-[rgba(64,195,130,0.05)]">
              <TableHead className="hidden md:table-cell p-0 text-[#808080] font-Inter text-[12px] font-not-italic font-[400] leading-[20px] w-[20px] text-center">
                -
              </TableHead>
              <TableHead className="p-1 text-[#808080] font-Inter text-[12px] font-not-italic font-[400] leading-[20px] w-[150px] px-4">
                {t('home.hot_market_rank_table.name')}
              </TableHead>
              <TableHead className="hidden md:table-cell p-1 text-[#808080] font-Inter text-[12px] font-not-italic font-[400] leading-[20px] w-[250px]"></TableHead>
              <TableHead className="p-1 text-[#808080] font-Inter text-[12px] font-not-italic font-[400] leading-[20px]">
                {t('home.hot_market_rank_table.price')}
              </TableHead>
              <TableHead className="p-1 text-[#808080] font-Inter text-[12px] font-not-italic font-[400] leading-[20px]">
                {t('home.hot_market_rank_table.change_rate')}
              </TableHead>
              <TableHead className="hidden md:table-cell p-1 text-[#808080] font-Inter text-[12px] font-not-italic font-[400] leading-[20px]">
                {t('home.hot_market_rank_table.value')}
              </TableHead>
              <TableHead className="hidden md:table-cell p-1 text-[#808080] font-Inter text-[12px] font-not-italic font-[400] leading-[20px]">
                {t('home.hot_market_rank_table.market_cap')}
              </TableHead>
              <TableHead className="p-1 text-[#808080] font-Inter text-[12px] font-not-italic font-[400] leading-[20px] w-[80px] text-right"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <>
                {Array.from({ length: 10 }).map((_, index) => (
                  <HotMarketRankSkeleton key={index} />
                ))}
              </>
            ) : (
              <>
                {list.map((item, index) => (
                  <HotMarketRankItem item={item} index={index} key={item.market + item.symbol} />
                ))}
              </>
            )}
          </TableBody>
        </Table>

        {!loading && list.length === 0 && (
          <Box className="flex w-full h-full items-center justify-center pt-[80px]">
            <Empty content={t('empty')} />
          </Box>
        )}
      </ScrollView>
    </VStack>
  );
}
