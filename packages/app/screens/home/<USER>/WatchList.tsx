'use client';
import { Box } from '@quantum/components/ui/box';
import { Divider } from '@quantum/components/ui/divider';
import { HStack } from '@quantum/components/ui/hstack';
import { Pressable } from '@quantum/components/ui/pressable';
import { ScrollView } from '@quantum/components/ui/scroll-view';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import Image from '@unitools/image';
import useRouter from '@unitools/router';
import { observer } from 'mobx-react-lite';
import Empty from '../../../components/Empty/Empty';
import LineGraph from '../../../components/LineGraph/LineGraph';
import { type WatchListItem } from '../../../services/watchlist/watchlist.types';
import { watchlistStore } from '../../../store/watchlist.store';
import { averageNumber } from '../../../uitls/number';
import TextOrImageLogo from '../../../components/TextOrImageLogo';
import { getKline, getKlineMock } from '../../../services/kline/kline.api';
import { useRequest } from 'ahooks';
import { AliKlineInterval } from '../../../services/kline/kline.types';
import { Skeleton } from '@quantum/components/ui/skeleton';
import { useTranslation } from 'react-i18next';
import { authStore } from '../../../store/auth.store';
import { AssetsTypeIcon } from '../../../components/AssetsTypeIcon';
import { Tooltip, TooltipContent, TooltipText } from '@quantum/components/ui/tooltip';

function WatchListItemSkeleton() {
  return (
    <HStack className="w-full h-full items-center min-h-[56px]">
      <HStack className="flex-1 gap-3 pr-1 items-center">
        <Skeleton variant="circular" className="w-[36px] h-[36px]" />
        <VStack className="flex-1 gap-1">
          <Skeleton className="w-[70%] h-[16px]" />
          <Skeleton className="w-[50%] h-[14px]" />
        </VStack>
      </HStack>
      <Box className="w-[67px] h-[40px]">
        <Skeleton className="w-full h-full" />
      </Box>
      <VStack className="w-[110px] items-end gap-1 pl-1">
        <Skeleton className="w-[30%] h-[16px]" />
        <Skeleton className="w-[50%] h-[14px]" />
      </VStack>
    </HStack>
  );
}

function WatchListItem({ item }: { item: any }) {
  const router = useRouter();
  const { t } = useTranslation();

  const { data = [] } = useRequest(
    async () => {
      const res = await getKline(item.market, item.symbol, AliKlineInterval.DAY_1, 7);
      return res;
    },
    {
      refreshDeps: [item.market, item.symbol],
      cacheKey: `kline-${item.market}-${item.symbol}-day-7`,
      staleTime: 600 * 1000, // 10分钟缓存
    },
  );

  return (
    <Pressable
      onPress={() => {
        router.push(`/detail/${item.market}/${item.symbol}`);
      }}
    >
      <HStack className="w-full h-full items-center min-h-[56px]">
        <HStack className="flex-1 gap-3 pr-1 items-center">
          <Box className="w-[36px] h-[36px] rounded-full overflow-hidden">
            <AssetsTypeIcon type={item.market} size={36} />
          </Box>
          <VStack className="flex-1">
            <Tooltip
              placement="top"
              trigger={(triggerProps) => {
                return (
                  <Text
                    className="text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[600] leading-[20px] overflow-hidden"
                    numberOfLines={2}
                    style={
                      {
                        WebkitLineClamp: 1,
                        display: '-webkit-box',
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      } as any
                    }
                    {...triggerProps}
                  >
                    {item.name}
                  </Text>
                );
              }}
            >
              <TooltipContent>
                <TooltipText>{item.name}</TooltipText>
              </TooltipContent>
            </Tooltip>

            <Text className="self-stretch text-[#808080] font-Inter text-[12px] font-not-italic font-[400] leading-[16px]">
              {item.symbol}
            </Text>
          </VStack>
        </HStack>
        <Box className="w-[60px] h-[40px]">
          <LineGraph isInteractive={false} data={data} />
        </Box>
        <VStack className="w-[100px] items-end">
          <Text className="text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[600] leading-[20px]">
            {averageNumber(item.price)}
          </Text>
          <Text
            className={`text-[14px]  font-not-italic font-[600] leading-[20px] text-right ${
              Number(item.change_rate) > 0 ? 'text-[#00AD6B]' : 'text-[#FF0000]'
            }`}
          >
            {averageNumber(item.change_rate, 2)}%
          </Text>
        </VStack>
      </HStack>
    </Pressable>
  );
}

function WatchList({ height }: { height: number }) {
  const list = watchlistStore.watchlist.data || [];
  const loading = watchlistStore.watchlist.loading;
  const { t, ready, i18n } = useTranslation();
  useRequest(
    async () => {
      await watchlistStore.watchlist.reload();
    },
    {
      ready: !!ready && !!authStore.isLoggedIn,
      refreshDeps: [i18n.language, authStore.currentToken],
    },
  );
  return (
    <VStack className="w-full h-full gap-3">
      <Text className="text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[700] leading-[20px] tracking--0.56px">
        {t('home.watchlist')}
      </Text>
      <Divider className="bg-[#f5f5f5]" />

      <ScrollView className={`w-full overflow-auto ${height ? `h-[${height}px]` : 'flex-1'}`}>
        {loading && list.length === 0 ? (
          <VStack className="gap-2 ">
            {Array.from({ length: 5 }).map((_, index) => (
              <WatchListItemSkeleton key={index} />
            ))}
          </VStack>
        ) : (
          <>
            {list.length > 0 ? (
              <VStack className="gap-2 ">
                {list.map((item, index) => (
                  <>
                    <WatchListItem key={item.symbol + item.market} item={item} />

                    {index < list.length - 1 && (
                      <Divider key={`line-${item.symbol + item.market}`} className="w-full h-[1px] bg-[#f5f5f5]" />
                    )}
                  </>
                ))}
              </VStack>
            ) : (
              <Empty className="mt-[74px]" content={t('empty_watchlist')} />
            )}
          </>
        )}
      </ScrollView>
    </VStack>
  );
}

export default observer(WatchList);
