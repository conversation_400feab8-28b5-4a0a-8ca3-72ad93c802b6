'use client';
import SentimentGauge from '@quantum/app/components/SentimentGauge';
import { Box } from '@quantum/components/ui/box';
import { Grid, GridItem } from '@quantum/components/ui/grid';
import { HStack } from '@quantum/components/ui/hstack';
import { Skeleton } from '@quantum/components/ui/skeleton';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { useRequest } from 'ahooks';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import Empty from '../../../components/Empty/Empty';
import { getMarketSentiment } from '../../../services/asset/asset.api';
import { Market } from '../../../services/asset/asset.types';
import EponentCard from './EponentCard';
interface SentimentIndicatorProps {
  title: string;
  descAry: string[];
  value: number;
  showValue: number;
  className?: string;
  labels?: string[];
}


function formatLongShortRatio(value: number) {
  if (value >= 2) {
    return 81;
  } else if (value >= 1.5 && value < 2) {
    return 61;
  } else if (value >= 1 && value < 1.5) {
    return 41;
  } else if (value >= 0.7 && value < 1) {
    return 21;
  } else {
    return 1;
  }
}

function formatViX(value: number) {
  // 0-12、12-18、18-24、24-36、36+
  if (value >= 36) {
    return 81;
  } else if (value >= 24 && value < 36) {
    return 61;
  } else if (value >= 18 && value < 24) {
    return 41;
  } else if (value >= 12 && value < 18) {
    return 21;
  } else {
    return 1;
  }
}
function formatPCRatio(value: number) {
  // 0.7-0.8、0.8-0.9、0.9-1、1-1.1、1.1+
  if (value >= 1.1) {
    return 81;
  } else if (value >= 1 && value < 1.1) {
    return 61;
  } else if (value >= 0.9 && value < 1) {
    return 41;
  } else if (value >= 0.8 && value < 0.9) {
    return 21;
  } else {
    return 1;
  }
}

function SentimentIndicatorSkeleton() {
  return (
    <VStack className={`gap-[11px] h-full p-3 rounded-[12px] border-[1px] border-solid border-[#F5F5F5] bg-[#FFF]`}>
      <HStack className="gap-[6px]">
        <Skeleton variant="circular" className="w-[20px] h-[20px]" />
        <Skeleton className="w-[30%] h-[20px]" />
      </HStack>
      <HStack className="gap-2 items-center">
        <VStack className="flex-1 gap-1">
          <Skeleton className="w-[80%] h-[20px]" />
          <Skeleton className="w-[60%] h-[20px]" />
        </VStack>
        <Box className="w-[56px] h-[44px]">
          <Skeleton className="w-full h-full" />
        </Box>
      </HStack>
    </VStack>
  );
}

function SentimentIndicator({
  title,
  descAry,
  value,
  showValue,
  className = '',
  labels = ['1', '2', '3', '4', '5'],
}: SentimentIndicatorProps) {
  const index = useMemo(() => {
    return Math.floor(Math.min(value, 100) / 20);
  }, [value]);
  return (
    <View className={`${className}`}>
      <VStack className="gap-2 items-center">
        <Box className="max-w-[100px] max-h-[60px]">
          <SentimentGauge value={value} labels={labels} showValue={showValue} />
        </Box>
        <Text className="text-[#0A0A0A] font-Inter text-[12px] font-not-italic font-[600] leading-[16px]">{title}</Text>
        <Text className="self-stretch text-[#808080] text-center font-Inter text-[12px] font-not-italic font-[500] leading-[16px]">
          {descAry[index]}
        </Text>
      </VStack>
    </View>
  );
}

export default function SentimentIndicators() {
  const { t, ready, i18n } = useTranslation();
  const [selectedIndex, setSelectedIndex] = useState(0);
  const { data: sentiment = [], loading } = useRequest(
    async () => {
      const res = await getMarketSentiment();
      return res;
    },
    {
      ready: !!ready,
      refreshDeps: [i18n.language],
    },
  );

  const SentimentIndicatorsData3 = useMemo(() => {
    const item = sentiment?.[selectedIndex];

    if (!item) return {};
    if (item.market === Market.CRYPTO) {
      return {
        title: t('crypto.alt_coin_season'),
        descAry: [
          t('crypto.alt_coin_season_tips1'),
          t('crypto.alt_coin_season_tips2'),
          t('crypto.alt_coin_season_tips3'),
          t('crypto.alt_coin_season_tips4'),
          t('crypto.alt_coin_season_tips5'),
        ],
        value: Number(item?.alt_coin_season || 0),
        showValue: Number(item?.alt_coin_season || 0),
        labels: [
          t('crypto.alt_coin_season_labels1'),
          t('crypto.alt_coin_season_labels2'),
          t('crypto.alt_coin_season_labels3'),
          t('crypto.alt_coin_season_labels4'),
          t('crypto.alt_coin_season_labels5'),
        ],
      };
    } else if (item.market === Market.US) {
      return {
        title: t('us_market.market_sentiment'),
        descAry: [
          t('us_market.market_sentiment_tips1'),
          t('us_market.market_sentiment_tips2'),
          t('us_market.market_sentiment_tips3'),
          t('us_market.market_sentiment_tips4'),
          t('us_market.market_sentiment_tips5'),
        ],
        value: Number(item?.market_sentiment || 0) * 20,
        showValue: Number(item?.market_sentiment || 0),
        labels: [
          t('us_market.market_sentiment_labels1'),
          t('us_market.market_sentiment_labels2'),
          t('us_market.market_sentiment_labels3'),
          t('us_market.market_sentiment_labels4'),
          t('us_market.market_sentiment_labels5'),
        ],
      };
    } else if (item.market === Market.HK) {
      return {
        title: t('hk_market.technical_rating'),
        descAry: [
          t('hk_market.technical_rating_tips1'),
          t('hk_market.technical_rating_tips2'),
          t('hk_market.technical_rating_tips3'),
          t('hk_market.technical_rating_tips4'),
          t('hk_market.technical_rating_tips5'),
        ],
        value: Number(item?.technical_rating) * 20,
        showValue: Number(item?.technical_rating || 0),
        labels: [
          t('hk_market.technical_rating_labels1'),
          t('hk_market.technical_rating_labels2'),
          t('hk_market.technical_rating_labels3'),
          t('hk_market.technical_rating_labels4'),
          t('hk_market.technical_rating_labels5'),
        ],
      };
    }
  }, [sentiment, selectedIndex, t]);

  const SentimentIndicatorsList = useMemo(() => {
    const item = sentiment?.[selectedIndex];
    if (!item) return [];
    return [
      {
        title: item.market === Market.CRYPTO ? t('crypto.binance_indications') : t('stock.vix_index'),
        descAry:
          item.market === Market.CRYPTO
            ? [
                t('crypto.binance_indications_tips1'),
                t('crypto.binance_indications_tips2'),
                t('crypto.binance_indications_tips3'),
                t('crypto.binance_indications_tips4'),
                t('crypto.binance_indications_tips5'),
              ]
            : [
                t('stock.vix_index_tips1'),
                t('stock.vix_index_tips2'),
                t('stock.vix_index_tips3'),
                t('stock.vix_index_tips4'),
                t('stock.vix_index_tips5'),
              ],
        value: item.market === Market.CRYPTO ? formatLongShortRatio(Number(item?.long_short_ratio || 0)) : formatViX(Number(item?.vix_index || 0)),
        showValue: item.market === Market.CRYPTO ? Number(item?.long_short_ratio || 0) : Number(item?.vix_index || 0),
        labels:
          item.market === Market.CRYPTO
            ? [
                t('crypto.binance_indications_labels1'),
                t('crypto.binance_indications_labels2'),
                t('crypto.binance_indications_labels3'),
                t('crypto.binance_indications_labels4'),
                t('crypto.binance_indications_labels5'),
              ]
            : [
                t('stock.vix_index_labels1'),
                t('stock.vix_index_labels2'),
                t('stock.vix_index_labels3'),
                t('stock.vix_index_labels4'),
                t('stock.vix_index_labels5'),
              ],
      },
      {
        title: item.market === Market.CRYPTO ? t('crypto.fear_greed_index') : t('stock.pc_ratio'),
        descAry:
          item.market === Market.CRYPTO
            ? [
                t('crypto.fear_greed_index_tips1'),
                t('crypto.fear_greed_index_tips2'),
                t('crypto.fear_greed_index_tips3'),
                t('crypto.fear_greed_index_tips4'),
                t('crypto.fear_greed_index_tips5'),
              ]
            : [
                t('stock.pc_ratio_tips1'),
                t('stock.pc_ratio_tips2'),
                t('stock.pc_ratio_tips3'),
                t('stock.pc_ratio_tips4'),
                t('stock.pc_ratio_tips5'),
              ],
        value: item.market === Market.CRYPTO ? item?.fear_greed_index : formatPCRatio(Number(item?.pc_ratio || 0)),
        showValue: item.market === Market.CRYPTO ? Number(item?.fear_greed_index || 0) : Number(item?.pc_ratio || 0),
        labels:
          item.market === Market.CRYPTO
            ? [
                t('crypto.fear_greed_index_labels1'),
                t('crypto.fear_greed_index_labels2'),
                t('crypto.fear_greed_index_labels3'),
                t('crypto.fear_greed_index_labels4'),
                t('crypto.fear_greed_index_labels5'),
              ]
            : [
                t('stock.pc_ratio_labels1'),
                t('stock.pc_ratio_labels2'),
                t('stock.pc_ratio_labels3'),
                t('stock.pc_ratio_labels4'),
                t('stock.pc_ratio_labels5'),
              ],
      },
      SentimentIndicatorsData3,
    ];
  }, [sentiment, selectedIndex, SentimentIndicatorsData3, t]);
  return (
    <VStack className="flex gap-[6px] p-[16px] flex-col justify-between items-start shrink-0 rounded-[12px] border-[1px] border-solid border-[#F5F5F5] bg-[#FFF]">
      <Text className="text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[700] leading-[20px]">
        {t('home.market.sentiment_indicators')}
      </Text>
      <HStack className="w-full gap-3">
        <Grid
          className="flex-1 gap-4"
          _extra={{
            className: 'grid-cols-12',
          }}
        >
          {loading ? (
            <>
              {Array.from({ length: 6 }).map((_, index) => (
                <GridItem
                  _extra={{
                    className: 'col-span-12 md:col-span-6',
                  }}
                  key={`skeleton-${index}`}
                >
                  <SentimentIndicatorSkeleton key={`skeleton-${index}`} />
                </GridItem>
              ))}
            </>
          ) : (
            <>
              {sentiment?.length > 0 ? (
                <>
                  {sentiment?.map((item, index) => (
                    <GridItem
                      _extra={{
                        className: 'col-span-12 md:col-span-6',
                      }}
                      key={`${item.market}-${item.symbol}`}
                    >
                      <EponentCard
                        key={index}
                        item={item}
                        selected={selectedIndex === index}
                        onPress={() => setSelectedIndex(index)}
                      />
                    </GridItem>
                  ))}
                </>
              ) : (
                <GridItem
                  _extra={{
                    className: 'col-span-12',
                  }}
                >
                  <Empty content={t('empty')} />
                </GridItem>
              )}
            </>
          )}
        </Grid>

        {SentimentIndicatorsList.length > 0 && (
          <VStack className="flex-1 max-w-[170px] gap-5">
            {SentimentIndicatorsList.map((item: any, index) => (
              <SentimentIndicator
                key={index}
                title={item.title}
                descAry={item.descAry}
                value={Number(item.value || 0)}
                showValue={item.showValue}
                labels={item.labels}
              />
            ))}
          </VStack>
        )}
      </HStack>
    </VStack>
  );
}
