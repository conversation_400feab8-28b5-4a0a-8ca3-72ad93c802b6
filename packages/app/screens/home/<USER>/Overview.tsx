'use client';
import { HStack } from '@quantum/components/ui/hstack';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import ChangeValue from '../../../components/ChangeValue';
import { getMarketOverview } from '../../../services/asset/asset.api';
import { averageNumber } from '../../../uitls/number';
import type { MarketsOverview } from '../../../services/asset/asset.types';
import { Trans, useTranslation } from 'react-i18next';
import ScrollingNotificationBanner from '../../../components/ScrollingNotificationBanner/ScrollingNotificationBanner';
import { useRequest } from 'ahooks';

export default function Overview({ data }: { data: MarketsOverview | null }) {
  const { t } = useTranslation();
  const {data: overviewData} = useRequest(getMarketOverview);
  const showData = overviewData || data;
  return (
    <VStack className="gap-1">
      <Text className="text-[#0A0A0A] font-Inter text-[16px] font-not-italic font-[600] leading-[20px]">
        {t('home.global_market_overview')}
      </Text>

      <ScrollingNotificationBanner
        data={[
          {
            content: (
              <HStack className="text-[#808080] font-Inter text-[12px] font-not-italic font-[600] leading-[16px] items-center">
                <Trans
                  i18nKey="home.global_market_overview_desc"
                  components={{
                    Text: <Text className="text-[#4d4d4d] whitespace-normal" />,
                    ChangeValue: <ChangeValue change={`${showData?.change_rate}%`} space={0} />,
                  }}
                  values={{
                    market_cap: averageNumber(Number(showData?.market_cap), 2),
                  }}
                />
              </HStack>
            ),
          },
          {
            content: (
              <HStack className="text-[#808080] font-Inter text-[12px] font-not-italic font-[600] leading-[16px] items-center">
                <Trans
                  i18nKey="home.global_market_overview_desc"
                  components={{
                    Text: <Text className="text-[#4d4d4d] whitespace-normal" />,
                    ChangeValue: <ChangeValue change={`${showData?.change_rate}%`} space={0} />,
                  }}
                  values={{
                    market_cap: averageNumber(Number(showData?.market_cap), 2),
                  }}
                />
              </HStack>
            ),
          },
          {
            content: (
              <HStack className="item-center text-[#808080] font-Inter text-[12px] font-not-italic font-[600] leading-[16px] items-center">
                <Trans
                  i18nKey="home.global_market_overview_desc"
                  components={{
                    Text: <Text className="text-[#4d4d4d] whitespace-normal" />,
                    ChangeValue: <ChangeValue change={`${showData?.change_rate}%`} space={0} />,
                  }}
                  values={{
                    market_cap: averageNumber(Number(showData?.market_cap), 2),
                  }}
                />
              </HStack>
            ),
          },
        ]}
      />
    </VStack>
  );
}
