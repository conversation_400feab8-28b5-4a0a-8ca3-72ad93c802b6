'use client';
import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import { Text } from '@quantum/components/ui/text';
import LineGraph from '../../../components/LineGraph/LineGraph';
import { VStack } from '@quantum/components/ui/vstack';
import Image from '@unitools/image';
import type { MarketsSentimentItem } from '../../../services/asset/asset.types';
import { Pressable } from '@quantum/components/ui/pressable';
import TextOrImageLogo from '../../../components/TextOrImageLogo';
import { averageNumber } from '../../../uitls/number';
import { useRequest } from 'ahooks';
import { getKline } from '../../../services/kline/kline.api';
import { AliKlineInterval } from '../../../services/kline/kline.types';
interface ExponentCardProps {
  item: MarketsSentimentItem;
  selected: boolean;
  onPress: () => void;
}

export default function ExponentCard({ item, selected, onPress }: ExponentCardProps) {
  const {data = []} = useRequest(async () => {
    const res = await getKline(item.market, item.symbol, AliKlineInterval.DAY_1, 7);
    return res;
  }, {
    refreshDeps: [item.market, item.symbol],
    cacheKey: `kline-${item.market}-${item.symbol}-day-7`,
    staleTime: 600 * 1000, // 10分钟缓存
  });

  return (
    <Pressable onPress={onPress} className='h-full w-full'>
      <VStack
        className={`gap-[11px] h-full p-3 rounded-[12px] border-[1px] border-solid border-[#F5F5F5] bg-[#FFF] ${
          selected && ' shadow-[0px_2px_4px_0px_rgba(0,0,0,0.05),0px_5px_16px_0px_rgba(0,0,0,0.15)]'
        }`}
      >
        <HStack className="gap-[6px] items-center">
          <TextOrImageLogo text={String(item.symbol)} size={20} />
          <Text className="text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[500] leading-[20px]">
            {item.name}
          </Text>
        </HStack>
        <HStack className="gap-2 items-center">
          <VStack className="flex-1 gap-1">
            <Text className="text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[700] leading-[20px]">
              {averageNumber(item.price, 2)}
            </Text>
            <Text
              className={`text-[14px] font-not-italic font-[600] leading-[20px] ${
                Number(item.change_rate) > 0 ? 'text-[#00AD6B]' : 'text-[#FF0000]'
              }`}
            >
              {Number(item.change_rate) > 0 ? '+' : ''}
              {item.change_rate}%
            </Text>
          </VStack>
          <Box className="w-[56px] h-[44px]">
            <LineGraph isInteractive={false} data={data}/>
          </Box>
        </HStack>
      </VStack>
    </Pressable>
  );
}
