import Comments from './Comments';
import { Market } from '../../../services/asset/asset.types';
import { SocialMediaSort } from '../../../services/media/media.types';
import { getSocialMedias } from '../../../services/media/media.api';
async function fetchPageData(market?: Market, symbol?: string) {
  try {
    const res = await getSocialMedias(SocialMediaSort.NEWEST, 10, undefined, market, symbol);
    return res.items;
  } catch (error) {
    return [];
  }
}

export default async function CommentsIndex({ market, symbol }: { market?: Market; symbol?: string }) {
  const data = await fetchPageData(market, symbol);
  return <Comments market={market} symbol={symbol} defaultData={data} />;
}
