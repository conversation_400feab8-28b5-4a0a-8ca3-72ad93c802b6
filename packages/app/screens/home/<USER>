import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import ScrollMessageBar from '../../components/ScrollMessageBar';
import Comments from './Comments';
import HotMarketRank from './HotMarketRank/HotMarketRank';
import Overview from './Overview/Overview';
import SentimentIndicators from './SentimentIndicators/SentimentIndicators';
import WatchList from './WatchList/WatchList';
import type { MarketsOverview } from '../../services/asset/asset.types';
import ScrollingNotificationBanner from '../../components/ScrollingNotificationBanner/ScrollingNotificationBanner';

const HomePage = ({ overviewData }: { overviewData: MarketsOverview | null }) => {
  return (
    <>
      <ScrollMessageBar key="topbar" />
      <VStack className="flex-1 p-4 md:p-6 gap-4">
        <Overview data={overviewData} />
        <Box className="flex flex-col lg:flex-row gap-4">
          <Box className="w-full lg:flex-1 h-[420px] md:p-4 md:pr-0 rounded-[12px] border-[1px] border-solid border-[#F5F5F5] bg-[#FFF]">
            <HotMarketRank />
          </Box>
          <Box className="w-full lg:w-[400px] xl:w-[620px]">
            <SentimentIndicators />
          </Box>
        </Box>

        <HStack className="flex-1 lg:flex-row flex-col-reverse gap-6">
          <Box className="flex-1">
            <Comments />
          </Box>
          <Box className="w-full lg:w-[329px]">
            <Box className="p-[16px] flex-col items-start gap-[12px] rounded-[12px] border-[1px] border-solid border-[#F5F5F5] bg-[#FFF] lg:sticky lg:top-[94px] lg:h-[calc(100vh-94px)] mb-4 lg:mb-0">
              <WatchList />
            </Box>
          </Box>
        </HStack>
      </VStack>
    </>
  );
};

export default HomePage;
