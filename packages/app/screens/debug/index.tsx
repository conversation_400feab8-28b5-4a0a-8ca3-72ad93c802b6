import React from 'react';
import { Button, ButtonText } from '@quantum/components/ui/button';
import useRouter from '@unitools/router';
import { SafeAreaView } from '@quantum/components/ui/safe-area-view';
import { VStack } from '@quantum/components/ui/vstack';
import { View } from 'react-native';
import { useTheme } from '@quantum/components/ui/ThemeProvider/ThemeProvider';
import { Text } from 'react-native';
const index = () => {
  const router = useRouter();
  const { theme, toggleTheme, mode } = useTheme();
  return (
    <SafeAreaView className="md:flex flex-col items-center justify-center md:w-full h-full bg-red-500 dark:bg-blue-500">
      <VStack className="p-2 md:max-w-[440px] w-full" space="xl">
        <Button
          className="w-full"
          onPress={() => {
            toggleTheme();
          }}
        >
          <ButtonText>{mode}</ButtonText>
        </Button>
        <Text>{theme}</Text>
        <Text className="text-blue-500 dark:text-orange-500">1242</Text>
        <View className="h-10 w-10 bg-orange-500 dark:bg-green-500"></View>
        <Button
          className="w-full"
          onPress={() => {
            router.push('auth/signin');
          }}
        >
          <ButtonText>Sign in12</ButtonText>
        </Button>

        <Button
          onPress={() => {
            router.push('profile/profile');
          }}
        >
          <ButtonText>Profile</ButtonText>
        </Button>
        <Button
          onPress={() => {
            router.push('/echart');
          }}
        >
          <ButtonText>echart</ButtonText>
        </Button>
      </VStack>
    </SafeAreaView>
  );
};

export default index;
