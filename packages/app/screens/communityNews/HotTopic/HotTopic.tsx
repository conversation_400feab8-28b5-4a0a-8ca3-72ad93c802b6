import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { Divider } from '@quantum/components/ui/divider';
import { ScrollView } from '@quantum/components/ui/scroll-view';
import Empty from '../../../components/Empty/Empty';
import { HStack } from '@quantum/components/ui/hstack';
import { averageNumber } from '../../../uitls/number';
import { useRequest } from 'ahooks';
import { getHotSearches } from '../../../services/asset/asset.api';
import type { TrendingItem } from '../../../services/asset/asset.types';

function HotTopicItem({ item, index }: { item: TrendingItem; index: number }) {
  return (
    <HStack className="w-full h-[62px] items-center gap-8">
      <Box className="w-[32px] h-[32px] items-center justify-center rounded-[6px] bg-[#F7F7F7]">
        <Text className="text-[#4D4D4D]">{index + 1}</Text>
      </Box>
      <VStack className="w-[159px] gap-[2px]">
        <Text className="text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[700] leading-[20px]">
            {item.symbol}
        </Text>
        <Text className="text-[#808080] font-Inter text-[12px] font-not-italic font-[400] leading-[16px]">
          24小时热度:{averageNumber(Number(item.volume))}
        </Text>
      </VStack>
      <Box className="flex-1">
        <Text className={`${Number(item.priceChangePercentage) > 0 ? 'text-[#FF0000]' : 'text-[#008000]'}`}>
          {Number(item.priceChangePercentage) > 0 ? `+${item.priceChangePercentage}%` : `${item.priceChangePercentage}%`}
        </Text>
      </Box>
    </HStack>
  );
}

export default function HotTopic() {
  const {data: list = [], loading} = useRequest(async () => {
    const res = await getHotSearches();
    return res || [];
  });
  
  return (
    <VStack className="w-full h-full min-h-[386px] gap-3">
      <Text className="text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[700] leading-[20px] tracking--0.56px">
        热门话题
      </Text>
      <Divider className="bg-[#f5f5f5]" />

      <ScrollView className="w-full flex-1 overflow-auto">
        {list.length > 0 ? (
          <VStack className=" ">
            {list.map((item, index) => (
              <HotTopicItem key={item.market + item.symbol} item={item} index={index} />
            ))}
          </VStack>
        ) : (
          <Empty className="mt-[74px]" content="暂无观察列表" />
        )}
      </ScrollView>
    </VStack>
  );
}
