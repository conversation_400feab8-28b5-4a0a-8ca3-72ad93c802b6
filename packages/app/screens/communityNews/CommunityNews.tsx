'use client';
import { View } from 'react-native';
import { Text } from '@quantum/components/ui/text';
import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import Comments from '../home/<USER>/Comments';
import WatchList from '../home/<USER>/WatchList';
import { VStack } from '@quantum/components/ui/vstack';
import HotTopic from './HotTopic/HotTopic';
import RecommendKOL from './RecommendKOL/RecommendKOL';
import { useTranslation } from 'react-i18next';
import { Button, ButtonText } from '@quantum/components/ui/button';
import { Image } from '@quantum/components/ui/image';
import { useMemo, useState } from 'react';
import { Pressable } from '@quantum/components/ui/pressable';
import { useToast } from '@quantum/components/ui/toast';
import { useCustomToast } from '../../components/Toast/ToastProvider';
import { useRequest } from 'ahooks';
import { observer } from 'mobx-react-lite';
import { Link } from '@quantum/components/ui/link';
import CopyToClipboard from 'react-copy-to-clipboard';
import { TwitterShareButton } from 'react-share';
import { RiTwitterXFill } from 'react-icons/ri';
import { userStore } from '../../store/user.store';
import { PiCopyLight } from 'react-icons/pi';
import { authStore } from '../../store/auth.store';
import { getWaitlistInviteList, joinWaitlist } from '../../services/waitlist/waitlist.api';
import { averageNumber } from '../../uitls/number';
import { waitlistStore } from '../../store/waitlist.store';
import { followKol } from '../../services/media/media.api';
import { SocialPlatform } from '../../services/media/media.types';
import { watchlistStore } from '../../store/watchlist.store';

function JoinPremium() {
  const { showToast } = useCustomToast();

  const { loading: waitlistLoading } = useRequest(
    async () => {
      if (!authStore.isLoggedIn) {
        waitlistStore.waitlistStatus.setData(null);
        return;
      }
      const res = await waitlistStore.waitlistStatus.fetch();
      return res;
    },
    {
      refreshDeps: [authStore.isLoggedIn, authStore.activeUserId],
    },
  );

  const { run, loading } = useRequest(
    async () => {
      if (!authStore.isLoggedIn) {
        authStore.setShowLoginModal(true);
        return;
      }
      try {
        const res = await joinWaitlist();
        await waitlistStore.waitlistStatus.reload();
      } catch (error: any) {
        console.log(error);
        showToast({
          title: t('failed'),
          description: error.msg || t('join_failed'),
          status: 'error',
        });
      }
    },
    {
      manual: true,
    },
  );
  const { t } = useTranslation();
  return (
    <HStack className="w-full px-[24px] py-[25px] items-center justify-between relative rounded-[16px] overflow-hidden">
      <Image
        source={{
          uri: 'https://static.stockbits.ai/o/bg-join-waitlist-3toh8v.png',
        }}
        className="absolute top-0 left-0 w-full h-full"
      />
      <VStack className="gap-[6px]">
        <Text className="text-[#000] text-[18px] font-not-italic font-600 leading-[24px]">
          {t('waitlist.right_content.join_waitlist_title')}
          </Text>
        <Text className="text-[#4D4D4D] text-[14px] font-not-italic font-400 leading-[20px]">
          {t('waitlist.right_content.join_waitlist_description')}
        </Text>
      </VStack>

      {waitlistStore.waitlistStatus.data?.result ? (
        <Button
          className="px-[16px] py-[8px] justify-center items-center gap-[4px] rounded-[60px] bg-[#ccc] data-[disabled]:bg-[#ccc] data-[hover=true]:bg-[#ccc]"
          disabled={true}
        >
          <Text className="text-[#FFFFFF] text-[16px]  font-[700] leading-[21px]">
            {t('waitlist.home_page.premium_free_waitlist_button_joined')}
          </Text>
        </Button>
      ) : (
        <>
          {!waitlistLoading && (
            <Button
              onPress={() => {
                run();
              }}
              loading={loading}
              className="px-[16px] py-[8px] justify-center items-center gap-[4px] rounded-[60px] bg-gradient-to-r from-[#7CFDFF] to-[#6AFF50]"
            >
              <Text className="text-[#0A0A0A] text-[14px] font-not-italic font-500 leading-[20px]">
                {t('waitlist.right_content.join_waitlist_button_text')}
              </Text>
            </Button>
          )}
        </>
      )}
    </HStack>
  );
}
const JoinPremiumComp = observer(JoinPremium);

function AddFavoriteKol() {
  const [selected, setSelected] = useState<SocialPlatform>(SocialPlatform.Twitter);
  const tabs = [
    {
      value: SocialPlatform.Twitter,
      label: 'X',
    },
    {
      value: SocialPlatform.TradingView,
      label: 'Trading view',
    },
  ];
  const [link, setLink] = useState('');
  const { showToast } = useCustomToast();
  const { run, loading } = useRequest(
    async () => {
      if (!authStore.isLoggedIn) {
        authStore.setShowLoginModal(true);
        return;
      }
      try {
        await followKol({
          platform: selected,
          link: link,
        });
        showToast({
          title: t('waitlist.right_content.collect_success'),
          description: '',
          status: 'success',
        });

        setLink('');
      } catch (error: any) {
        showToast({
          title: 'Error',
          description: error?.msg,
          status: 'error',
        });
      } finally {
        watchlistStore.followingKol.reload();
      }
    },
    {
      manual: true,
    },
  );
  const { t } = useTranslation();
  return (
    <VStack className="p-[24px] gap-[12px] rounded-[12px] border-[2px] border-solid border-[#F5B800] bg-gradient-to-r from-[#FFFDF7] to-[#FFFAEC] relative">
      <Image
        source={{
          uri: 'https://static.stockbits.ai/o/icon-crown-ygndbd.png',
        }}
        className="w-[120px] h-[120px] absolute top-[-40px] right-[6px]"
      />
      <HStack className="gap-2 items-center">
        <Text className="text-[#0A0A0A] font-[Inter] text-[14px] font-not-italic font-700 leading-[20px]">
          {t('waitlist.right_content.join_waitlist_title')}
        </Text>
        <HStack className="px-[6px] py-[2px] justify-center items-center gap-[4px] rounded-[22px]  bg-gradient-to-r from-[#FFCC2E] to-[#FFE69A]">
          <Text className="text-[12px] font-not-italic font-500 leading-[16px] bg-gradient-to-r from-[#0A0A0A] to-[#5C5C5C] bg-clip-text text-transparent">
            {t('waitlist.right_content.premium_user')}
          </Text>
        </HStack>
      </HStack>

      <HStack className="p-[4px] items-start gap-[2px] self-stretch rounded-[8px] bg-[#FFF6D9] backdrop-blur-[2px]">
        {tabs.map((item, index) => {
          return (
            <Pressable
              className="flex-1 h-7"
              key={index}
              onPress={() => {
                setSelected(item.value);
              }}
            >
              <HStack
                className={`h-full w-full rounded-[4px] items-center justify-center ${
                  selected === item.value ? 'bg-[#fff]' : ''
                }`}
              >
                <Text
                  className={`text-[#808080] text-center text-[12px] font-not-italic font-400 leading-[16px] ${
                    selected === item.value ? 'text-[#0A0A0A] font-500' : ''
                  }`}
                >
                  {item.label}
                </Text>
              </HStack>
            </Pressable>
          );
        })}
      </HStack>

      <VStack className="gap-[6px]">
        <Text className="text-[#0A0A0A] font-[Poppins] text-[14px] font-not-italic font-500 leading-[20px]">
          {t('waitlist.right_content.kol_link')}
        </Text>
        <HStack className="h-[48px] px-[12px] items-center gap-2 rounded-[8px] bg-[#FFF6D9]">
          <Box className="flex-1 h-full">
            <input
              type="text"
              placeholder={t('waitlist.right_content.kol_link_placeholder')}
              className="flex-1 h-full w-full bg-transparent focus:outline-none text-[14px] font-not-italic font-400 leading-[20px]"
              value={link}
              onChange={(e) => setLink(e.target.value)}
            />
          </Box>
          <Button
            loading={loading}
            onPress={() => {
              run();
            }}
            className="w-min-[60px] h-[28px] px-[12px] py-[6px] justify-center items-center gap-[4px] rounded-[60px] bg-[#FFC30A] data-[hover=true]:bg-[#FFC30A]"
          >
            <Text className='text-[#0A0A0A] font-["PingFang_SC"] text-[12px] font-not-italic font-500 leading-[16px]'>
              {t('waitlist.right_content.collect')}
            </Text>
          </Button>
        </HStack>
        <Text className="text-[#808080] font-[Poppins] text-[14px] font-not-italic font-400 leading-[20px]">
          {t('waitlist.right_content.premium_user_max_follow')}
        </Text>
      </VStack>
    </VStack>
  );
}

const AddFavoriteKolComp = observer(AddFavoriteKol);

function WaitListData() {
  const { t } = useTranslation();

  const inviteUrl = useMemo(() => {
    return `${window.location.origin}/waitlist?inviteCode=${userStore.currentUserInfo?.invite_code || ''}`;
  }, [userStore.currentUserInfo]);

  const { showToast } = useCustomToast();
  const handleCopy = () => {
    showToast({
      title: t('copy_link_success'),
      description: t('copy_link_success_description'),
      status: 'success',
    });
  };

  const { data: list = [] } = useRequest(
    async () => {
      if (!authStore.isLoggedIn) {
        return [];
      }
      const res = await getWaitlistInviteList();
      return res;
    },
    {
      refreshDeps: [authStore.currentToken, authStore.isLoggedIn],
    },
  );

  if (!authStore.isLoggedIn) {
    return null;
  }

  return (
    <VStack className="p-[24px] items-center gap-[24px] self-stretch rounded-[12px] border-[2px] border-solid border-[#F5F5F5] bg-[#FFF]">
      <HStack className="pt-[16px] pb-[16px] pl-[20px] pr-[24px] justify-between items-center self-stretch rounded-[6px] bg-[rgba(5,198,151,0.03)]">
        <HStack className="gap-[10px]">
          <Image
            source={{ uri: 'https://static.stockbits.ai/o/bg-join-waitlist-d0xqm9.png' }}
            className="w-[42px] h-[46px]"
          />
          <VStack className="gap-[4px]">
            <Text className="text-[#0A0A0A] font-[Poppins] text-[16px] font-not-italic font-600 leading-[20px]">
              {averageNumber(list.length)}
            </Text>
            <Text className="text-[#808080] text-center font-[Poppins] text-[12px] font-not-italic font-400 leading-[16px]">
              {t('waitlist.right_content.invited_people')}
            </Text>
          </VStack>
        </HStack>

        <Link href="/waitlist/join">
          <Text className="text-[#808080] text-center text-[12px] font-not-italic font-400 leading-[16px] underline underline-offset-auto">
            {t('waitlist.right_content.view_invite_record')}
          </Text>
        </Link>
      </HStack>

      <VStack className="gap-[6px]">
        <Text className="text-[#141414] font-[Poppins] text-[14px] font-not-italic font-500 leading-[20px]">
          {t('waitlist.right_content.invite_link')}
        </Text>
        <HStack className="flex px-[8px] py-[6px] items-center gap-[6px] self-stretch rounded-[6px] bg-[rgba(5,198,151,0.03)]">
          <Box className="flex-1">
            <Text className="flex-[1_0_0] text-[#808080] font-[Poppins] text-[14px] font-not-italic font-400 leading-[20px]">
              {inviteUrl}
            </Text>
          </Box>

          <CopyToClipboard text={inviteUrl} onCopy={handleCopy}>
            <button className="flex w-[24px] h-[24px] p-[6px] justify-center items-center gap-[10px] rounded-[4px] bg-[#05C697]">
              <PiCopyLight size={12} className="text-[#fff]" />
            </button>
          </CopyToClipboard>
          <TwitterShareButton url={inviteUrl} title={t('waitlist.join_page.invite_your_friends_title_tips')}>
            <Box className="flex w-[24px] h-[24px] p-[6px] justify-center items-center gap-[10px] rounded-[4px] bg-[#05C697]">
              <RiTwitterXFill size={12} className="text-[#fff]" />
            </Box>
          </TwitterShareButton>
        </HStack>
      </VStack>
    </VStack>
  );
}

const WaitListDataComp = observer(WaitListData);

function CommunityNews() {
  const { t } = useTranslation();
  const rightContent = [
    // <HotTopic />,
    <JoinPremiumComp />,
    <Box className="p-[14px] flex-col rounded-[12px] border-[1px] border-solid border-[#F5F5F5] bg-[#FFF]">
      <WatchList height={272} />
    </Box>,
    <AddFavoriteKolComp />,
    <WaitListDataComp />,
  ];
  return (
    <Box>
      <HStack className="flex-1 gap-6">
        <Box className="flex-1">
          <VStack className="gap-4">
            {/* <RecommendKOL /> */}
            <Comments showFavorites={true} />
          </VStack>
        </Box>
        <Box className="w-[448px]">
          <VStack className="gap-6">
            {rightContent.map((item, index) => (
              <Box key={index}>{item}</Box>
            ))}
          </VStack>
        </Box>
      </HStack>
    </Box>
  );
}

export default observer(CommunityNews);
