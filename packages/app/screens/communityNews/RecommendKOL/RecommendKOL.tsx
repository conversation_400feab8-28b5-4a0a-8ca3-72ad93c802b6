import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { Image } from '@quantum/components/ui/image';
import { Text } from '@quantum/components/ui/text';
import { Button } from '@quantum/components/ui/button';

export default function RecommendKOL() {
  const list = [
    {
      name: 'KOL1',
      description: 'KOL1描述KOL1描述KOL1描述KOL1描述KOL1描述KOL1描述KOL1描述',
      avatar: 'https://assets.coingecko.com/coins/images/12559/large/Avalanche_Circle_RedWhite_Trans.png?1696512369',
    },
    {
      name: 'KOL2',
      description: 'KOL2描述',
      avatar: 'https://assets.coingecko.com/coins/images/12559/large/Avalanche_Circle_RedWhite_Trans.png?1696512369',
    },
    {
      name: '<PERSON>OL3',
      description: 'KOL3描述',
      avatar: 'https://assets.coingecko.com/coins/images/12559/large/Avalanche_Circle_RedWhite_Trans.png?1696512369',
    },
  ];
  return (
    <HStack className="gap-4">
      {list.map((item, index) => (
        <HStack key={index} className="flex-1 px-[16px] py-[12px] items-center gap-[10px] rounded-[8px] bg-[#F7F7F7]">
          <Image source={{ uri: item.avatar }} className="w-[36px] h-[36px] rounded-full" />
          <VStack className="flex-1">
            <Text className="overflow-hidden  text-ellipsis whitespace-nowrap text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[600] leading-[20px]">
              {item.name}
            </Text>
            <Text className="overflow-hidden  text-ellipsis whitespace-nowrap text-[#808080] font-Inter text-[12px] font-not-italic font-[400] leading-[16px]">
              {item.description}
            </Text>
          </VStack>
          <Button action="primary" className="w-[68px] h-[28px] rounded-[60px] bg-[rgba(0,0,0,0.08)] data-[hover=true]:bg-[rgba(0,0,0,0.08)] p-0">
            <Text className='text-[#0A0A0A] font-Poppins text-[12px] font-not-italic font-[500] leading-[16px]'>关注</Text>
          </Button>
        </HStack>
      ))}
    </HStack>
  );
}
