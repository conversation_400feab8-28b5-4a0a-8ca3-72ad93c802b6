import { HStack } from '@quantum/components/ui/hstack';
import { Text } from '@quantum/components/ui/text';
import { Pressable } from '@quantum/components/ui/pressable';
import { useEffect, useState } from 'react';
import { Box } from '@quantum/components/ui/box';
export default function TextTabs({
  tabs,
  onPress,
  activeTab,
}: {
  tabs: {
    value: string;
    label: string;
  }[];
  onPress: (tab: string) => void;
  activeTab: string;
}) {
  return (
    <HStack className="gap-6 min-h-[52px] items-center relative">
      {tabs.map((tab) => (
        <Pressable key={tab.value} className="h-[52px]" onPress={() => onPress(tab.value)}>
          <HStack className='relative h-full items-center'>
            <Text
              className={`text-[#808080] font-[Poppins] text-[18px] font-not-italic font-500 leading-[24px] ${
                activeTab === tab.value ? 'text-[#0A0A0A] font-[600]' : 'text-[#808080] font-[500]'
              }`}
            >
              {tab.label}
            </Text>
            <Box className={`absolute bottom-0 left-0 w-full h-[2px] bg-[#0A0A0A] transition-all duration-300 ${activeTab === tab.value ? 'opacity-100' : 'opacity-0'}`} />
          </HStack>
        </Pressable>
      ))}
    </HStack>
  );
}
