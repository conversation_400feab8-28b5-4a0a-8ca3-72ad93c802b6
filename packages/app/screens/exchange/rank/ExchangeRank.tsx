'use client';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import ScrollMessageBar from '../../../components/ScrollMessageBar';
import { VStack } from '@quantum/components/ui/vstack';
import { HStack } from '@quantum/components/ui/hstack';
import TextTabs from './components/TextTabs';
import { useEffect, useMemo, useState } from 'react';
import SelectUI from '../../../components/SelectUI/SelectUI';
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableData,
  TableFooter,
} from '@quantum/components/ui/table';
import TableUi, { type TableColumn } from '../../../components/TableUi/TableUi';
import Image from '@unitools/image';
import { averageNumber } from '../../../uitls/number';
import { Button, ButtonText } from '@quantum/components/ui/button';
import { RiContractRightLine } from 'react-icons/ri';
import { useRequest } from 'ahooks';
import { getExchangeList } from '../../../services/crypto_exchange/crypto_exchange.api';
import type { ExchangeOverview } from '../../../services/crypto_exchange/crypto_exchange.types';
import Link from '@unitools/link';
import { useTranslation } from 'react-i18next';
import React from 'react';
import { Grid, GridItem } from '@quantum/components/ui/grid';
import { useSearchParams } from '@unitools/navigation';
import useRouter from '@unitools/router';
import { quickSortListCfg } from './config';
import { isNil } from 'lodash';

function TableHeaderRender({ children }: { children: React.ReactNode }) {
  return (
    <Text className="text-[#4D4D4D] text-center text-[14px] font-not-italic font-400 leading-[20px]">{children}</Text>
  );
}



export default function ExchangeRank({ exchangeList: defaultExchangeList = [] }: { exchangeList: ExchangeOverview[] }) {
  const query = useSearchParams();
  const defaultSortKey = query?.get('sort');
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('spot');
  const [selectedLegalTender, setSelectedLegalTender] = useState('');
  const tabs = [
    {
      value: 'spot',
      label: t('exchange_page.rank.tabs.spot'),
    },
    // {
    //   value: 'dex',
    //   label: 'DEX',
    // },
    // {
    //   value: 'wallet',
    //   label: 'Wallet',
    // },
  ];

  const { data: exchangeList = defaultExchangeList, loading } = useRequest(
    async () => {
      const res = await getExchangeList();
      return res;
    },
    {
      ready: !defaultExchangeList || !defaultExchangeList.length,
    },
  );

  const legalTender = useMemo(() => {
    // 提取所有交易所的fiats字段（数组），合并为一个数组，去重，返回法币列表，第一个元素插入"全部"
    const fiatSet = new Set<string>();
    exchangeList.forEach((item: any) => {
      if (Array.isArray(item.fiats)) {
        item.fiats.forEach((fiat: string) => {
          fiatSet.add(fiat);
        });
      }
    });
    const fiatArr = Array.from(fiatSet);
    const legalTender = [
      {
        value: '',
        label: t('exchange_page.rank.legal_tender.all'),
        icon: '', // "全部"不需要图标
      },
      ...fiatArr.map((fiat) => ({
        value: fiat,
        label: fiat,
        icon: `https://cdn.jsdelivr.net/gh/atomiclabs/cryptocurrency-icons@0a2156e2/128/color/${fiat}.png`,
      })),
    ];
    return legalTender;
  }, [exchangeList, t]);

  // ExchangeOverview 的 key 都可以作为排序字段，这里用联合类型约束
  const [selectedSort, setSelectedSort] = useState<keyof ExchangeOverview | null>(
    (defaultSortKey as keyof ExchangeOverview) || null,
  );
  const [sortValue, setSortValue] = useState<'asc' | 'desc' | null>(defaultSortKey ? 'desc' : null);
  const [currentPage, setCurrentPage] = useState(1);
  useEffect(() => {
    setSelectedSort((defaultSortKey as keyof ExchangeOverview) || null);
    setSortValue(defaultSortKey ? 'desc' : null);
    setCurrentPage(1);
  }, [defaultSortKey]);
  const data = useMemo(() => {
    let result = [...exchangeList];
    if (selectedLegalTender !== '') {
      result = result.filter((item: any) => item.fiats.includes(selectedLegalTender));
    }
    if (selectedSort) {
      if (!!sortValue) {
        result = result.sort((a: any, b: any) => {
          if (sortValue === 'asc') {
            return Number(a[selectedSort]) - Number(b[selectedSort]);
          } else {
            return Number(b[selectedSort]) - Number(a[selectedSort]);
          }
        });
      }
    }
    return result;
  }, [exchangeList, selectedLegalTender, selectedSort, sortValue]);

  const columns: TableColumn[] = useMemo(() => {
    return [
      {
        key: 'no',
        title: <TableHeaderRender>{t('exchange_page.rank.table.no')}</TableHeaderRender>,
        align: 'left',
        render: (value: string, row: any, index: number) => {
          return <Text className="text-[#0A0A0A] text-[14px] font-600 leading-[20px]">{index + 1}</Text>;
        },
        enablePress: true,
      },
      {
        key: 'name',
        title: <TableHeaderRender>{t('exchange_page.rank.table.exchange')}</TableHeaderRender>,
        align: 'left',
        sortKey: 'name',
        render: (value: string, row: any) => {
          return (
            <HStack className="gap-[6px] h-full items-center">
              <Image source={row.logo} alt={value} width={24} height={24} />
              <Text className="text-[#0A0A0A] text-[14px] font-600 leading-[20px]">{value}</Text>
            </HStack>
          );
        },
        enablePress: true,
      },

      {
        key: 'spot_volume_usd',
        title: <TableHeaderRender>{t('exchange_page.rank.table.volume_24h')}</TableHeaderRender>,
        align: 'center',
        sortKey: 'spot_volume_usd',
        render: (value: string, row: any) => {
          if (isNil(value)) {
            return <Text className="text-[#4D4D4D] text-[14px] font-500 leading-[20px]">--</Text>;
          }
          return <Text className="text-[#4D4D4D] text-[14px] font-500 leading-[20px]">${value ? averageNumber(value) : '--'}</Text>;
        },
        enablePress: true,
      },
      {
        key: 'btc_balance',
        title: <TableHeaderRender>{t('exchange_page.rank.table.btc_balance')}</TableHeaderRender>,
        align: 'center',
        sortKey: 'btc_balance',
        render: (value: string, row: any) => {
          if (isNil(value)) {
            return <Text className="text-[#4D4D4D] text-[14px] font-500 leading-[20px]">--</Text>;
          }
          return (
            <Text className="text-[#4D4D4D] text-[14px] font-500 leading-[20px]">{averageNumber(value)} BTC</Text>
          );
        },
        enablePress: true,
      },
      {
        key: 'symbol_amount',
        title: <TableHeaderRender>{t('exchange_page.rank.table.pair_count')}</TableHeaderRender>,
        align: 'center',
        sortKey: 'symbol_amount',
        render: (value: string, row: any) => {
          return <Text className="text-[#4D4D4D] text-[14px] font-500 leading-[20px]">{value ? averageNumber(value) : '--'}</Text>;
        },
        enablePress: true,
      },
      {
        key: 'weekly_visits',
        title: <TableHeaderRender>{t('exchange_page.rank.table.weekly_visits')}</TableHeaderRender>,
        align: 'center',
        sortKey: 'weekly_visits',
        render: (value: string, row: any) => {
          return <Text className="text-[#4D4D4D] text-[14px] font-500 leading-[20px]">{value ? averageNumber(value) : '--'}</Text>;
        },
        enablePress: true,
      },
      {
        key: 'maker_fee',
        title: <TableHeaderRender>{t('exchange_page.rank.table.maker_fee')}</TableHeaderRender>,
        align: 'center',
        sortKey: 'maker_fee',
        render: (value: string, row: any) => {
          if (isNil(value)) {
            return <Text className="text-[#4D4D4D] text-[14px] font-500 leading-[20px]">--</Text>;
          }
          return (
            <Text className="text-[#4D4D4D] text-[14px] font-500 leading-[20px]">
              {averageNumber(Number(value), 2)}%
            </Text>
          );
        },
        enablePress: true,
      },
      {
        key: 'trust_score',
        title: <TableHeaderRender>{t('exchange_page.rank.table.trust_score')}</TableHeaderRender>,
        align: 'center',
        sortKey: 'trust_score',
        render: (value: string | undefined, row: any) => {
          if (isNil(value)) {
            return <Text className="text-[#4D4D4D] text-[14px] font-500 leading-[20px]">--</Text>;
          }
          return (
            <HStack className="items-center justify-center">
              <Text
                className={`text-[#4D4D4D] text-[14px] font-600 leading-[20px] ${
                  Number(value) >= 5 ? 'text-[#05C697]' : 'text-[#FF0000]'
                }`}
              >
                {averageNumber(Number(value || 0), 2)}
              </Text>
              <Text className="text-[#808080] text-[14px] font-400 leading-[14px]"> /10</Text>
            </HStack>
          );
        },
        enablePress: true,
      },
      {
        key: 'operation',
        title: '',
        align: 'center',
        width: '146px',
        render: (value: string, row: any) => {
          if (!row.exchange_url) {
            return null
          }
          return (
            <HStack className="items-center justify-center">
              <a href={row.exchange_url} target="_blank">
                <Button className="bg-[#F7F7F7] w-[146px] h-[36px] data-[hover=true]:bg-[#05C697]">
                  <ButtonText className="text-[#0A0A0A] text-[14px] font-not-italic font-500 leading-[20px]">
                    <HStack className="gap-[4px] items-center">
                      {row.name}
                      <RiContractRightLine size={16} />
                    </HStack>
                  </ButtonText>
                </Button>
              </a>
            </HStack>
          );
        },
      },
    ];
  }, [t]);

  // const selectListSort = useMemo(() => {
  //   return [
  //     {
  //       label: t('exchange_page.rank.table.default_sort'),
  //       value: null,
  //     },
  //     {
  //       label: t('exchange_page.rank.table.desc'),
  //       value: 'desc',
  //     },
  //     {
  //       label: t('exchange_page.rank.table.asc'),
  //       value: 'asc',
  //     },
  //   ];
  // }, [t]);
  // const selectListField = useMemo(() => {
  //   const fieldList = columns
  //     .filter((item) => !!item.sortKey)
  //     .map((item) => ({
  //       label:
  //         typeof item.title === 'string'
  //           ? item.title
  //           : React.isValidElement(item.title)
  //             ? item.title.props?.children
  //             : '',
  //       value: item.sortKey,
  //     }));
  //   return [
  //     {
  //       label: t('exchange_page.rank.table.field'),
  //       value: null,
  //     },
  //     ...fieldList,
  //   ];
  // }, [columns, t]);

  const quickSortList = useMemo(() => {
    return quickSortListCfg.map((item) => ({
      label: t(item.i18nLabelKey),
      value: item.value,
    }));
  }, [t]);

  const router = useRouter();
  return (
    <>
      <Box className="p-6 w-full">
        <VStack className="gap-1">
          <Text className="text-[#0A0A0A] text-[24px] font-not-italic font-600 leading-[32px]">
            {t('exchange_page.rank.title')}
          </Text>
          <Text className={`text-[#808080] text-[14px] font-not-italic font-500 leading-[20px]`}>
            {t('exchange_page.rank.description')}
          </Text>
        </VStack>
        <VStack className="mt-6">
          <Box className="mb-2 rounded-[12px] bg-[rgba(5,198,151,0.10)] backdrop-blur-[2px] p-1">
            <Grid
              className="gap-[2px]"
              _extra={{
                className: 'grid-cols-5',
              }}
            >
              {quickSortList.map((item) => (
                <GridItem
                  key={item.value}
                  _extra={{
                    className: 'col-span-1',
                  }}
                >
                  <Link href={`/exchange/rank?sort=${item.value}`}>
                    <HStack
                      className={`w-full h-[40px] px-[16px] py-[12px] justify-center items-center flex-[1_0_0] rounded-[8px] ${
                        item.value === defaultSortKey ? 'bg-[#05C697]' : ''
                      }`}
                    >
                      <Text
                        className={`text-center font-[Poppins] text-[14px] font-not-italic font-400 leading-[20px] ${
                          item.value === defaultSortKey ? 'text-[#FFF]' : 'text-[#05C697]'
                        }`}
                      >
                        {item.label}
                      </Text>
                    </HStack>
                  </Link>
                </GridItem>
              ))}
            </Grid>
          </Box>
          <HStack className="gap-4 justify-between items-center">
            <TextTabs tabs={tabs} onPress={setActiveTab} activeTab={activeTab} />

            <HStack className="gap-3">
              <SelectUI
                enableSearch={true}
                label={t('exchange_page.rank.legal_tender.label')}
                className="h-[36px] min-w-[160px] rounded-[8px]"
                valueClassName="text-[#0A0A0A] text-[14px] font-500 leading-[20px]"
                list={legalTender}
                value={selectedLegalTender}
                onChange={setSelectedLegalTender}
              />
              {/* <SelectUI
                className="h-[36px] min-w-[104px] rounded-[8px]"
                valueClassName="text-[#0A0A0A] text-[14px] font-500 leading-[20px]"
                list={selectListSort}
                value={sortValue}
                onChange={setSortValue}
              />
              <SelectUI
                className="h-[36px] min-w-[118px] rounded-[8px]"
                valueClassName="text-[#0A0A0A] text-[14px] font-500 leading-[20px]"
                list={selectListField}
                value={selectedSort}
                onChange={setSelectedSort}
              /> */}
            </HStack>
          </HStack>
          <TableUi
            sortValue={sortValue}
            sortKey={selectedSort}
            columns={columns}
            data={data}
            loading={loading}
            handleSort={(key, value) => {
              setSelectedSort(key as keyof ExchangeOverview);
              setSortValue(value);
            }}
            pageConfig={{
              total: data.length,
              pageSize: 20,
              currentPage: currentPage,
              onChange: (page) => {
                setCurrentPage(page);
              },
            }}
            onRowPress={(row) => {
              router.push(`/exchange/detail/${row.name}`);
            }}
          />
        </VStack>
      </Box>
    </>
  );
}
