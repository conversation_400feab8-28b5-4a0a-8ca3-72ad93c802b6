import { VStack } from '@quantum/components/ui/vstack';
import { getExchangeDetail } from '../../services/crypto_exchange/crypto_exchange.api';
import ExchangeDetailInfo from './components/ExchangeDetailInfo';
import ExchangeHeader from './components/ExchangeHeader/ExchangeHeader';
import axios from 'axios';
async function getExchangeUrl(exchangeName: string) {

  const isGray = process.env.SETUP_ENV === 'gray';

  const env = isGray ? 'gray' : process.env.APP_ENV;
  try {
    const res = await axios.get('http://qqtoa.ulegofi.com/api/exchange_url:list', {
      headers: {
        Authorization: `Bearer ${process.env.OA_API_KEY}`,
      },
      params: {
        pageSize: 1000,
        page: 1,
        filter: { $and: [{ enable_env: { $anyOf: [env] } }, { exchange_name: { $eq: exchangeName } }] },
      },
    });
   
    const list = res.data.data as {
      exchange_name: string;
      exchange_url: string;
    }[];
    return list?.[0]?.exchange_url || '';
  } catch (error) {
    console.error(error);
    return '';
  }
}


const getExchangeDetailData = async (exchangeName: string) => {
  try {
    const response = await getExchangeDetail(exchangeName);
    return response;
  } catch (err) {
    return undefined;
  }
};

const fetchData = async (exchangeName: string) => {
  const [detailData, exchangeUrl] = await Promise.all([
    getExchangeDetailData(exchangeName),
    getExchangeUrl(exchangeName),
  ]);
  return {
    detailData,
    exchangeUrl,
  };
};


export default async function ExchangeDetail({ exchangeName }: { exchangeName: string }) {
  const { detailData, exchangeUrl } = await fetchData(exchangeName);
 

  return (
    <VStack className="max-w-[1440px] w-full px-[142px] py-[40px] mx-auto bg-white gap-[31px]">
      <ExchangeHeader exchangeName={exchangeName} defaultExchangeDetail={detailData} url={exchangeUrl} />

      <ExchangeDetailInfo exchangeName={exchangeName} detailData={detailData} />
    </VStack>
  );
}
