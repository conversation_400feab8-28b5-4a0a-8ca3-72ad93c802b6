'use client';
import { Grid, GridItem } from '@quantum/components/ui/grid';
import TradingStats from '../TradingStats/TradingStats';
import ContractData from '../ContractData/ContractData';
import RankingList from '../RankingList/RankingList';
import { useRequest } from 'ahooks';
import type { ExchangeDetail } from '../../../../services/crypto_exchange/crypto_exchange.types';
import { getExchangeDetail } from '../../../../services/crypto_exchange/crypto_exchange.api';

export default function ExchangeDataInfo({exchangeName, defaultExchangeDetail}: {exchangeName: string,  defaultExchangeDetail?: ExchangeDetail}) {
    
  const {data = defaultExchangeDetail} = useRequest(async () => {
    const response = await getExchangeDetail(exchangeName);
    return response;
  }, {
    refreshDeps: [exchangeName],
    ready: !!exchangeName && !defaultExchangeDetail,
  });

  return (
    <Grid
      className="flex-1 gap-[20px]"
      _extra={{
        className: 'grid-cols-1 lg:grid-cols-2',
      }}
    >
      <GridItem
        _extra={{
          className: 'col-span-1',
        }}
      >
        <TradingStats data={data} />
      </GridItem>

      <GridItem
        _extra={{
          className: 'col-span-1',
        }}
      >
        <ContractData data={data} />
      </GridItem>
      {/* <GridItem
        _extra={{
          className: 'col-span-1',
        }}
      >
        <RankingList data={data} />
      </GridItem> */}
    </Grid>
  );
};
