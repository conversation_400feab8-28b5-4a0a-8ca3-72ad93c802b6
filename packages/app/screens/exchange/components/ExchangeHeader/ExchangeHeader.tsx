'use client';
import React from 'react';
import { Image, TouchableOpacity } from 'react-native';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { Icon } from '@quantum/components/ui/icon';
import { RiArrowRightSFill, RiContractRightLine } from 'react-icons/ri';
import { useRequest } from 'ahooks';
import type { ExchangeDetail } from '../../../../services/crypto_exchange/crypto_exchange.types';
import { getExchangeDetail } from '../../../../services/crypto_exchange/crypto_exchange.api';
import { useTranslation } from 'react-i18next';
import { Button } from '@quantum/components/ui/button';
import Link from '@unitools/link';
import { useSearchParams } from '@unitools/navigation';

interface ExchangeHeaderProps {
  exchangeName: string;
  defaultExchangeDetail?: ExchangeDetail;
  url: string;
}

const ExchangeHeader: React.FC<ExchangeHeaderProps> = ({ url, exchangeName, defaultExchangeDetail }) => {
  const { t } = useTranslation();


  const query = useSearchParams();

  const { data = defaultExchangeDetail } = useRequest(
    async () => {
      const response = await getExchangeDetail(exchangeName);
      return response;
    },
    {
      refreshDeps: [exchangeName],
      ready: !!exchangeName && !defaultExchangeDetail,
    },
  );

  const exchange_info = data?.exchange_info;

  return (
    <VStack className="w-full gap-6">
      <HStack className="items-center gap-[6px] text-[#0A0A0A] font-[Inter] text-[14px] font-not-italic font-400 leading-[20px]">
        <Text className="">{t('exchange_page.header.breadcrumb.crypto_exchange')}</Text>
        <Text className=""> / </Text>
        <Text className="">{exchange_info?.name || '--'}</Text>
      </HStack>

      <HStack className="items-center justify-between">
        <HStack className="items-center gap-3">
          <Box className="w-12 h-12 rounded-[6px] border border-[#F5F5F5] overflow-hidden flex justify-center items-center">
            <Image source={{ uri: exchange_info?.logo }} className="w-[48px] h-[48px]" resizeMode="contain" />
          </Box>

          <VStack className="gap-[2px]">
            <Text className="text-[#0A0A0A] text-[18px] font-not-italic font-600 leading-[24px]">
              {exchange_info?.name}
            </Text>
            <Text className="text-[#808080] text-[12px] font-not-italic font-400 leading-[16px]">
              {t('exchange_page.header.centralized_exchange')}
            </Text>
          </VStack>
        </HStack>

        {!!url && (
          <a href={url} target="_blank">
            <Button className="bg-[#05C697] px-4 py-2.5 rounded-full flex-row items-center h-[40px] min-w-[120px]">
              <HStack className="items-center gap-1">
                <Text className="text-[#FFF] text-[16px] font-not-italic font-500 leading-[20px]">
                  {t('exchange_page.header.go_to_trade')}
                </Text>
                <RiContractRightLine size={20} color="white" />
              </HStack>
            </Button>
          </a>
        )}
      </HStack>
    </VStack>
  );
};

export default ExchangeHeader;
