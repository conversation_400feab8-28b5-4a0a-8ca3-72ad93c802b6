'use client';
import React, { useState } from 'react';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { Icon } from '@quantum/components/ui/icon';
import { RiArrowDownSFill, RiArrowUpSFill } from 'react-icons/ri';
import { Pressable } from '@quantum/components/ui/pressable';
import { HStack } from '@quantum/components/ui/hstack';
import { useTranslation } from 'react-i18next';
import type { ExchangeDetail } from '../../../../services/crypto_exchange/crypto_exchange.types';

interface FAQItemProps {
  question?: string;
  answer?: string;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer }) => {
  const [expanded, setExpanded] = useState(false);

  return (
    <VStack className="gap-2">
      <Pressable className="flex-row items-center gap-1" onPress={() => setExpanded(!expanded)}>
        {expanded ? <RiArrowUpSFill color="#0A0A0A" size={16} /> : <RiArrowDownSFill color="#0A0A0A" size={16} />}
        <Text className="text-[#0A0A0A] text-[16px] font-not-italic font-600 leading-[20px]">{question}</Text>
      </Pressable>

      {expanded && (
        <HStack className="gap-1">
          <Box className="w-[16px]"></Box>
          <Text className="flex-1 text-[#808080] font-[Inter] text-[14px] font-not-italic font-400 leading-[20px]">
            {answer}
          </Text>
        </HStack>
      )}
    </VStack>
  );
};

const ExchangeFAQ: React.FC<{ data: any}> = ({data = []}) => {

  const { t, i18n } = useTranslation();
  const titleKey = i18n.language === 'zh-CN' ? 'question_zh_cn' : 'question_en';
  const contentKey = i18n.language === 'zh-CN' ? 'answer_zh_cn' : 'answer_en';

  return (
    <VStack className="gap-[24px]">
      {data.map((item: any, index: number) => (
        <FAQItem key={index} question={item[titleKey]} answer={item[contentKey]} />
      ))}
    </VStack>
  );
};

export default ExchangeFAQ;
