import ExchangeFAQ from './ExchangeFAQ';
import axios from 'axios';
async function getFAQList(exchangeName: string) {

  const isGray = process.env.SETUP_ENV === 'gray';

  const env = isGray ? 'gray' : process.env.APP_ENV;
  try {
    const res = await axios.get('http://qqtoa.ulegofi.com/api/exchange_faq:list', {
      headers: {
        Authorization: `Bearer ${process.env.OA_API_KEY}`,
      },
      params: {
        pageSize: 1000,
        page: 1,
        filter: { $and: [{ enable_env: { $anyOf: [env] } }, { exchange_name: { $eq: exchangeName } }] },
      },
    });
   
    const list = res.data.data as {
      question_en: string;
      answer_en: string;
      exchange_name: string;
      question_zh_cn: string;
      answer_zh_cn: string;
    }[];
    return list;
  } catch (error) {
    console.error(error);
    return [];
  }
}
export default async function ExchangeFAQIndex({ exchangeName }: { exchangeName: string }) {
  const data = await getFAQList(exchangeName);
  return <ExchangeFAQ data={data} />;
}
