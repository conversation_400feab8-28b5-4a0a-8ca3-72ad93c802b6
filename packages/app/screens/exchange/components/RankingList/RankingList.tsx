'use client';
import React from 'react';
import { Image } from 'react-native';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { HStack } from '@quantum/components/ui/hstack';
import { Icon } from '@quantum/components/ui/icon';
import { RiArrowUpSFill } from 'react-icons/ri';
import TextOrImageLogo from '../../../../components/TextOrImageLogo';
import ChangeValue from '../../../../components/ChangeValue';
import type { ExchangeDetail } from '../../../../services/crypto_exchange/crypto_exchange.types';

interface RankingItemProps {
  name: string;
  price: string;
  change: string;
  iconUrl: string;
}

const RankingItem: React.FC<RankingItemProps> = ({ name, price, change, iconUrl }) => {
  return (
    <HStack className="items-center gap-[6px] h-9">
      <HStack className="items-center gap-2 flex-1">
        <Box
          className="w-6 h-6 rounded-full overflow-hidden flex justify-center items-center"
          style={{ backgroundColor: iconUrl ? 'transparent' : 'gray' }}
        >
          <TextOrImageLogo text={name} size={20} />
        </Box>
        <Text className="text-[#0A0A0A] font-[Inter] text-[14px] font-not-italic font-600 leading-[20px]">{name}</Text>
      </HStack>

      <HStack className="gap-[6px] w-[150px] justify-end">
        <Text className="text-[#0A0A0A] text-[14px] font-not-italic font-500 leading-[20px]">{price}</Text>
        <ChangeValue change={`${change}`} />
      </HStack>
    </HStack>
  );
};

const RankingList: React.FC<{data?: ExchangeDetail}> = ({data}) => {
  // 模拟数据
  const topGainers = [
    {
      name: 'Portal',
      price: '$5.12',
      change: '+999.99%',
      iconUrl: 'https://cryptologos.cc/logos/polkadot-new-dot-logo.png?v=026',
    },
    {
      name: 'Alpaca Finance',
      price: '$5.12',
      change: '+999.99%',
      iconUrl: 'https://cryptologos.cc/logos/alpaca-finance-alpaca-logo.png?v=026',
    },
    {
      name: 'REI Network',
      price: '$0.3475',
      change: '+999.99%',
      iconUrl: 'https://cryptologos.cc/logos/ren-ren-logo.png?v=026',
    },
  ];

  return (
    <VStack className="p-4 gap-[16px] rounded-[12px] border-[2px] border-solid border-[#E6E6E6] bg-[rgba(5,198,151,0.03)]">
      <Text className="text-[#0A0A0A] text-[16px] font-not-italic font-600 leading-[20px]">🚀 涨幅榜</Text>

      <VStack>
        {topGainers.map((item, index) => (
          <RankingItem key={index} name={item.name} price={item.price} change={item.change} iconUrl={item.iconUrl} />
        ))}
      </VStack>
    </VStack>
  );
};

export default RankingList;
