'use client';
import React, { useState, useEffect, useMemo } from 'react';
import { View } from 'react-native';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { Pressable } from '@quantum/components/ui/pressable';
import VolumeLineChart from './VolumeLineChart';
import { getExchangeVolumeChart } from '../../../../services/crypto_exchange/crypto_exchange.api';
import type { ExchangeVolume } from '../../../../services/crypto_exchange/crypto_exchange.types';
import dayjs from 'dayjs';
import { useRequest } from 'ahooks';
import { useTranslation } from 'react-i18next';

// 月份数据标签
const monthLabels = ['2 月', '3 月', '4 月', '5 月', '6 月', '7 月', '8 月', '9 月', '10 月', '11 月'];

// 金额标签
const amountLabels = ['$0', '$100億', '$200億', '$300億', '$400億', '$500億'];

interface VolumeChartProps {
  exchangeName: string;
}

const VolumeChart: React.FC<VolumeChartProps> = ({ exchangeName }) => {
  const { t } = useTranslation();
  const [selectedRange, setSelectedRange] = useState('3m');

  // 时间范围选项
  const timeRangeOptions = [
    { label: t('exchange_page.volume_chart.time_range.24h'), value: '24h' },
    { label: t('exchange_page.volume_chart.time_range.7d'), value: '7d' },
    { label: t('exchange_page.volume_chart.time_range.14d'), value: '14d' },
    { label: t('exchange_page.volume_chart.time_range.1m'), value: '1m' },
    { label: t('exchange_page.volume_chart.time_range.3m'), value: '3m', active: true },
    { label: t('exchange_page.volume_chart.time_range.1y'), value: '1y' },
  ];

  const { data: volumeData, loading } = useRequest(
    async () => {
      let days = 30;
      switch (selectedRange) {
        case '24h':
          days = 1;
          break;
        case '7d':
          days = 7;
          break;
        case '14d':
          days = 14;
          break;
        case '1m':
          days = 30;
          break;
        case '3m':
          days = 90;
          break;
        case '1y':
          days = 365;
          break;
        default:
          days = 30;
      }
      const res = await getExchangeVolumeChart(exchangeName, days);
      return res;
    },
    {
      refreshDeps: [exchangeName, selectedRange],
    },
  );

  return (
    <>
      <Text className="text-[#0A0A0A] text-[18px] font-not-italic font-600 leading-[24px]">
        {t('exchange_page.detail.exchange_volume')}
      </Text>
      <VStack className="border border-[#E6E6E6] rounded-[12px] bg-white py-8 px-0 overflow-hidden">
        <VStack className="gap-[20px]">
          <HStack className="items-center justify-end w-full px-8">
            <HStack className="bg-[rgba(0,0,0,0.04)] rounded-[8px] p-1 gap-[2px]">
              {timeRangeOptions.map((option) => (
                <Pressable
                  key={option.value}
                  className={`py-1.5 px-4 rounded-md ${option.value === selectedRange ? 'bg-white' : 'bg-transparent'}`}
                  onPress={() => setSelectedRange(option.value)}
                >
                  <Text
                    className={`text-xs ${
                      option.value === selectedRange ? 'text-gray-900 font-medium' : 'text-gray-500 font-normal'
                    }`}
                  >
                    {option.label}
                  </Text>
                </Pressable>
              ))}
            </HStack>
          </HStack>
          <VolumeLineChart data={volumeData} loading={loading} />
        </VStack>
      </VStack>
    </>
  );
};

export default VolumeChart;
