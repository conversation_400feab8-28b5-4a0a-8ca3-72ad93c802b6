'use client';
import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { LineChart } from 'echarts/charts';
import { DataZoomComponent, GridComponent, TooltipComponent } from 'echarts/components';
import * as echarts from 'echarts/core';
import { SVGRenderer } from '@wuba/react-native-echarts';
import dayjs from 'dayjs';
import type { ExchangeVolume } from '../../../../services/crypto_exchange/crypto_exchange.types';
import { averageNumber } from '../../../../uitls/number';

echarts.use([LineChart, DataZoomComponent, GridComponent, TooltipComponent, SVGRenderer]);

interface VolumeLineChartProps {
  data?: ExchangeVolume[] | null;
  loading?: boolean;
}

export default function VolumeLineChart({
  data: defaultData = null,
  loading = false,
}: VolumeLineChartProps) {
  const { t } = useTranslation();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);

  // 数据处理
  const { xData, volumeData } = React.useMemo(() => {
    if (!defaultData?.length) {
      return { xData: [], volumeData: [] };
    }
    const xData = defaultData.map((item: ExchangeVolume) => dayjs(item.t).format('YYYY/MM/DD'));
    const volumeData = defaultData.map((item: ExchangeVolume) => Number(item.v));
    return { xData, volumeData };
  }, [defaultData]);

  // 计算Y轴范围
  const { volumeMin, volumeMax } = React.useMemo(() => {
    if (!volumeData.length) {
      return {
        volumeMin: 0,
        volumeMax: 100,
      };
    }

    const volumeMin = Math.min(...volumeData);
    const volumeMax = Math.max(...volumeData);

    // 计算合适的范围，留出10%的边距
    const volumeRange = volumeMax - volumeMin;

    return {
      volumeMin: Math.max(0, volumeMin - volumeRange * 0.1), // 确保最小值不小于0
      volumeMax: volumeMax + volumeRange * 0.1,
    };
  }, [volumeData]);

  // 静态配置
  const staticOption = React.useMemo(
    () => ({
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const idx = params?.[0]?.dataIndex ?? 0;
          let html = `${params?.[0]?.axisValue || ''}<br/>`;
          params.forEach((p: any) => {
            html += `<span style=\"color:${p.color}\">●</span> ${t('chart.volume')}: <b>${averageNumber(p.data)}</b><br/>`;
          });
          return html;
        },
      },
      grid: { left: 50, right: 50, top: 10, bottom: 20 },
      xAxis: {
        type: 'category',
        axisLabel: { show: true },
        boundaryGap: false,
      },
      yAxis: {
        type: 'value',
        position: 'right',
        axisLabel: {
          formatter: (v: number) => averageNumber(v),
        },
        
        // min: volumeMin,
        // max: volumeMax,
      },
      dataZoom: [
        {
          type: 'slider',
          show: false,
          xAxisIndex: 0,
          bottom: 10,
          height: 20,
          start: 0,
          end: 100,
          labelFormatter: (value: number) => {
            if (xData.length && value >= 0 && value < xData.length) {
              return dayjs(xData[Math.floor(value)]).format('MM/DD');
            }
            return '';
          }
        },
      ],
      series: [
        {
          name: t('chart.volume'),
          type: 'line',
          showSymbol: false,
          connectNulls: true,
          smooth: true,
          itemStyle: {
            color: '#05C697'
          },
          lineStyle: {
            color: '#05C697',
            width: 2
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(5, 198, 151, 0.5)' },
              { offset: 1, color: 'rgba(5, 198, 151, 0.05)' }
            ])
          },
          z: 2,
        },
      ],
    }),
    [volumeMin, volumeMax, t, xData],
  );

  useEffect(() => {
    if (!chartRef.current) return;

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        useDirtyRect: true,
      });

      chartInstance.current.setOption(staticOption);
    }

    // 控制加载状态
    if (loading) {
      // 清空数据
      chartInstance.current.setOption({
        xAxis: {
          data: [],
        },
        series: [
          {
            data: [],
          },
        ],
      });
      chartInstance.current.showLoading({
        text: 'Loading...',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        textColor: '#000',
        spinnerRadius: 10,
        lineWidth: 5,
        showSpinner: true,
        showText: true,
        animation: true,
      });
    } else {
      chartInstance.current.hideLoading();
      // 只在有数据时更新图表数据
      if (xData.length) {
        chartInstance.current.setOption({
          xAxis: {
            data: xData,
          },
          series: [
            {
              data: volumeData,
            },
          ],
        });
      }
    }

    const resize = () => chartInstance.current && chartInstance.current.resize();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', resize);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', resize);
      }
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [xData, volumeData, staticOption, loading]);

  return <div ref={chartRef} style={{ width: '100%', height: '300px' }} />;
}
