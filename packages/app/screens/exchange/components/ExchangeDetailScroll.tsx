'use client';
import { HStack } from '@quantum/components/ui/hstack';
import { Pressable } from '@quantum/components/ui/pressable';
import { Text } from '@quantum/components/ui/text';
import { Box } from '@quantum/components/ui/box';
import { useState, useEffect, useRef } from 'react';
import { Platform } from 'react-native';
import { useTranslation } from 'react-i18next';
export default function ExchangeDetailScroll({
  contentContainerId = 'content-container',
  offsetTop = 160,
}: {
  contentContainerId?: string;
  offsetTop?: number;
}) {
  const { t } = useTranslation();

  // 定义 tabs 数据
  const tabs: any[] = [
    {
      key: 'data',
      label: t('exchange_page.detail.tabs.data'),
      id: 'data',
    },
    {
      key: 'intro',
      label: t('exchange_page.detail.tabs.intro'),
      id: 'intro',
    },
    {
      key: 'liquidity',
      label: t('exchange_page.detail.tabs.liquidity'),
      id: 'liquidity',
    },
    {
      key: 'volume',
      label: t('exchange_page.detail.tabs.volume'),
      id: 'volume',
    },
    {
      key: 'reserves',
      label: t('exchange_page.detail.tabs.reserves'),
      id: 'reserves',
    },
  ];
  const [activeKey, setActiveKey] = useState(tabs[0].key);

  const handleTabClick = (key: string) => {
    setActiveKey(key);
    console.log('key', key);

    // 找到对应ID的元素并滚动到该位置
    const targetElement = document.getElementById(key);
    if (targetElement) {
      // 获取元素相对于视口的位置
      const rect = targetElement.getBoundingClientRect();

      // 计算元素相对于文档顶部的位置 (视口位置 + 当前滚动位置 - 顶部偏移)
      const scrollPosition = rect.top + window.scrollY - offsetTop;

      const scrollOptions = {
        top: scrollPosition,
        behavior: 'smooth' as ScrollBehavior,
      };

      if (Platform.OS === 'web') {
        window.scrollTo(scrollOptions);
      } else {
        // 在React Native中，我们可以使用scrollTo方法
        // 这里假设我们有一个ScrollView引用
        // 实际使用时需要将scrollViewRef从父组件传入
        // scrollViewRef.current?.scrollTo(scrollOptions);
      }
    }
  };

  // 设置滚动监听，当滚动到某个区域时更新activeKey
  useEffect(() => {
    if (Platform.OS !== 'web') {
      // 对于React Native，需要使用不同的滚动检测方法
      // 这里可以使用onScroll事件监听
      return;
    }

    // 在Web环境下使用IntersectionObserver
    const observerOptions = {
      root: null, // 使用视口作为根元素
      rootMargin: `-${offsetTop}px 0px -70% 0px`, // 考虑顶部偏移量，并减少底部检测区域
      threshold: [0, 0.1, 0.2, 0.3, 0.4, 0.5], // 使用多个阈值提高精度
    };

    // 添加滚动事件监听，处理滚动位置小于第一个元素的情况以及精确检测当前可见元素
    const handleScroll = () => {
      // 如果没有元素，直接返回
      if (tabs.length === 0) return;

      // 创建一个数组存储所有元素的位置信息
      const positions = [];

      // 计算每个元素距离视口顶部的距离
      for (const tab of tabs) {
        const element = document.getElementById(tab.key);
        if (element) {
          const rect = element.getBoundingClientRect();
          // 计算元素顶部距离视口顶部的距离（考虑偏移量）
          const topDistance = rect.top - offsetTop;

          // 将元素信息存入数组
          positions.push({
            key: tab.key,
            topDistance,
          });
        }
      }

      // 优先处理第一个元素之前的滚动位置
      const firstElement = document.getElementById(tabs[0].key);
      if (firstElement) {
        const rect = firstElement.getBoundingClientRect();
        // 如果第一个元素的顶部位置大于视口顶部位置加上偏移量，说明滚动位置在第一个元素之前
        if (rect.top > offsetTop) {
          setActiveKey(tabs[0].key);
          return;
        }
      }

      // 找出最接近视口顶部但不超过顶部的元素（即最接近但小于等于0的距离）
      // 首先过滤出所有已经进入或刚好位于偏移位置的元素
      const visibleElements = positions.filter((pos) => pos.topDistance <= 0);

      // 如果有可见元素，找出最接近顶部的那个
      if (visibleElements.length > 0) {
        // 按照到顶部距离排序，距离最小的（最接近顶部的）排在最前面
        visibleElements.sort((a, b) => b.topDistance - a.topDistance);
        // 设置最接近顶部的元素为激活状态
        setActiveKey(visibleElements[0].key);
      } else {
        // 如果没有可见元素，默认激活第一个标签
        setActiveKey(tabs[0].key);
      }
    };

    // 使用节流函数减少滚动事件触发频率
    let ticking = false;
    const scrollListener = () => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', scrollListener);

    // 初始化时执行一次滚动检查
    handleScroll();

    // 清理函数
    return () => {
      // 移除滚动事件监听
      window.removeEventListener('scroll', scrollListener);
    };
  }, [tabs, offsetTop]);

  return (
    <HStack className="gap-6 border-b border-[#E6E6E6] sticky top-[64px] bg-white z-[10]">
      {tabs.map((tab) => (
        <HStack key={tab.key} className="h-[54px] items-center relative">
          <Pressable onPress={() => handleTabClick(tab.key)}>
            <Text
              className={`text-[18px] font-not-italic leading-[24px] ${
                activeKey === tab.key ? 'text-[#0A0A0A] font-600' : 'text-[#808080] font-500'
              }`}
            >
              {tab.label}
            </Text>
          </Pressable>
          <Box
            className={`absolute bottom-0 left-[50%] translate-x-[-50%] w-full h-[2px] bg-[#0A0A0A] transition-all duration-300 ${
              activeKey === tab.key ? 'w-full' : 'w-0 opacity-0'
            }`}
          />
        </HStack>
      ))}
    </HStack>
  );
}
