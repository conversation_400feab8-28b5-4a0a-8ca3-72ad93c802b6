'use client';
import React from 'react';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import type { ExchangeDetail } from '../../../../services/crypto_exchange/crypto_exchange.types';
import { useRequest } from 'ahooks';
import { getExchangeDetail } from '../../../../services/crypto_exchange/crypto_exchange.api';
import { averageNumber } from '../../../../uitls/number';
import { useTranslation } from 'react-i18next';
import { isNil } from 'lodash';

const ExchangeInfo: React.FC<{ exchangeName: string; defaultExchangeDetail?: ExchangeDetail }> = ({
  exchangeName,
  defaultExchangeDetail,
}) => {
  const { t } = useTranslation();

  const { data = defaultExchangeDetail } = useRequest(
    async () => {
      const response = await getExchangeDetail(exchangeName);
      return response;
    },
  );

  const exchangeData = {
    totalAssets: data?.exchange_info.total_asset_usd,
    spotVolume: {
      usd: data?.trade_info.spot_volume_24h_usd,
    },
  };

  return (
    <VStack className="gap-[24px]">
      <Text className="text-[#0A0A0A] text-[18px] font-not-italic font-600 leading-[24px]">
        {t('exchange_page.info.title')}
      </Text>

      <Box className="flex flex-col xl:flex-row gap-[40px] justify-between">
        <VStack className="gap-2">
          <Text className="text-[#0A0A0A] text-[32px] font-not-italic font-600 leading-[40px]">
            {isNil(exchangeData.spotVolume.usd) ? '--' : `$${averageNumber(exchangeData.spotVolume.usd)}`}
          </Text>
          <HStack className="gap-2">
            <Text className="text-[#808080] font-[Inter] text-[14px] font-not-italic font-400 leading-[20px]">
              {t('exchange_page.info.spot_volume_24h')}
            </Text>
            <Text className="text-[#808080] font-[Inter] text-[14px] font-not-italic font-400 leading-[20px]">
              {isNil(exchangeData.spotVolume.usd) ? '--' : `$${averageNumber(exchangeData.spotVolume.usd)}`}
            </Text>
          </HStack>
        </VStack>

        <VStack className="gap-2">
          <Text className="text-[#0A0A0A] text-[32px] font-not-italic font-600 leading-[40px]">
            {isNil(exchangeData.totalAssets) ? '--' : `$${averageNumber(exchangeData.totalAssets)}`}
          </Text>
          <Text className="text-[#808080] font-[Inter] text-[14px] font-not-italic font-400 leading-[20px]">
            {t('exchange_page.info.total_assets')}
          </Text>
        </VStack>
      </Box>
    </VStack>
  );
};

export default ExchangeInfo;
