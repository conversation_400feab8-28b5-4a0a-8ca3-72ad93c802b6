'use client';
import React from 'react';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { HStack } from '@quantum/components/ui/hstack';
import { View } from 'react-native';
import ProgressBar from '../../../../components/ProgressBar/ProgressBar';
import type { ExchangeDetail } from '../../../../services/crypto_exchange/crypto_exchange.types';
import { useRequest } from 'ahooks';
import { getExchangeDetail } from '../../../../services/crypto_exchange/crypto_exchange.api';
import { averageNumber } from '../../../../uitls/number';
import { useTranslation } from 'react-i18next';
import { isNil } from 'lodash';

const Liquidity: React.FC<{ exchangeName: string; defaultExchangeDetail?: ExchangeDetail }> = ({
  exchangeName,
  defaultExchangeDetail,
}) => {
  const { t } = useTranslation();

  const { data = defaultExchangeDetail } = useRequest(
    async () => {
      const response = await getExchangeDetail(exchangeName);
      return response;
    },
    {
      refreshDeps: [exchangeName],
      ready: !!exchangeName && !defaultExchangeDetail,
    },
  );
  //   已报告成交量
  //   货币数量
  //   交易对数量
  //   交易对的总信用评分
  const liquidityData = [
    {
      title: t('exchange_page.liquidity.reported_volume'),
      value: isNil(data?.market_info.spot_volume_24h_usd)
        ? '--'
        : `$${averageNumber(data?.market_info.spot_volume_24h_usd || 0)}`,
    },
    {
      title: t('exchange_page.liquidity.currency_count'),
      value: isNil(data?.market_info.currency_amount)
        ? '--'
        : `${averageNumber(data?.market_info.currency_amount || 0)}`,
    },
    {
      title: t('exchange_page.liquidity.pair_count'),
      value: isNil(data?.market_info.pair_amount) ? '--' : `${averageNumber(data?.market_info.pair_amount || 0)}`,
    },
    {
      title: t('exchange_page.liquidity.trust_score'),
      value: (
        <HStack className="gap-[12px] items-center justify-center">
          {isNil(data?.market_info.trust_score) ? (
            '--'
          ) : (
            <>
              <Box className="w-[114px]">
                <ProgressBar progress={Number(data?.market_info.trust_score || 0) * 10} size={8} />
              </Box>
              <Text>{Number(data?.market_info.trust_score || 0)}/10</Text>
            </>
          )}
        </HStack>
      ),
    },
  ];

  return (
    <VStack className="p-4 gap-[16px] rounded-[12px] border-[2px] border-solid border-[#E6E6E6] bg-[rgba(5,198,151,0.03)]">
      <Text className="text-[#0A0A0A] text-[16px] font-not-italic font-600 leading-[20px]">
        {t('exchange_page.liquidity.title')}
      </Text>
      <VStack className="gap-[16px]">
        {liquidityData.map((item, index) => (
          <HStack className="gap-[16px] items-center justify-between" key={index}>
            <Text className="text-[#0A0A0A] font-[Inter] text-[14px] font-not-italic font-600 leading-[20px]">
              {item.title}
            </Text>
            <Text className="text-[#0A0A0A] text-[14px] font-not-italic font-500 leading-[20px]">{item.value}</Text>
          </HStack>
        ))}
      </VStack>
    </VStack>
  );
};

export default Liquidity;
