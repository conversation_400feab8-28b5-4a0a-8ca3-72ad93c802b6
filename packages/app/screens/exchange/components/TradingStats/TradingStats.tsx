'use client';
import React from 'react';
import { StyleSheet } from 'react-native';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { HStack } from '@quantum/components/ui/hstack';
import type { ExchangeDetail } from '../../../../services/crypto_exchange/crypto_exchange.types';
import { averageNumber } from '../../../../uitls/number';
import { useTranslation } from 'react-i18next';
import { isNil } from 'lodash';
interface StatItemProps {
  title: string;
  value?: string | null;
  highlightColor?: string;
}

export const StatItem: React.FC<StatItemProps> = ({ title, value, highlightColor = '#05C697' }) => {
  return (
    <HStack className="flex-1 gap-[10px] items-center">
      <Box className={`w-[3px] h-[36px] rounded-[2px] bg-[${highlightColor}]`} />
      <VStack className="gap-[6px]">
        <Text className="text-[#0A0A0A] text-[16px] font-not-italic font-600 leading-[20px]">{isNil(value) ? '--' : `$${averageNumber(value)}`}</Text>
        <Text className="text-[#808080] text-[12px] font-not-italic font-400 leading-[16px]">{title}</Text>
      </VStack>
    </HStack>
  );
};

const TradingStats: React.FC<{ data?: ExchangeDetail }> = ({ data }) => {
  const { t } = useTranslation();
  
  const stats = [
    { title: t('exchange_page.trading_stats.spot_volume_24h'), value: data?.trade_info.spot_volume_24h_usd },
    // { title: '期权成交量', value: data.trade_info.future_volume_24h_usd || '--' },
    { title: t('exchange_page.trading_stats.futures_volume_24h'), value: data?.trade_info.future_volume_24h_usd },
    { title: t('exchange_page.trading_stats.futures_position'), value: data?.trade_info.future_position_usd },
  ];

  return (
    <VStack className="p-4 gap-[20px] rounded-[12px] border-[2px] border-solid border-[#E6E6E6] bg-[#FFF]">
      <Text className="text-[#0A0A0A] text-[16px] font-600 leading-[20px]">{t('exchange_page.trading_stats.title')}</Text>

      <VStack className="gap-[20px]">
        <HStack className="gap-[8px]">
          <StatItem title={stats[0].title} value={stats[0].value} highlightColor="#05C697" />
          <StatItem title={stats[1].title} value={stats[1].value} highlightColor="#05C697" />
        </HStack>

        <HStack className="gap-[8px]">
          <StatItem title={stats[2].title} value={stats[2].value} highlightColor="#05C697" />
          {/* <StatItem title={stats[3].title} value={stats[3].value} highlightColor="#05C697" /> */}
        </HStack>
      </VStack>
    </VStack>
  );
};

export default TradingStats;
