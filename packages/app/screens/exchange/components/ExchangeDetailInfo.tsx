import { Box } from '@quantum/components/ui/box';
import { VStack } from '@quantum/components/ui/vstack';
import ExchangeDataInfo from './ExchangeDataInfo/ExchangeDataInfo';
import ExchangeDetailScroll from './ExchangeDetailScroll';
import ExchangeFAQ from './ExchangeFAQ';
import ExchangeInfo from './ExchangeInfo/ExchangeInfo';
import Liquidity from './Liquidity/Liquidity';
import LiquidityTable from './LiquidityTable';
import VolumeChart from './VolumeChart/VolumeChart';
import VolumeTable from './VolumeTable';

export default function ExchangeDetailInfo({ exchangeName, detailData }: { exchangeName: string; detailData: any }) {

  // 定义 tabs 数据
  const tabs: any[] = [
    {
      key: 'data',
      id: 'data',
    },
    {
      key: 'intro',
      id: 'intro',
    },
    {
      key: 'liquidity',
      id: 'liquidity',
    },
    {
      key: 'volume',
      id: 'volume',
    },
    {
      key: 'reserves',
      id: 'reserves',
    },
  ];
  return (
    <>
      <Box className="w-full">
        <ExchangeDetailScroll />
        <VStack className="gap-10 py-5" id={tabs[0].id}>
          <ExchangeDataInfo exchangeName={exchangeName} defaultExchangeDetail={detailData} />

          <Box className={`flex flex-col lg:flex-row gap-[48px]`} id={tabs[1].id}>
            <VStack className="gap-[28px] flex-1">
              <ExchangeInfo exchangeName={exchangeName} defaultExchangeDetail={detailData} />
              <ExchangeFAQ exchangeName={exchangeName} />
            </VStack>
            <Box className="w-full lg:w-[424px]">
              <Liquidity exchangeName={exchangeName} defaultExchangeDetail={detailData} />
            </Box>
          </Box>

          <VStack className="gap-6" id={tabs[2].id}>
            <LiquidityTable exchangeName={exchangeName} />
          </VStack>

          <VStack className="gap-6" id={tabs[3].id}>
          
            <VolumeChart exchangeName={exchangeName} />
          </VStack>

          <VStack className="gap-6" id={tabs[4].id}>
          
            <VolumeTable exchangeName={exchangeName} />
          </VStack>
        </VStack>
      </Box>
    </>
  );
}
