'use client';
import React, { useState } from 'react';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { HStack } from '@quantum/components/ui/hstack';
import { Divider } from '@quantum/components/ui/divider';
import { Pressable } from '@quantum/components/ui/pressable';

export interface TabItem {
  key: string;
  label: string;
  content: React.ReactNode;
}

interface TabsProps {
  tabs: TabItem[];
  defaultActiveKey?: string;
  mountInactiveTabsOnDemand?: boolean; // 控制非活动 tab 是否挂载
  onChange?: (activeKey: string) => void;
}

const Tabs: React.FC<TabsProps> = ({
  tabs,
  defaultActiveKey,
  mountInactiveTabsOnDemand = true, // 默认只挂载当前激活的 tab
  onChange,
}) => {
  const [activeKey, setActiveKey] = useState(defaultActiveKey || (tabs.length > 0 ? tabs[0].key : ''));

  const handleTabClick = (key: string) => {
    setActiveKey(key);
    onChange?.(key);
  };

  return (
    <Box className="w-full">
      <HStack className="gap-6 border-b border-[#E6E6E6]">
        {tabs.map((tab) => (
          <HStack key={tab.key} className="h-[54px] items-center relative">
            <Pressable onPress={() => handleTabClick(tab.key)}>
              <Text
                className={`  text-[18px] font-not-italic leading-[24px] ${
                  activeKey === tab.key ? 'text-[#0A0A0A] font-600' : 'text-[#808080] font-500'
                }`}
              >
                {tab.label}
              </Text>
            </Pressable>
            <Box
              className={`absolute bottom-0 left-[50%] translate-x-[-50%] w-full h-[2px] bg-[#0A0A0A] transition-all duration-300 ${
                activeKey === tab.key ? 'w-full' : 'w-0 opacity-0'
              }`}
            />
          </HStack>
        ))}
      </HStack>

      <Box>
        {tabs.map((tab) => {
          // 如果设置了按需挂载，则只渲染当前激活的 tab
          if (mountInactiveTabsOnDemand && tab.key !== activeKey) {
            return null;
          }

          // 使用 display 样式控制可见性
          return (
            <Box key={tab.key} className={activeKey === tab.key ? 'block' : 'hidden'}>
              {tab.content}
            </Box>
          );
        })}
      </Box>
    </Box>
  );
};

export default Tabs;
