'use client';
import React from 'react';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { HStack } from '@quantum/components/ui/hstack';
import { StatItem } from '../TradingStats/TradingStats';
import type { ExchangeDetail } from '../../../../services/crypto_exchange/crypto_exchange.types';
import { averageNumber } from '../../../../uitls/number';
import { useTranslation } from 'react-i18next';
import { isNil } from 'lodash';

const ContractData: React.FC<{ data?: ExchangeDetail }> = ({ data }) => {
  const { t } = useTranslation();

  // 爆仓数据
  const liquidationData = {
    total:
      isNil(data?.liq_info.long_liq_24h_usd) || isNil(data?.liq_info.short_liq_24h_usd)
        ? null
        : Number(data?.liq_info.long_liq_24h_usd) + Number(data?.liq_info.short_liq_24h_usd),
    longAmount: data?.liq_info.long_liq_24h_usd,
    shortAmount: data?.liq_info.short_liq_24h_usd,
    // 比例计算: 长仓爆仓 / 总爆仓
    longPercentage: ~~(
      (Number(data?.liq_info.long_liq_24h_usd) /
        (Number(data?.liq_info.long_liq_24h_usd) + Number(data?.liq_info.short_liq_24h_usd)) || 0) * 100
    ), // 3.23 / 4.74 ≈ 0.68
  };

  return (
    <VStack className="p-4 gap-[32px] rounded-[12px] border-[2px] border-solid border-[#E6E6E6] bg-[#FFF] h-full">
      <HStack className="items-center justify-between">
        <Text className="flex-[1_0_0] text-[#0A0A0A] text-[16px] font-not-italic font-600 leading-[20px]">
          {t('exchange_page.contract_data.title')}
        </Text>

        <HStack className="items-center gap-[6px]">
          <Text className="text-[#808080] text-[14px] font-not-italic font-400 leading-[20px]">
            {t('exchange_page.contract_data.liquidation_24h')}
          </Text>
          <Text className="text-[#0A0A0A] text-[14px] font-not-italic font-600 leading-[20px]">
            {isNil(liquidationData.total) ? '--' : `$${averageNumber(liquidationData.total)}`}
          </Text>
        </HStack>
      </HStack>

      <VStack className="gap-[8px]">
        <HStack className="items-center gap-[8px]">
          <StatItem
            title={t('exchange_page.contract_data.long_liquidation')}
            value={liquidationData.longAmount}
            highlightColor="#05C697"
          />
          <StatItem
            title={t('exchange_page.contract_data.short_liquidation')}
            value={liquidationData.shortAmount}
            highlightColor="#E55043"
          />
        </HStack>
        {isNil(liquidationData.total) ? null : (
          <Box className="py-[15px]">
            <HStack className="w-full h-3">
              <Box
                style={{
                  backgroundColor: '#05C697',
                  width: `${liquidationData.longPercentage}%`,
                  height: '100%',
                  borderTopLeftRadius: 4,
                  borderBottomLeftRadius: 4,
                }}
              />
              <Box
                style={{
                  backgroundColor: '#E55043',
                  width: `${100 - liquidationData.longPercentage}%`,
                  height: '100%',
                  borderTopRightRadius: 4,
                  borderBottomRightRadius: 4,
                }}
              />
            </HStack>
          </Box>
        )}
      </VStack>
    </VStack>
  );
};

export default ContractData;
