'use client';
import React from 'react';
import { Image } from 'react-native';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import TableUi, { type TableColumn } from '../../../../components/TableUi/TableUi';
import TextOrImageLogo from '../../../../components/TextOrImageLogo';
import { useRequest } from 'ahooks';
import { getExchangeAssets } from '../../../../services/crypto_exchange/crypto_exchange.api';
import type { ExchangeAsset } from '../../../../services/crypto_exchange/crypto_exchange.types';
import { averageNumber } from '../../../../uitls/number';
import { useTranslation } from 'react-i18next';

const VolumeTable: React.FC<{ defaultData?: ExchangeAsset[]; exchangeName: string }> = ({
  defaultData,
  exchangeName,
}) => {
  const { t } = useTranslation();

  const { data: volumeData = defaultData } = useRequest(
    async () => {
      const response = await getExchangeAssets(exchangeName);
      return response;
    },
    {
      refreshDeps: [exchangeName],
      ready: !!exchangeName && !defaultData,
    },
  );

  const columns: TableColumn[] = [
    {
      key: 'index',
      title: t('exchange_page.volume_table.no'),
      render: (value: any, item: ExchangeAsset, index: number) => (
        <Text className="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]">
          {index + 1}
        </Text>
      ),
    },
    {
      key: 'currency',
      title: t('exchange_page.volume_table.currency'),
      render: (value: any, item: ExchangeAsset, index: number) => (
        <HStack>
          <TextOrImageLogo text={item.assets_name} size={24} />
          <Text className="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]">
            {item.assets_name}
          </Text>
        </HStack>
      ),
    },
    {
      key: 'address',
      title: t('exchange_page.volume_table.address'),
      render: (value: any, item: ExchangeAsset, index: number) => (
        <Text className="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]">
          {item.wallet_address}
        </Text>
      ),
    },
    {
      key: 'balance',
      title: t('exchange_page.volume_table.balance'),
      render: (value: any, item: ExchangeAsset, index: number) => (
        <Text className="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]">
          {averageNumber(item.balance)} {item.symbol}
        </Text>
      ),
    },
    {
      key: 'balance_usd',
      title: t('exchange_page.volume_table.value'),
      render: (value: any, item: ExchangeAsset, index: number) => (
        <Text className="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]">
          ${averageNumber(item.balance_usd)}
        </Text>
      ),
    },
  ];
  return (
    <>
      <Text className="text-[#0A0A0A] text-[18px] font-not-italic font-600 leading-[24px]">
        {t('exchange_page.detail.Crypto_Exchanges_Assets_Transparency')}
      </Text>
      <Box className="border border-[#E6E6E6] rounded-[12px] bg-white py-0 overflow-hidden">
        <TableUi
          columns={columns}
          data={volumeData}
          loading={false}
          handleSort={() => {}}
          headerClassName="px-[12px]"
          thTextClassName="text-[#4D4D4D] text-center text-[14px] font-not-italic font-400 leading-[20px]"
          tdTextClassName="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]"
        />
      </Box>
    </>
  );
};

export default VolumeTable;
