import { getExchangeAssets, getExchangeVolumeChart } from "../../../../services/crypto_exchange/crypto_exchange.api";
import VolumeTable from "./VolumeTable";

const fetchData = async (exchangeName: string) => {
  try {
    const response = await getExchangeAssets(exchangeName);
    return response;
  } catch (error) {
    // console.error(error);
    return undefined;
  }
};

export default async function VolumeTableIndex({ exchangeName }: { exchangeName: string }) {
  const data = await fetchData(exchangeName);
  return <VolumeTable defaultData={data} exchangeName={exchangeName} />;
}
