'use client';
import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import { Text } from '@quantum/components/ui/text';
import { useRequest } from 'ahooks';
import React from 'react';
import { useTranslation } from 'react-i18next';
import ChangeValue from '../../../../components/ChangeValue';
import TableUi, { type TableColumn } from '../../../../components/TableUi/TableUi';
import TextOrImageLogo from '../../../../components/TextOrImageLogo';
import { getExchangeSpotMarkets } from '../../../../services/crypto_exchange/crypto_exchange.api';
import type { MarketSymbol } from '../../../../services/crypto_exchange/crypto_exchange.types';
import { averageNumber } from '../../../../uitls/number';

const LiquidityTable: React.FC<{ defaultData?: MarketSymbol[]; exchangeName: string }> = ({
  defaultData,
  exchangeName,
}) => {
  const { t } = useTranslation();

  const { data: liquidityData = defaultData } = useRequest(
    async () => {
      const response = await getExchangeSpotMarkets(exchangeName);
      return response;
    },
    {
      refreshDeps: [exchangeName],
      ready: !!exchangeName && !defaultData,
    },
  );

  const columns: TableColumn[] = [
    {
      key: 'index',
      title: t('exchange_page.liquidity_table.no'),
      render: (value: any, item: MarketSymbol, index: number) => (
        <Text className="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]">
          {index + 1}
        </Text>
      ),
    },
    {
      key: 'currency',
      title: t('exchange_page.liquidity_table.currency'),
      render: (value: any, item: MarketSymbol, index: number) => (
        <HStack>
          <TextOrImageLogo text={item.quota} size={24} />
          <Text className="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]">
            {item.base}
          </Text>
        </HStack>
      ),
    },
    {
      key: 'pair',
      title: t('exchange_page.liquidity_table.pair'),
      align: 'center',
      render: (value: any, item: MarketSymbol, index: number) => (
        <HStack className="m-auto h-[36px] flex w-[134px] px-[16px] py-[8px] justify-center items-center gap-[4px] rounded-[4px] bg-[#F7F7F7]">
          <Text className="text-[#0A0A0A] font-[Poppins] text-[14px] font-not-italic font-500 leading-[20px]">
            {item.base}/{item.quota}
          </Text>
        </HStack>
      ),
    },
    {
      key: 'price',
      title: t('exchange_page.liquidity_table.price'),
      align: 'center',
    },
    {
      key: 'cost_to_move_up_usd',
      title: t('exchange_page.liquidity_table.depth_up'),
      align: 'center',
      render: (value: any, item: MarketSymbol, index: number) => (
        <Text className="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]">
          ${averageNumber(item.cost_to_move_up_usd)}
        </Text>
      ),
    },
    {
      key: 'cost_to_move_down_usd',
      title: t('exchange_page.liquidity_table.depth_down'),
      align: 'center',
      render: (value: any, item: MarketSymbol, index: number) => (
        <Text className="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]">
          ${averageNumber(item.cost_to_move_down_usd)}
        </Text>
      ),
    },
    {
      key: 'volume',
      title: t('exchange_page.liquidity_table.volume'),
      align: 'right',
      render: (value: any, item: MarketSymbol, index: number) => (
        <Text className="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]">
          ${averageNumber(item.volume_24h_usd)}
        </Text>
      ),
    },
    {
      key: 'volumePercentage',
      title: t('exchange_page.liquidity_table.spread'),
      align: 'right',
      width: '100px',
      render: (value: any, item: MarketSymbol, index: number) => (
        <HStack className="justify-end items-center px-[12px] text-[#4D4D4D] text-[14px] font-not-italic font-500 leading-[20px]">
          <ChangeValue change={`${averageNumber(item.bid_ask_spread_percentage, 2)}%`} />
        </HStack>
      ),
    },
  ];

  return (
    <>
      <Text className="text-[#0A0A0A] text-[18px] font-not-italic font-600 leading-[24px]">
        {t('exchange_page.detail.spot_liquidity')}
      </Text>
      <Box className="border border-[#E6E6E6] rounded-[12px] bg-white py-0 overflow-hidden">
        <TableUi
          columns={columns}
          data={liquidityData}
          loading={false}
          handleSort={() => {}}
          headerClassName="px-[12px]"
          thClassName="px-[12px] py-0"
          thTextClassName="text-[#4D4D4D] text-center text-[14px] font-not-italic font-400 leading-[20px]"
          tdTextClassName="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]"
        />
      </Box>
    </>
  );
};

export default LiquidityTable;
