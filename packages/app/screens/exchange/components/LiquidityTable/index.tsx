import { getExchangeSpotMarkets } from '../../../../services/crypto_exchange/crypto_exchange.api';
import LiquidityTable from './LiquidityTable';
const fetchData = async (exchangeName: string) => {
  try {
    const response = await getExchangeSpotMarkets(exchangeName);
    return response;
  } catch (error) {
    // console.error(error);
    return undefined;
  }
};
export default async function LiquidityTableIndex({ exchangeName }: { exchangeName: string }) {
  const data = await fetchData(exchangeName);
  return (
    <>
      <LiquidityTable defaultData={data} exchangeName={exchangeName} />
    </>
  );
}
