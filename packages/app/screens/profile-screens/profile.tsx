'use client';

import React from 'react';
import { View, Text, Image, ScrollView } from 'react-native';
import { Button, ButtonText } from '@quantum/components/ui/button';
import { VStack } from '@quantum/components/ui/vstack';
import { HStack } from '@quantum/components/ui/hstack';
import { Heading } from '@quantum/components/ui/heading';
import useRouter from '@unitools/router';

export const Profile = () => {
  const router = useRouter();

  // 模拟用户数据
  const userData = {
    name: '张三',
    avatar: 'https://via.placeholder.com/100',
    email: 'zhang<PERSON>@example.com',
    phone: '138****1234',
    joinDate: '2023年1月1日',
  };

  return (
    <ScrollView className="flex-1 bg-gray-50 dark:bg-gray-900">
      <VStack space="md" className="p-4">
        {/* 用户基本信息 */}
        <View className="bg-white rounded-lg p-4 shadow-sm">
          <HStack space="md" alignItems="center">
            <Image source={{ uri: userData.avatar }} alt="用户头像" className="w-20 h-20 rounded-full" />
            <VStack space="xs">
              <Heading size="lg">{userData.name}</Heading>
              <Text className="text-gray-500">{userData.email}</Text>
            </VStack>
          </HStack>
        </View>

        {/* 个人信息部分 */}
        <View className="bg-white rounded-lg p-4 shadow-sm">
          <Heading size="md" className="mb-4">
            个人信息
          </Heading>
          <VStack space="md">
            <HStack justifyContent="space-between">
              <Text className="text-gray-500">手机号码</Text>
              <Text>{userData.phone}</Text>
            </HStack>
            <HStack justifyContent="space-between">
              <Text className="text-gray-500">注册日期</Text>
              <Text>{userData.joinDate}</Text>
            </HStack>
          </VStack>
        </View>

        {/* 操作按钮 */}
        <VStack space="md" className="mt-4">
          <Button
            className="w-full"
            onPress={() => {
              // 编辑个人资料
            }}
          >
            <ButtonText>编辑个人资料</ButtonText>
          </Button>

          <Button
            className="w-full"
            variant="outline"
            onPress={() => {
              router.push('/');
            }}
          >
            <ButtonText>返回首页</ButtonText>
          </Button>

          <Button
            className="w-full"
            variant="destructive"
            onPress={() => {
              // 退出登录
            }}
          >
            <ButtonText>退出登录</ButtonText>
          </Button>

          <Button
            className="w-full"
            onPress={() => {
              router.push('auth/signin');
            }}
          >
            <ButtonText>Sign in</ButtonText>
          </Button>
        </VStack>
      </VStack>
    </ScrollView>
  );
};
