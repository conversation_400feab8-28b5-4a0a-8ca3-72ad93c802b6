'use client';
import { Grid, GridItem } from '@quantum/components/ui/grid';
import FearGreedIndexGraph from '../../components/cryptoStat/FearGreedIndex/FearGreedIndexGraph';
import LiquidationHistory from '../../components/cryptoStat/LiquidationHistory/LiquidationHistory';
import EtfPremiumDiscountHistory from '../../components/cryptoStat/EtfPremiumDiscountHistory/EtfPremiumDiscountHistory';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { useTranslation } from 'react-i18next';
import { VStack } from '@quantum/components/ui/vstack';
export default function CryptoStatOverview() {
  const { t } = useTranslation();
  return (
    <VStack className="gap-2">
      <Text>{t('crypto_stat.overview.content')}</Text>
      <Grid
        className="flex-1 gap-4"
        _extra={{
          className: 'grid-cols-12',
        }}
      >
        <GridItem
          _extra={{
            className: 'col-span-12 md:col-span-6',
          }}
        >
          <div style={{ width: '100%', height: 400 }}>
            <FearGreedIndexGraph data={null} />
          </div>
        </GridItem>
        <GridItem
          _extra={{
            className: 'col-span-12 md:col-span-6',
          }}
        >
          <EtfPremiumDiscountHistory defaultData={null} />
        </GridItem>
      </Grid>
    </VStack>
  );
}
