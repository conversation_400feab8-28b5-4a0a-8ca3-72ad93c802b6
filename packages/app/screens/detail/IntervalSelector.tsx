import { HStack } from '@quantum/components/ui/hstack';
import { Pressable } from '@quantum/components/ui/pressable';
import { Text } from '@quantum/components/ui/text';
import { useState } from 'react';
import { AliKlineInterval } from '../../services/kline/kline.types';

export default function IntervalSelector({ value, onChange }: { value: string | number, onChange: (value: string | number) => void }) {

  const list = [
    {
      label: '5min',
      value: AliKlineInterval.MIN_5,
    },
    {
      label: '30min',
      value: AliKlineInterval.MIN_30,
    },
    {
      label: '1h',
      value: AliKlineInterval.HOUR_1,
    },
    {
      label: '2h',
      value: AliKlineInterval.HOUR_2,
    },
    {
      label: '1d',
      value: AliKlineInterval.DAY_1,
    },
    {
      label: '1w',
      value: AliKlineInterval.WEEK_1,
    },
    {
      label: '1m',
      value: AliKlineInterval.MONTH_1,
    },
  ];
  const handleIntervalChange = (value: string | number) => {
    onChange(value);
  };

  return (
    <HStack className="p-[4px] items-center gap-[2px] rounded-[8px] bg-[rgba(0,0,0,0.04)] backdrop-blur-[2px]">
      {list.map((item, index) => (
        <Pressable
          key={index}
          onPress={() => handleIntervalChange(item.value)}
          className={`h-[28px] px-[16px] rounded-[4px] ${
            value === item.value ? 'bg-[#FFF] shadow-[0px_0px_6px_0px_rgba(0,0,0,0.10)]' : 'bg-transparent'
          }`}
        >
          <HStack className="h-full items-center justify-center">
            <Text
              className={`text-[12px] ${
                value === item.value ? 'text-[#0A0A0A] font-[500]' : 'text-[#808080] font-[400]'
              }`}
            >
              {item.label}
            </Text>
          </HStack>
        </Pressable>
      ))}
    </HStack>
  );
}
