'use client';
import Comments from '@quantum/app/screens/home/<USER>';
import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { RiLineChartLine } from 'react-icons/ri';
import IntervalSelector from './IntervalSelector';

import { Pressable } from '@quantum/components/ui/pressable';
import { Skeleton } from '@quantum/components/ui/skeleton';
import { useRequest } from 'ahooks';
import { observer } from 'mobx-react-lite';
import { useMemo, useState } from 'react';
import { MdOutlineCandlestickChart } from 'react-icons/md';
import { ButtonStar } from '../../components/ButtonStar/ButtonStar';
import CandlestickGraph from '../../components/CandlestickGraph/CandlestickGraph';
import ChangeValue from '../../components/ChangeValue';
import LineGraph from '../../components/LineGraph/LineGraph';
import TextOrImageLogo from '../../components/TextOrImageLogo';
import { marketList } from '../../consts';
import useParams from '../../hooks/useParams';
import { useToggleStar } from '../../hooks/useToggleStar';
import { getAssetDetail } from '../../services/asset/asset.api';
import { getKline } from '../../services/kline/kline.api';
import { AliKlineInterval } from '../../services/kline/kline.types';
import { getWatchlistStatus } from '../../services/watchlist/watchlist.api';
import { Market } from '../../services/watchlist/watchlist.types';
import type { AssetDetail } from '../../services/asset/asset.types';
import { authStore } from '../../store/auth.store';
import { watchlistStore } from '../../store/watchlist.store';
import { sleep } from '../../uitls';
import { averageNumber } from '../../uitls/number';
import { calculatePriceChanges } from '../../utils/priceChange';
import { useTranslation } from 'react-i18next';
function Detail({ defaultData }: { defaultData: AssetDetail | null }) {
  const { t, ready, i18n } = useTranslation();
  const [selectedInterval, setSelectedInterval] = useState<AliKlineInterval>(AliKlineInterval.MIN_5);

  const [chartType, setChartType] = useState<'line' | 'candlestick'>('line');
  const params = useParams() as any;

  const { data = defaultData, loading } = useRequest(
    async () => {
      if (!params?.market || !params?.symbol) return null;

      const res = await getAssetDetail({
        market: params.market as Market,
        symbol: params.symbol,
      });

      return res;
    },
    {
      pollingInterval: 5000,
      ready: !!ready,
      refreshDeps: [params.market, params.symbol, i18n.language],
    },
  );
  

  const infoList = useMemo(() => {
    if (!data) return [];

    return [
      {
        label: t('detail.volume'),
        value: averageNumber(data.value),
      },
      {
        label: t('detail.market_cap'),
        value: averageNumber(data.market_cap),
      },
      {
        label: data?.market === Market.CRYPTO ? t('detail.fully_diluted_market_cap') : t('detail.pe_label'),
        value:
          data?.market === Market.CRYPTO ? averageNumber(data.fdv || 0) || '-' : averageNumber(data.pe || 0) || '-',
      },
      {
        label: data?.market === Market.CRYPTO ? t('detail.max_supply') : t('detail.total_share_label'),
        value:
          data?.market === Market.CRYPTO
            ? averageNumber(data.total_supply || 0) || '-'
            : averageNumber(data.total_share || 0) || '-',
      },
    ];
  }, [data, t]);

  const { data: klineData = [], loading: klineDataLoading } = useRequest(
    async () => {
      const res = await getKline(params.market as Market, params.symbol, selectedInterval, 1000);
      return res;
    },
    {
      refreshDeps: [selectedInterval],
      pollingInterval: 5000,
    },
  );
  const { data: yearData = [], loading: yearDataLoading } = useRequest(async () => {
    const res = await getKline(params.market as Market, params.symbol, AliKlineInterval.DAY_1, 365);
    return res;
  });

  const detailList = useMemo(() => {
    const data = calculatePriceChanges(yearData);
    return [
      {
        label: t('detail.one_day'),
        change: data.oneDayChange,
      },
      {
        label: t('detail.one_week'),
        change: data.oneWeekChange,
      },
      {
        label: t('detail.three_months'),
        change: data.threeMonthChange,
      },
      {
        label: t('detail.one_year'),
        change: data.oneYearChange,
      },
    ];
  }, [klineData, yearData, t]);
  const marketLabel = useMemo(() => {
    return marketList.find((item) => item.value === params?.market)?.i18nKey;
  }, [data?.market]);

  const { runAsync: callOnToggleStar } = useToggleStar(params.market, params.symbol);

  const { data: defaultStar = false } = useRequest(
    async () => {
      if (!authStore.isLoggedIn) return false;
      const res = await getWatchlistStatus({
        market: params.market as Market,
        symbol: params.symbol,
      });
      return res.result;
    },
    {
      refreshDeps: [params.market, params.symbol, authStore.isLoggedIn],
    },
  );

  const handleOnToggleStar = async (value: boolean) => {
    await callOnToggleStar(value);
    await sleep(1000);
    await watchlistStore.watchlist.reload();
  };

  return (
    <VStack className="gap-9">
      <VStack className="gap-4">
        <Text className="text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[400] leading-[20px]">
          {t(marketLabel || '')} / {params?.symbol}
        </Text>
        <HStack className="gap-[55px]">
          <VStack className="gap-4">
            <HStack className="gap-3 items-center">
              <TextOrImageLogo text={String(params?.symbol)} size={40} />
              {loading && !data ? (
                <Skeleton className="w-[100px] h-[30px]" />
              ) : (
                <Text className="text-[#0A0A0A] font-Inter text-[24px] font-not-italic font-[700] leading-normal tracking--0.96px">
                  {data?.name}
                </Text>
              )}
              <ButtonStar
                className="flex items-center justify-center w-[32px] h-[32px] rounded-[6px] bg-[#F7F7F7] w-[32px] h-[32px]"
                size={20}
                defaultStar={defaultStar}
                onToggleStar={handleOnToggleStar}
              />
            </HStack>

            <VStack className="gap-2">
              {loading && !data ? (
                <Skeleton className="w-[100px] h-[40px]" />
              ) : (
                <Text className="text-[#0A0A0A] font-Inter text-[40px] font-not-italic font-[700] leading-[56px]">
                  ${averageNumber(data?.price || 0)}
                </Text>
              )}
              <HStack className="gap-3 items-center">
                {loading && !data ? (
                  <Skeleton className="w-[150px] h-[20px]" />
                ) : (
                  <>
                    <ChangeValue className="text-[14px] leading-[1]" change={detailList?.[2]?.change || '0'} />
                    <Text className="text-[#808080] font-Inter text-[14px] font-not-italic font-[400] leading-[20px]">
                      {t('detail.past_3_months')}
                    </Text>
                  </>
                )}
              </HStack>
            </VStack>

            <VStack className="mt-[45px] gap-6">
              {loading && !data ? (
                <>
                  {Array.from({ length: 4 }).map((_, index) => (
                    <VStack key={'skeleton' + index} className="gap-[2px]">
                      <Skeleton className="w-[100px] h-[20px]" />
                      <Skeleton className="w-[80px] h-[20px]" />
                    </VStack>
                  ))}
                </>
              ) : (
                <>
                  {infoList.map((item, index) => (
                    <VStack key={index} className="gap-[2px]">
                      <Text className="text-[#808080] font-Inter text-[12px] font-not-italic font-[400] leading-[16px]">
                        {item.label}
                      </Text>
                      <Text className="text-[#4D4D4D] font-Inter text-[12px] font-not-italic font-[600] leading-[16px]">
                        {item.value}
                      </Text>
                    </VStack>
                  ))}
                </>
              )}
            </VStack>
          </VStack>

          <Box className="flex-1">
            <VStack className="gap-7">
              <HStack className="gap-[10px] justify-end">
                <Pressable onPress={() => setChartType('candlestick')}>
                  <Box className="w-[36px] h-[36px] items-center justify-center rounded-[6px] bg-[#F7F7F7]">
                    <MdOutlineCandlestickChart
                      size={20}
                      className={chartType === 'candlestick' ? 'text-[#0a0a0a]' : 'text-[#808080]'}
                    />
                  </Box>
                </Pressable>
                <Pressable onPress={() => setChartType('line')}>
                  <Box className="w-[36px] h-[36px] items-center justify-center rounded-[6px] bg-[#F7F7F7]">
                    <RiLineChartLine size={20} className={chartType === 'line' ? 'text-[#0a0a0a]' : 'text-[#808080]'} />
                  </Box>
                </Pressable>

                <IntervalSelector
                  value={selectedInterval}
                  onChange={(value) => setSelectedInterval(value as AliKlineInterval)}
                />
              </HStack>

              <VStack className="gap-[17px]">
                <Box className="h-[327px] w-full">
                  {chartType === 'line' ? (
                    <LineGraph data={klineData} loading={klineDataLoading} />
                  ) : (
                    <CandlestickGraph data={klineData} />
                  )}
                </Box>
                <HStack className="flex w-full px-[16px] py-[8px] justify-between items-center rounded-[8px] bg-[#F7F7F7]">
                  {yearDataLoading ? (
                    <>
                      {Array.from({ length: 4 }).map((_, index) => (
                        <Skeleton key={index} className="w-[100px] h-[20px]" />
                      ))}
                    </>
                  ) : (
                    <>
                      {detailList.map((item, index) => (
                        <HStack key={index} className="gap-[2px] items-center">
                          <Text className="text-[#808080] font-Inter text-[12px] font-not-italic font-[400] leading-[16px]">
                            {item.label}
                          </Text>
                          <Text
                            className={`text-[${
                              item.change.includes('+') ? '#05C697' : '#d93526'
                            }] font-Inter text-[12px] font-not-italic font-[600] leading-[1]`}
                          >
                            {item.change}
                          </Text>
                        </HStack>
                      ))}
                    </>
                  )}
                </HStack>
              </VStack>
            </VStack>
          </Box>
        </HStack>
      </VStack>
    </VStack>
  );
}

export default observer(Detail);
