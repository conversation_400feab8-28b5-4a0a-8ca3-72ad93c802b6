'use client';

import Image from '@unitools/image';
import { ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';

// 导入图标资源
import AutoCategorizationIcon from '@quantum/shared/assets/features/portfolio-tracker/auto-categorization.svg';
import BarChartBoxLineIcon from '@quantum/shared/assets/features/portfolio-tracker/bar-chart-box-line.svg';
import BardFillIcon from '@quantum/shared/assets/features/portfolio-tracker/bard-fill.svg';
import LineChartLineIcon from '@quantum/shared/assets/features/portfolio-tracker/line-chart-line.svg';
import NotificationLineIcon from '@quantum/shared/assets/features/portfolio-tracker/notification-2-line.svg';
import PredictiveAnalyticsIcon from '@quantum/shared/assets/features/portfolio-tracker/predictive-analytics.svg';
import RebalancingAlertsIcon from '@quantum/shared/assets/features/portfolio-tracker/rebalancing-alerts.svg';
import RiskProfilingIcon from '@quantum/shared/assets/features/portfolio-tracker/risk-profiling.svg';
import SentimentAnalysisIcon from '@quantum/shared/assets/features/portfolio-tracker/sentiment-analysis.svg';
import ShieldCheckLineIcon from '@quantum/shared/assets/features/portfolio-tracker/shield-check-line.svg';

// 导入资产图片资源
import CryptoAssetsImage from '@quantum/shared/assets/features/portfolio-tracker/crypto-assets.svg';
import CustomAssetsImage from '@quantum/shared/assets/features/portfolio-tracker/futures-assets.svg';
import StockAssetsImage from '@quantum/shared/assets/features/portfolio-tracker/stock-assets.svg';



import Who1Image from '@quantum/shared/assets/features/portfolio-tracker/user-types/who-1.png';
import Who2Image from '@quantum/shared/assets/features/portfolio-tracker/user-types/who-2.png';
import Who3Image from '@quantum/shared/assets/features/portfolio-tracker/user-types/who-3.png';
import Who4Image from '@quantum/shared/assets/features/portfolio-tracker/user-types/who-4.png';

// 导入组件
import HeroSection from '../components/HeroSection';
import UserTypesSection from '../components/UserTypesSection';
import CTASection from './components/sections/CTASection';
import ExperienceSection from './components/sections/ExperienceSection';
import IntroSection from './components/sections/IntroSection';
// 导入公共FAQ组件
import Coments from '../components/Coments/Coments';
import DescriptionIcon from '../components/DescriptionIcon/DescriptionIcon';
import DescriptionImage from '../components/DescriptionImage/DescriptionImage';
import DescriptionLine from '../components/DescriptionLine/DescriptionLine';
import { FAQSection } from '../components/faq';


const PortfolioTracker = () => {
  const { t } = useTranslation();
  
  // 特性数据
  const features = [

    {
      icon: <Image source={BardFillIcon} alt="AI Powered" width={32} height={32} />,
      title: t('features_page.portfolio_tracker.features.ai_insights.title'),
      description: t('features_page.portfolio_tracker.features.ai_insights.description')
    },
    {
      icon: <Image source={BarChartBoxLineIcon} alt="Portfolio Tracking" width={32} height={32} />,
      title: t('features_page.portfolio_tracker.features.unified_dashboard.title'),
      description: t('features_page.portfolio_tracker.features.unified_dashboard.description')
    },
    {
      icon: <Image source={NotificationLineIcon} alt="Price Alerts" width={32} height={32} />,
      title: t('features_page.portfolio_tracker.features.smart_alerts.title'),
      description: t('features_page.portfolio_tracker.features.smart_alerts.description')
    },
    {
      icon: <Image source={LineChartLineIcon} alt="Portfolio Analysis" width={32} height={32} />,
      title: t('features_page.portfolio_tracker.features.data_driven.title'),
      description: t('features_page.portfolio_tracker.features.data_driven.description')
    },
    {
      icon: <Image source={ShieldCheckLineIcon} alt="Security" width={32} height={32} />,
      title: t('features_page.portfolio_tracker.features.security.title'),
      description: t('features_page.portfolio_tracker.features.security.description')
    },
  ];

  // AI 特性数据
  const aiFeatures = [
    {
      icon: <Image source={RebalancingAlertsIcon} alt="Rebalancing Alerts" width={32} height={32} />,
      title: t('features_page.portfolio_tracker.ai_features.rebalancing.title'),
      description: t('features_page.portfolio_tracker.ai_features.rebalancing.description')
    },
    {
      icon: <Image source={SentimentAnalysisIcon} alt="Sentiment Analysis" width={32} height={32} />,
      title: t('features_page.portfolio_tracker.ai_features.sentiment.title'),
      description: t('features_page.portfolio_tracker.ai_features.sentiment.description')
    },
    {
      icon: <Image source={AutoCategorizationIcon} alt="Auto Categorization" width={32} height={32} />,
      title: t('features_page.portfolio_tracker.ai_features.categorization.title'),
      description: t('features_page.portfolio_tracker.ai_features.categorization.description')
    },
    {
      icon: <Image source={PredictiveAnalyticsIcon} alt="Predictive Analytics" width={32} height={32} />,
      title: t('features_page.portfolio_tracker.ai_features.predictive.title'),
      description: t('features_page.portfolio_tracker.ai_features.predictive.description')
    },
    {
      icon: <Image source={RiskProfilingIcon} alt="Risk Profiling" width={32} height={32} />,
      title: t('features_page.portfolio_tracker.ai_features.risk.title'),
      description: t('features_page.portfolio_tracker.ai_features.risk.description')
    }
  ];

  // 资产数据
  const assets = [
    {
      title: t('features_page.portfolio_tracker.assets.stocks.title'),
      description: t('features_page.portfolio_tracker.assets.stocks.description'),
      image: StockAssetsImage
    },
    {
      title: t('features_page.portfolio_tracker.assets.crypto.title'),
      description: t('features_page.portfolio_tracker.assets.crypto.description'),
      image: CryptoAssetsImage
    },
    {
      title: t('features_page.portfolio_tracker.assets.custom.title'),
      description: t('features_page.portfolio_tracker.assets.custom.description'),
      image: CustomAssetsImage
    }
  ];

  // 用户类型数据
  const userTypes = [
    {
      title: t('features_page.portfolio_tracker.user_types.type1'),
      backgroundColor: '#CAD4D5',
      imageUrl: Who1Image
    },
    {
      title: t('features_page.portfolio_tracker.user_types.type2'),
      backgroundColor: '#9BA5BA',
      imageUrl: Who2Image
    },
    {
      title: t('features_page.portfolio_tracker.user_types.type3'),
      backgroundColor: '#CECAD5',
      imageUrl: Who3Image
    },
    {
      title: t('features_page.portfolio_tracker.user_types.type4'),
      backgroundColor: '#D5D4CA',
      imageUrl: Who4Image
    },
  ];

  // FAQ 数据
  const faqs = [
    {
      question: t('features_page.portfolio_tracker.faq.ai_advisor_q'),
      answer: t('features_page.portfolio_tracker.faq.ai_advisor_a')
    },
    {
      question: t('features_page.portfolio_tracker.faq.data_security_q'),
      answer: t('features_page.portfolio_tracker.faq.data_security_a')
    },
    {
      question: t('features_page.portfolio_tracker.faq.asset_types_q'),
      answer: t('features_page.portfolio_tracker.faq.asset_types_a')
    },
    {
      question: t('features_page.portfolio_tracker.faq.ai_features_q'),
      answer: t('features_page.portfolio_tracker.faq.ai_features_a')
    }
  ];

  // 用户评价数据
  const reviews = [
    {
      userName: t('features_page.portfolio_tracker.reviews.user1_name'),
      userRole: t('features_page.portfolio_tracker.reviews.user1_role'),
      reviewContent: t('features_page.portfolio_tracker.reviews.user1_content'),
      rating: 5
    },
    {
      userName: t('features_page.portfolio_tracker.reviews.user2_name'),
      userRole: t('features_page.portfolio_tracker.reviews.user2_role'),
      reviewContent: t('features_page.portfolio_tracker.reviews.user2_content'),
      rating: 5
    },
    {
      userName: t('features_page.portfolio_tracker.reviews.user3_name'),
      userRole: t('features_page.portfolio_tracker.reviews.user3_role'),
      reviewContent: t('features_page.portfolio_tracker.reviews.user3_content'),
      rating: 4
    },
    {
      userName: t('features_page.portfolio_tracker.reviews.user4_name'),
      userRole: t('features_page.portfolio_tracker.reviews.user4_role'),
      reviewContent: t('features_page.portfolio_tracker.reviews.user4_content'),
      rating: 5
    }
  ];

  return (
    <ScrollView className="bg-white">
      {/* Hero部分 */}
      <HeroSection 
        title={t('features_page.portfolio_tracker.hero.title')}
        subtitle={t('features_page.portfolio_tracker.hero.subtitle')}
        description={t('features_page.portfolio_tracker.hero.description')}
        ctaText={t('features_page.portfolio_tracker.hero.cta')}
      />

      {/* 介绍部分 */}
      <IntroSection />

      <DescriptionLine
        title={t('features_page.portfolio_tracker.why_choose.title')}
        features={features}
      />

      {/* AI特性部分 */}
      <DescriptionIcon title={t('features_page.portfolio_tracker.ai_features.title')} features={aiFeatures} />

      {/* 支持的资产部分 */}
      <DescriptionImage title={t('features_page.portfolio_tracker.assets.title')} steps={assets} />


      {/* 用户类型部分 */}
      <UserTypesSection userTypes={userTypes} />

      {/* 体验部分 */}
      <ExperienceSection />

      {/* 用户评价部分 */}
      <Coments reviews={reviews} />

      {/* CTA部分 */}
      <CTASection />
      
      {/* FAQ部分 - 使用公共组件 */}
      <FAQSection faqs={faqs} title={t('features_page.portfolio_tracker.faq.title')} />

    </ScrollView>
  );
};

export default PortfolioTracker;
