'use client';

import React from 'react';
import { View } from 'react-native';
import { Box } from '@quantum/components/ui/box';
import { VStack } from '@quantum/components/ui/vstack';
import { HStack } from '@quantum/components/ui/hstack';
import { Text } from '@quantum/components/ui/text';
import { Button, ButtonText } from '@quantum/components/ui/button';

import experienceApple from '@quantum/shared/assets/features/portfolio-tracker/experience/experience-apple.png';
import experienceAndroid from '@quantum/shared/assets/features/portfolio-tracker/experience/experience-android.png';
import experienceWeb from '@quantum/shared/assets/features/portfolio-tracker/experience/experience-web.png';

import Image from '@unitools/image';
import Link from '@unitools/link';

// Experience Section组件
const ExperienceSection = () => {
  return (
    <Box className="px-[142px] py-[120px] bg-[#ffffff]">
      <VStack className="gap-[120px] items-center">
        <VStack className="gap-[24px] items-center">
          <Text className="text-[40px] font-700 leading-[48px] text-[#0A0A0A] max-w-[648px] text-center">
            Experience Next-Gen Portfolio Tracking
          </Text>
          <Text className="text-[20px] font-400 leading-[30px] text-[#808080] max-w-[686px] text-center">
            No spreadsheets. No switching apps. Just one smart tool to track your wealth. Join thousands who trust our
            Al-powered engine to stay in control of their investments.
          </Text>
        </VStack>
        <VStack className="gap-[80px] items-center">
          {/* 占位符，在实际项目中可以添加应用截图或设备模型 */}
          <Box className="h-[532px] w-[920px] rounded-xl bg-[#202020]" />

          <VStack className="gap-[56px] items-center">
            <HStack space="lg">
              <HStack className="p-[12px] justify-center gap-[10px] rounded-[12px] border-[1px] border-solid border-[#0000001A] items-center">
                <Image source={experienceApple} width={32} height={32} alt="experienceApple" />
                <Text className="text-[##808080]">Coming Soon</Text>
              </HStack>
              <HStack className="p-[12px] justify-center gap-[10px] rounded-[12px] border-[1px] border-solid border-[#0000001A items-center">
                <Image source={experienceWeb} width={32} height={32} alt="experienceWeb" />
                <Text className="text-[#0A0A0A]">Available on Web Now</Text>
              </HStack>
              <HStack className="p-[12px] justify-center gap-[10px] rounded-[12px] border-[1px] border-solid border-[#0000001A] items-center">
                <Image source={experienceAndroid} width={32} height={32} alt="experienceAndroid" />
                <Text className="text-[##808080]">Coming Soon</Text>
              </HStack>
            </HStack>

            <Link href="/">
              <Button className="w-[233px] h-[56px] bg-[#05C697] rounded-[56px]" variant="solid">
                <ButtonText className="text-white font-semibold">Get started for free</ButtonText>
              </Button>
            </Link>
          </VStack>
        </VStack>
      </VStack>
    </Box>
  );
};

export default ExperienceSection;
