'use client';

import React from 'react';
import { Box } from '@quantum/components/ui/box';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import IntroSectionBg from '@quantum/shared/assets/features/portfolio-tracker/intro-section/intro-section-bg.png';
import IconIntroSection from '@quantum/shared/assets/features/portfolio-tracker/intro-section/icon-IntroSection.svg';
import Image from '@unitools/image';

const IntroSection = () => {
  return (
    <Box className="px-[142px] pt-[120px] bg-white relative"
 
    >
      <VStack className="w-full items-center gap-[105px]">
        <VStack className="text-center gap-[24px] max-w-[686px]">
          <Text className="text-[40px] font-700 leading-[48px] text-[#0A0A0A] text-center">
            Smarter Investing Starts From Stockbits
          </Text>
          <Text className="text-[20px] font-400 leading-[30px] text-[#808080] text-center">
            Stay ahead of the market with our Al-powered portfolio tracker. Seamlessly monitor your crypto and stock
            holdings in one intelligent dashboard. Whether you're holding Bitcoin, Ethereum, Tesla, or ETFs, our app
            brings clarity and control to your entire investment strategy.
          </Text>
        </VStack>

        <Image source={IconIntroSection} alt="Intro Section Background" height={180} width={494} style={{marginBottom: 110}} />
        <Image
              source={require('@quantum/shared/assets/features/portfolio-tracker/intro-section/bg-IntroSection.png')}
              alt="bottom-bg"
              height={636}
              width={1288}
              style={{
                position: 'absolute',
                bottom: 0,
                left: '50%',
                transform: 'translateX(-50%)',
                zIndex: -1,
                maxWidth: 'none',
              }}
             
            />
      </VStack>
    </Box>
  );
};

export default IntroSection;
