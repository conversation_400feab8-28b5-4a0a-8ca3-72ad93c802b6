'use client';

import React from 'react';
import { View } from 'react-native';
import { Box } from '@quantum/components/ui/box';
import { VStack } from '@quantum/components/ui/vstack';
import { HStack } from '@quantum/components/ui/hstack';
import { Text } from '@quantum/components/ui/text';
import { Button, ButtonText } from '@quantum/components/ui/button';
import bgCta from '@quantum/shared/assets/features/portfolio-tracker/cta/bg-cta.png';
import iconCta from '@quantum/shared/assets/features/portfolio-tracker/cta/icon-cta.png';
import Image from '@unitools/image';
import Link from '@unitools/link';

// CTA Section组件
const CTASection = () => {
  return (
    <Box className="px-[142px] py-[120px] bg-[#ffffff]">
      <VStack className="gap-[120px] items-center">
        <VStack className="gap-[24px] items-center">
          <Text className="text-[40px] font-700 leading-[48px] text-[#0A0A0A] max-w-[684px] text-center">
            Track Better, Invest Smarter.
          </Text>
          <Text className="text-[20px] font-400 leading-[30px] text-[#808080] max-w-[686px] text-center">
            Your portfolio deserves an upgrade. Let AI help you make sharper decisions and avoid costly mistakes.
          </Text>

          {/* 视觉元素 */}
        </VStack>

        <VStack className="gap-[120px] w-full items-center">
          <Box className="w-full relative">
            <Image
              source={iconCta}
              width={824}
              height={'auto'}
              alt="icon-cta"
              style={{
                width: '100%',
                maxWidth: 824,
                height: 'auto',
                margin: 'auto',
              }}
            />
            <Image
              source={bgCta}
              width={'100%'}
              height={'auto'}
              alt="bg-cta"
              style={{
                width: '100%',
                maxWidth: '100%',
                height: 'auto',
                margin: 'auto',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                zIndex: -1,
              }}
            />
          </Box>

          <HStack className="gap-[9px]">
            <Link href="/features">
              <Button
                className="px-8 rounded-[56px] h-[56px] border border-[#00000026]"
                variant="outline"
                action="secondary"
              >
                <ButtonText className="text-[#0A0A0A] font-700 font-[18px] leading-[24px]">Learn More</ButtonText>
              </Button>
            </Link>
            <Link href="/">
              <Button className="px-8 bg-[#05C697] rounded-[56px] h-[56px]" variant="solid">
                <ButtonText className="text-white font-700 font-[18px] leading-[24px]">Get started for free</ButtonText>
              </Button>
            </Link>
          </HStack>
        </VStack>
      </VStack>
    </Box>
  );
};

export default CTASection;
