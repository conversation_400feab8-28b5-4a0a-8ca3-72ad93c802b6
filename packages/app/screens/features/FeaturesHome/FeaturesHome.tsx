'use client';

import React from 'react';
import { ScrollView } from 'react-native';
import Image from '@unitools/image';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { Button } from '@quantum/components/ui/button';
import { VStack } from '@quantum/components/ui/vstack';
import { HStack } from '@quantum/components/ui/hstack';
import { FAQSection } from '../components/faq';
import { useTranslation } from 'react-i18next';

// 导入图标资源
import DragDropLineIcon from '@quantum/shared/assets/features/features-home/drag-drop-line.svg';
import ExchangeFundsLineIcon from '@quantum/shared/assets/features/features-home/exchange-funds-line.svg';
import HistoryLineIcon from '@quantum/shared/assets/features/features-home/history-line.svg';
import AlexChenAvatar from '@quantum/shared/assets/features/features-home/alex-chen-avatar.png';
import BgImage from '@quantum/shared/assets/features/features-home/bg.png';
import { Grid, GridItem } from '@quantum/components/ui/grid';
import { RiCheckboxCircleFill, RiCheckFill } from 'react-icons/ri';

import avatar1 from '@quantum/shared/assets/features/features-home/avatars/1.svg';
import avatar2 from '@quantum/shared/assets/features/features-home/avatars/2.svg';
import avatar3 from '@quantum/shared/assets/features/features-home/avatars/3.svg';
import avatar4 from '@quantum/shared/assets/features/features-home/avatars/4.svg';
import avatar5 from '@quantum/shared/assets/features/features-home/avatars/5.svg';
import home1 from '@quantum/shared/assets/features/features-home/home-1.svg';
import home2 from '@quantum/shared/assets/features/features-home/home-2.svg';
import home3 from '@quantum/shared/assets/features/features-home/home-3.svg';
import home4 from '@quantum/shared/assets/features/features-home/home-4.svg';
import home5 from '@quantum/shared/assets/features/features-home/home-5.svg';
import { FeatureCard } from '../components/DescriptionLine/DescriptionLine';
import DescriptionCard from '../components/DescriptionCard/DescriptionCard';
import { useRequest } from 'ahooks';
import { authStore } from '../../../store/auth.store';
import useRouter from '@unitools/router';
import { observer } from 'mobx-react-lite';
import { joinWaitlist } from '../../../services/waitlist/waitlist.api';
import { waitlistStore } from '../../../store/waitlist.store';
import { useCustomToast } from '../../../components/Toast/ToastProvider';

const avatars = [
  {
    id: 1,
    name: '',
    avatar: avatar1,
    bg: '#FFC6C4',
  },
  {
    id: 2,
    name: '',
    avatar: avatar2,
    bg: '#A2A8CD',
  },
  {
    id: 3,
    name: '',
    avatar: avatar3,
    bg: '#D4B5AD',
  },
  {
    id: 4,
    name: '',
    avatar: avatar4,
    bg: '#D3D0CB',
  },
  {
    id: 5,
    name: '',
    avatar: avatar5,
    bg: '#BEA887',
  },
];

function FeaturesHome() {
  const { t } = useTranslation();

  // 为什么选择StockBits的特性数据
  const whyChooseFeatures = [
    {
      title: t('features_page.features_home.why_choose.unified_portfolio_tracker_title'),
      descriptions: [
        t('features_page.features_home.why_choose.unified_portfolio_tracker_desc1'),
        t('features_page.features_home.why_choose.unified_portfolio_tracker_desc2'),
      ],
      image: home1,
    },

    {
      title: t('features_page.features_home.why_choose.follow_traders_title'),
      descriptions: [
        t('features_page.features_home.why_choose.follow_traders_desc1'),
        t('features_page.features_home.why_choose.follow_traders_desc2'),
      ],
      image: home2,
    },
    {
      title: t('features_page.features_home.why_choose.ai_powered_title'),
      descriptions: [
        t('features_page.features_home.why_choose.ai_powered_desc1'),
        t('features_page.features_home.why_choose.ai_powered_desc2'),
      ],
      image: home3,
    },
    {
      title: t('features_page.features_home.why_choose.smart_signals_title'),
      descriptions: [
        t('features_page.features_home.why_choose.smart_signals_desc1'),
        t('features_page.features_home.why_choose.smart_signals_desc2'),
        t('features_page.features_home.why_choose.smart_signals_desc3'),
      ],
      image: home4,
    },

    {
      title: t('features_page.features_home.why_choose.free_tools_title'),
      descriptions: [t('features_page.features_home.why_choose.free_tools_desc')],
      image: home5,
    },
  ];

  // Alpha Launch 专属内容
  const alphaLaunchFeatures = [
    t('features_page.features_home.alpha_launch.feature1'),
    t('features_page.features_home.alpha_launch.feature2'),
    t('features_page.features_home.alpha_launch.feature3'),
  ];

  // 常见问题
  const faqs = [
    {
      question: t('features_page.features_home.faq.what_is_stockbits_q'),
      answer: t('features_page.features_home.faq.what_is_stockbits_a'),
    },
    {
      question: t('features_page.features_home.faq.ai_advisor_q'),
      answer: t('features_page.features_home.faq.ai_advisor_a'),
    },
    {
      question: t('features_page.features_home.faq.data_security_q'),
      answer: t('features_page.features_home.faq.data_security_a'),
    },
    {
      question: t('features_page.features_home.faq.connect_accounts_q'),
      answer: t('features_page.features_home.faq.connect_accounts_a'),
    },
    {
      question: t('features_page.features_home.faq.mobile_app_q'),
      answer: t('features_page.features_home.faq.mobile_app_a'),
    },
    {
      question: t('features_page.features_home.faq.markets_supported_q'),
      answer: t('features_page.features_home.faq.markets_supported_a'),
    },
  ];

  // 全球交易者信任的原因
  const trustReasons = [
    {
      icon: <Image source={DragDropLineIcon} alt="DragDropLineIcon" width={32} height={32} />,
      text: t('features_page.features_home.trust_reasons.reason1'),
    },
    {
      icon: <Image source={ExchangeFundsLineIcon} alt="ExchangeFundsLineIcon" width={32} height={32} />,
      text: t('features_page.features_home.trust_reasons.reason2'),
    },
    {
      icon: <Image source={HistoryLineIcon} alt="HistoryLineIcon" width={32} height={32} />,
      text: t('features_page.features_home.trust_reasons.reason3'),
    },
  ];
  const router = useRouter();

  const { showToast } = useCustomToast();
  const { run, loading } = useRequest(
    async () => {
      if (!authStore.isLoggedIn) {
        authStore.setShowLoginModal(true);
        return;
      }
      try {
        // const res = await joinWaitlist();
        // await waitlistStore.waitlistStatus.reload();
      } catch (error: any) {
        console.log(error);
        showToast({
          title: t('failed'),
          description: error.msg || t('join_failed'),
          status: 'error',
        });
      }
      router.push('/');
    },
    {
      manual: true,
    },
  );

  return (
    <ScrollView className="bg-white">
      {/* 第一部分：英雄部分 */}
      <Box className="p-[80px] relative overflow-hidden">
        <Box className="w-full absolute top-0 left-['50%'] translate-x-['-50%'] translate-y-['-50%']">
          <Image
            source={BgImage}
            alt="BgImage"
            width={1288}
            height={'auto'}
            style={{
              margin: 'auto',
            }}
          />
        </Box>

        <VStack className="max-w-[976px] mx-auto items-center gap-12">
          <VStack className="items-center gap-4">
            <Text className="text-[64px] font-700 leading-[72px] text-center text-[#0A0A0A]">
              {t('features_page.features_home.hero.title')}
            </Text>
            <Text className="text-[20px] leading-[30px] text-center text-[#808080]">
              {t('features_page.features_home.hero.subtitle')}
            </Text>
          </VStack>

          <Button className="px-8 py-4 rounded-[56px] h-[56px] min-w-[233px] bg-[#05C697]" onPress={run} loading={loading}>
            <Text className="text-[18px] font-700 text-white">{t('features_page.features_home.hero.cta')}</Text>
          </Button>

          <Box>
            <HStack className="items-center gap-2">
              <HStack className="gap-[-8px]">
                {/* 用户头像组 - 简化版本 */}
                {avatars.map((avatar, index) => (
                  <Box
                    key={index}
                    className="w-[32px] h-[32px] rounded-full border-2 border-white ml-[-10px]"
                    style={{ backgroundColor: avatar.bg }}
                  >
                    <Image source={avatar.avatar} alt="Avatar" width={32} height={28} />
                  </Box>
                ))}
              </HStack>
              <Text className="text-[14px] leading-[20px]  font-400 text-[#808080]">
                {t('features_page.features_home.hero.customers')}
              </Text>
            </HStack>
          </Box>
        </VStack>
      </Box>

      <Box className="w-full max-w-[1440px] mx-auto">
        {/* 第二部分：Why Traders Choose StockBits */}
        <DescriptionCard title={t('features_page.features_home.why_choose.title')} features={whyChooseFeatures} />

        <Box className="py-[120px] bg-white px-[142px]">
          <VStack className="gap-10">
            <HStack className="gap-4 items-center justify-between">
              <VStack className="gap-[22px] max-w-[648px]">
                <Text className="text-[40px] font-700 leading-[48px] text-[#0A0A0A]">
                  {t('features_page.features_home.alpha_launch.title')}
                </Text>
                <Text className="font-400 text-[20px] leading-[30px] text-[#808080]">
                  {t('features_page.features_home.alpha_launch.subtitle')}
                </Text>
              </VStack>
              <Button className="px-8 py-4 rounded-[40px]" style={{ backgroundColor: '#05C697' }} onPress={run} loading={loading}>
                <Text className="text-[18px] font-700 text-white">
                  {t('features_page.features_home.alpha_launch.cta')}
                </Text>
              </Button>
            </HStack>
            <Grid
              className="gap-6 w-full"
              _extra={{
                className: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
              }}
            >
              {alphaLaunchFeatures.map((feature, index) => (
                <GridItem
                  key={index}
                  _extra={{
                    className: 'col-span-1',
                  }}
                >
                  <VStack className="gap-4 border border-[#E6E6E6] rounded-[16px] p-6 h-full">
                    <RiCheckboxCircleFill className="text-[#05c697]" size={32} />
                    <Text className="text-[20px] font-500 leading-[1] text-[#0A0A0A] flex-1">{feature}</Text>
                  </VStack>
                </GridItem>
              ))}
            </Grid>
          </VStack>
        </Box>
      </Box>

      <Box className="py-[120px] bg-[#F8F8F8]">
        <VStack className="items-center gap-[64px]">
          <Text className="text-[40px] font-600 text-center leading-[52px] text-[#000000] max-w-[1156px]">
            {t('features_page.features_home.quote.text')}
          </Text>

          <HStack className="gap-[14px] items-center">
            <Box className="w-[56px] h-[56px] rounded-full bg-[#ffffff]">
              <Image source={AlexChenAvatar} alt="Alex Chen Avatar" width={56} height={56} />
            </Box>
            <Text className="font-400 text-[20px] leading-[30px] text-[#808080]">
              <Text className="font-700 text-[20px] leading-[30px] text-[#0a0a0a]">
                {t('features_page.features_home.quote.name')} ·
              </Text>{' '}
              {t('features_page.features_home.quote.role')}
            </Text>
          </HStack>
        </VStack>
      </Box>

      <Box className="w-full max-w-[1440px] mx-auto">
        <Box className="px-[142px] pt-[120px] pb-[91px] bg-white">
          <VStack className="gap-[120px] items-center">
            <Text className="text-[40px] font-700 leading-[48px] text-[#0A0A0A] max-w-[684px] text-center">
              {t('features_page.features_home.trust_reasons.title')}
            </Text>
            <Grid
              className="gap-0 w-full"
              _extra={{
                className: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
              }}
            >
              {trustReasons.map((reason, index) => (
                <GridItem
                  key={index}
                  _extra={{
                    className: 'col-span-1',
                  }}
                >
                  <FeatureCard icon={reason.icon} title={reason.text} description={''} minHeight="251px" />
                </GridItem>
              ))}
            </Grid>
          </VStack>
        </Box>

        {/* 第六部分：FAQ */}
        <FAQSection faqs={faqs} title={t('features_page.features_home.faq.title')} />
      </Box>
    </ScrollView>
  );
}

export default observer(FeaturesHome);
