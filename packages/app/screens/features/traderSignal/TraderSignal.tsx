'use client';
import { ScrollView } from '@quantum/components/ui/scroll-view';
import Image from '@unitools/image';
import Coments from '../components/Coments/Coments';
import DescriptionImage from '../components/DescriptionImage/DescriptionImage';
import DescriptionLine from '../components/DescriptionLine/DescriptionLine';
import { FAQSection } from '../components/faq';
import HeroSection from '../components/HeroSection';
import UserTypesSection from '../components/UserTypesSection';
import { useTranslation } from 'react-i18next';

// 导入图片
const SocialMediaImage = require('@quantum/shared/assets/features/trader-signal/social-media.png');
const NewsImage = require('@quantum/shared/assets/features/trader-signal/news.png');
const ChartImage = require('@quantum/shared/assets/features/trader-signal/chart.png');

const DescriptionLineIcon1 = require('@quantum/shared/assets/features/trader-signal/DescriptionLineIcon1.png');
const DescriptionLineIcon2 = require('@quantum/shared/assets/features/trader-signal/DescriptionLineIcon2.png');
const DescriptionLineIcon3 = require('@quantum/shared/assets/features/trader-signal/DescriptionLineIcon3.png');
const DescriptionLineIcon4 = require('@quantum/shared/assets/features/trader-signal/DescriptionLineIcon4.png');
const DescriptionLineIcon5 = require('@quantum/shared/assets/features/trader-signal/DescriptionLineIcon5.png');


const FeaturesIcon1 = require('@quantum/shared/assets/features/trader-signal/features-icon1.png');
const FeaturesIcon2 = require('@quantum/shared/assets/features/trader-signal/features-icon2.png');
const FeaturesIcon3 = require('@quantum/shared/assets/features/trader-signal/features-icon3.png');
const FeaturesIcon4 = require('@quantum/shared/assets/features/trader-signal/features-icon4.png');
const FeaturesIcon5 = require('@quantum/shared/assets/features/trader-signal/features-icon5.png');
const FeaturesIcon6 = require('@quantum/shared/assets/features/trader-signal/features-icon6.png');



import Who1Image from '@quantum/shared/assets/features/portfolio-tracker/user-types/who-1.png';
import Who2Image from '@quantum/shared/assets/features/portfolio-tracker/user-types/who-2.png';
import Who3Image from '@quantum/shared/assets/features/portfolio-tracker/user-types/who-3.png';
import Who4Image from '@quantum/shared/assets/features/portfolio-tracker/user-types/who-4.png';


import DescriptionIcon from '../components/DescriptionIcon/DescriptionIcon';

export default function TraderSignal() {
  const { t } = useTranslation();
  
  // 工作原理数据
  const workflowSteps = [
    {
      title: t('features_page.trader_signal.workflow.step1_title'),
      description: t('features_page.trader_signal.workflow.step1_desc'),
      image: SocialMediaImage,
    },
    {
      title: t('features_page.trader_signal.workflow.step2_title'),
      description: t('features_page.trader_signal.workflow.step2_desc'),
      image: NewsImage,
    },
    {
      title: t('features_page.trader_signal.workflow.step3_title'),
      description: t('features_page.trader_signal.workflow.step3_desc'),
      image: ChartImage,
    },
  ];

  // 用户类型数据
  const userTypes = [
    {
      title: t('features_page.trader_signal.user_types.type1'),
      backgroundColor: '#CAD4D5',
      imageUrl: Who1Image
    },
    {
      title: t('features_page.trader_signal.user_types.type2'),
      backgroundColor: '#9BA5BA',
      imageUrl: Who2Image
    },
    {
      title: t('features_page.trader_signal.user_types.type3'),
      backgroundColor: '#CECAD5',
      imageUrl: Who3Image
    },
    {
      title: t('features_page.trader_signal.user_types.type4'),
      backgroundColor: '#D5D4CA',
      imageUrl: Who4Image
    },
  ];
  // 用户评价数据
  const reviews = [
    {
      userName: t('features_page.trader_signal.reviews.user1_name'),
      userRole: t('features_page.trader_signal.reviews.user1_role'),
      reviewContent: t('features_page.trader_signal.reviews.user1_content'),
      rating: 5,
    },
    {
      userName: t('features_page.trader_signal.reviews.user2_name'),
      userRole: t('features_page.trader_signal.reviews.user2_role'),
      reviewContent: t('features_page.trader_signal.reviews.user2_content'),
      rating: 5,
    },
    {
      userName: t('features_page.trader_signal.reviews.user3_name'),
      userRole: t('features_page.trader_signal.reviews.user3_role'),
      reviewContent: t('features_page.trader_signal.reviews.user3_content'),
      rating: 4,
    },
    {
      userName: t('features_page.trader_signal.reviews.user4_name'),
      userRole: t('features_page.trader_signal.reviews.user4_role'),
      reviewContent: t('features_page.trader_signal.reviews.user4_content'),
      rating: 5,
    },
  ];

  // FAQ 数据
  const faqs = [
    {
      question: t('features_page.trader_signal.faq.beginners_q'),
      answer: t('features_page.trader_signal.faq.beginners_a'),
    },
    {
      question: t('features_page.trader_signal.faq.ai_advisor_q'),
      answer: t('features_page.trader_signal.faq.ai_advisor_a'),
    },
    {
      question: t('features_page.trader_signal.faq.data_security_q'),
      answer: t('features_page.trader_signal.faq.data_security_a'),
    },
    {
      question: t('features_page.trader_signal.faq.asset_types_q'),
      answer: t('features_page.trader_signal.faq.asset_types_a'),
    },
  ];


  // AI 特性数据
  const features = [
    {
      icon: <Image source={FeaturesIcon1} alt="Rebalancing Alerts" width={32} height={32} />,
      title: t('features_page.trader_signal.features.feature1'),
      description: ''
    },
    {
      icon: <Image source={FeaturesIcon2} alt="Sentiment Analysis" width={32} height={32} />,
      title: t('features_page.trader_signal.features.feature2'),
      description: ''
    },
    {
      icon: <Image source={FeaturesIcon3} alt="Auto Categorization" width={32} height={32} />,
      title: t('features_page.trader_signal.features.feature3'),
      description: ''
    },
    {
      icon: <Image source={FeaturesIcon4} alt="Predictive Analytics" width={32} height={32} />,
      title: t('features_page.trader_signal.features.feature4'),
      description: ''
    },
    {
      icon: <Image source={FeaturesIcon5} alt="Risk Profiling" width={32} height={32} />,
      title: t('features_page.trader_signal.features.feature5'),
      description: ''
    },
    {
      icon: <Image source={FeaturesIcon6} alt="Risk Profiling" width={32} height={32} />,
      title: t('features_page.trader_signal.features.feature6'),
      description: ''
    }
  ];

  return (
    <ScrollView className="bg-white">
      {/* Hero Section */}
      <HeroSection
        title={t('features_page.trader_signal.hero.title')}
        subtitle={t('features_page.trader_signal.hero.subtitle')}
        description={t('features_page.trader_signal.hero.description')}
        ctaText={t('features_page.trader_signal.hero.cta')}
        imageUrl="https://static.stockbits.ai/o/banner%20(2)-rcx9ty.png"
      />

      {/* How it Works Section */}
      <DescriptionImage title={t('features_page.trader_signal.workflow.title')} steps={workflowSteps} />
      
      <DescriptionIcon title={t('features_page.trader_signal.features.title')} features={features} />



      {/* 用户类型部分 */}
      <UserTypesSection userTypes={userTypes} />
      
      <DescriptionLine
        title={t('features_page.trader_signal.why_choose.title')}
        features={[
          {
            icon: <Image source={DescriptionLineIcon1} alt="AI Powered" width={32} height={32} />,
            title: t('features_page.trader_signal.why_choose.feature1'),
            description: '',
          },
          {
            icon: <Image source={DescriptionLineIcon2} alt="AI Powered" width={32} height={32} />,
            title: t('features_page.trader_signal.why_choose.feature2'),
            description: '',
          },
          {
            icon: <Image source={DescriptionLineIcon3} alt="AI Powered" width={32} height={32} />,
            title: t('features_page.trader_signal.why_choose.feature3'),
            description: '',
          },
          {
            icon: <Image source={DescriptionLineIcon4} alt="AI Powered" width={32} height={32} />,
            title: t('features_page.trader_signal.why_choose.feature4'),
            description: '',
          },
          {
            icon: <Image source={DescriptionLineIcon5} alt="AI Powered" width={32} height={32} />,
            title: t('features_page.trader_signal.why_choose.feature5'),
            description: '',
          },
        ]}
      />

      <Coments reviews={reviews} />
      {/* FAQ Section */}
      <FAQSection faqs={faqs} title={t('features_page.trader_signal.faq.title')} containerClassName="px-[142px] py-[120px] bg-white" />
    </ScrollView>
  );
}
