'use client';

import React from 'react';
import { Text } from '@quantum/components/ui/text';
import { Box } from '@quantum/components/ui/box';
import { VStack } from '@quantum/components/ui/vstack';
import { HStack } from '@quantum/components/ui/hstack';

export interface FeatureItem {
  /**
   * 特性图标（React元素）
   */
  icon: React.ReactNode;
  /**
   * 特性标题
   */
  title: string;
  /**
   * 特性描述
   */
  description: string;
}

export interface FeaturesSectionProps {
  /**
   * 特性数据列表
   */
  features: FeatureItem[];
  /**
   * 区块标题
   */
  title?: string;
  /**
   * 容器样式类名
   */
  containerClassName?: string;
  /**
   * 标题样式类名
   */
  titleClassName?: string;
  /**
   * 特性网格容器样式类名
   */
  featuresGridClassName?: string;
  /**
   * 单个特性项样式类名
   */
  featureItemClassName?: string;
  /**
   * 特性图标容器样式类名
   */
  iconContainerClassName?: string;
  /**
   * 特性标题样式类名
   */
  featureTitleClassName?: string;
  /**
   * 特性描述样式类名
   */
  featureDescriptionClassName?: string;
}

/**
 * 特性展示组件 - 展示产品/服务的核心特性
 */
const FeaturesSection = ({
  features,
  title,
  containerClassName = "px-[142px] py-[120px] bg-white",
  titleClassName = "text-[40px] font-700 leading-[48px] text-[#0A0A0A] text-center mb-[60px]",
  featuresGridClassName = "flex flex-wrap gap-x-[32px] gap-y-[48px] justify-between",
  featureItemClassName = "w-[364px]",
  iconContainerClassName = "w-[64px] h-[64px] rounded-full bg-[rgba(5,198,151,0.05)] flex items-center justify-center mb-[24px]",
  featureTitleClassName = "text-[24px] font-600 leading-[30px] text-[#0A0A0A] mb-[12px]",
  featureDescriptionClassName = "text-[16px] font-400 leading-[24px] text-[#808080]"
}: FeaturesSectionProps) => {
  return (
    <Box className={containerClassName}>
      <VStack className="w-full max-w-[1156px] mx-auto">
        {title && <Text className={titleClassName}>{title}</Text>}

        <Box className={featuresGridClassName}>
          {features.map((feature, index) => (
            <VStack key={index} className={featureItemClassName}>
              <Box className={iconContainerClassName}>
                {feature.icon}
              </Box>
              <Text className={featureTitleClassName}>{feature.title}</Text>
              <Text className={featureDescriptionClassName}>{feature.description}</Text>
            </VStack>
          ))}
        </Box>
      </VStack>
    </Box>
  );
};

export default FeaturesSection; 