'use client';

import React from 'react';
import { Box } from '@quantum/components/ui/box';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import { Grid, GridItem } from '@quantum/components/ui/grid';
import { HStack } from '@quantum/components/ui/hstack';

// 用户评价卡片组件的属性接口
export interface UserReviewCardProps {
    userName: string;
    userRole: string;
    reviewContent: string;
    rating: number; // 评分，1-5之间的整数
  }
  
  const UserReviewCard = ({ userName, userRole, reviewContent, rating }: UserReviewCardProps) => {
    // 生成星级评分
    const renderStars = () => {
      const stars = [];
      for (let i = 0; i < 5; i++) {
        stars.push(
          <Box key={i} className="w-6 h-6">
            <Box className="relative w-6 h-6">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27Z"
                  fill={i < rating ? '#05C697' : '#E6E6E6'}
                />
              </svg>
            </Box>
          </Box>,
        );
      }
      return stars;
    };
  
    return (
      <Box className="p-6 rounded-[24px] border border-[#E6E6E6] h-full">
        <VStack className="gap-[24px]">
          <VStack className=" w-full gap-[16px]">
            <VStack className="gap-[8px]">
              <Text className="text-[20px] font-700 leading-[28px] text-[#0A0A0A]">{userName}</Text>
              <Text className="text-[20px] font-400 leading-[24px] text-[#808080]">{userRole}</Text>
            </VStack>
            <HStack space="xs">{renderStars()}</HStack>
          </VStack>
          <Text className="text-xl font-normal leading-[30px] text-[#808080]">{reviewContent}</Text>
        </VStack>
      </Box>
    );
  };

export interface UserReviewsSectionProps {
  reviews: UserReviewCardProps[];
}

const Coments = ({ reviews }: UserReviewsSectionProps) => {
  return (
    <Box className="px-[142px] py-[120px] bg-white">
      <VStack className="gap-[110px] items-center">
      <Text className="text-[40px] font-700 leading-[48px] text-[#0A0A0A] max-w-[684px]">
            What Users Are Saying
          </Text>
        <Grid
          className="gap-6 w-full"
          _extra={{
            className: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
          }}
        >
          {reviews.map((review, index) => (
            <GridItem 
              key={index}
              _extra={{
                className: 'col-span-1',
              }}
            >
              <UserReviewCard
                userName={review.userName}
                userRole={review.userRole}
                reviewContent={review.reviewContent}
                rating={review.rating}
              />
            </GridItem>
          ))}
        </Grid>
      </VStack>
    </Box>
  );
};

export default Coments; 