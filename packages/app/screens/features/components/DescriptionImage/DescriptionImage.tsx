import { Box } from '@quantum/components/ui/box';
import { Grid, GridItem } from '@quantum/components/ui/grid';
import { VStack } from '@quantum/components/ui/vstack';
import { HStack } from '@quantum/components/ui/hstack';
import { Text } from '@quantum/components/ui/text';
import Image from '@unitools/image';

export interface DescriptionImageProps {
  title: string;
  steps: {
    title: string;
    description: string;
    image: any;
  }[];
}

const AssetCard = ({ title, description, image }: DescriptionImageProps['steps'][number]) => {
  return (
    <VStack className="border border-[#E6E6E6] rounded-[24px] gap-[17px] h-full overflow-hidden justify-between">
      <VStack className="gap-3 p-6">
        <Text className="text-[24px] font-700 leading-[28px] text-[#0A0A0A]">{title}</Text>
        <Text className="text-[20px] font-400 leading-[30px] text-[#808080]">{description}</Text>
      </VStack>
      <Box>
        <HStack className="w-full h-full justify-end">
          <Image
            source={typeof image === 'string' ? { uri: image } : image}
            alt={title}
            style={{ objectFit: 'contain' }}
            width={'100%'}
            height={140}
          />
        </HStack>
      </Box>
    </VStack>
  );
};

export default function DescriptionImage({ title, steps }: DescriptionImageProps) {
  return (
    <Box className="px-[142px] py-[120px] bg-white">
      <VStack className="gap-[120px]">
        <Text className="text-[40px] font-700 leading-[48px] text-[#0A0A0A] text-center">{title}</Text>

        <Grid
          className="gap-6 w-full"
          _extra={{
            className: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
          }}
        >
          {steps.map((step, index) => (
            <GridItem
              key={index}
              _extra={{
                className: 'col-span-1',
              }}
            >
              <AssetCard title={step.title} description={step.description} image={step.image} />
            </GridItem>
          ))}
        </Grid>
      </VStack>
    </Box>
  );
}
