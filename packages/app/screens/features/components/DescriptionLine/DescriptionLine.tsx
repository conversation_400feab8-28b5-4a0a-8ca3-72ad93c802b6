'use client';

import React from 'react';
import { Box } from '@quantum/components/ui/box';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import { Grid, GridItem } from '@quantum/components/ui/grid';

export interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  minHeight?: string;
}

export const FeatureCard = ({ icon, title, description, minHeight = '354px' }: FeatureCardProps) => {
  return (
    <Box style={{ minHeight: minHeight as any }}>
      <VStack className="px-8 pb-[29px] bg-white border-l border-l-[#E6E6E6] gap-[100px]">
        <Box
          className="w-[64px] h-[64px] rounded-full flex justify-center items-center"
          style={{ backgroundColor: 'rgba(5, 198, 151, 0.05)' }}
        >
          {icon}
        </Box>
        <VStack className="gap-3">
          <Text className="text-[24px] text-600 leading-[1] text-[#0A0A0A]">{title}</Text>
          <Text className="text-[20px] font-400 leading-[30px] text-[#808080]">{description}</Text>
        </VStack>
      </VStack>
    </Box>
  );
};

export interface FeaturesSectionProps {
  title: string;
  features: FeatureCardProps[];
}

const DescriptionLine = ({ title, features }: FeaturesSectionProps) => {
  return (
    <Box className="px-[142px] pt-[120px] pb-[91px] bg-white">
      <VStack className="gap-[120px] items-center">
        <Text className="text-[40px] font-700 leading-[48px] text-[#0A0A0A] max-w-[684px] text-center">{title}</Text>
        <Grid
          className="gap-0 w-full"
          _extra={{
            className: `grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 ${features.length === 5 ? 'xl:grid-cols-5' : ''}`,
          }}
        >
          {features.map((feature, index) => (
            <GridItem
              key={index}
              _extra={{
                className: 'col-span-1',
              }}
            >
              <FeatureCard icon={feature.icon} title={feature.title} description={feature.description} />
            </GridItem>
          ))}
        </Grid>
      </VStack>
    </Box>
  );
};

export default DescriptionLine;
