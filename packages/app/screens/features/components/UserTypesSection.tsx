'use client';

import React from 'react';
import { Box } from '@quantum/components/ui/box';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import { Grid, GridItem } from '@quantum/components/ui/grid';
import Image from '@unitools/image'
import { useTranslation } from 'react-i18next';

interface UserType {
  title: string;
  backgroundColor: string;
  imageUrl: string;
}

export interface UserTypesSectionProps {
  userTypes: UserType[];
}

const UserTypesSection = ({ userTypes }: UserTypesSectionProps) => {
  const { t } = useTranslation();
  
  return (
    <Box className="px-[142px] py-[120px] bg-white">
      <VStack className="gap-[120px]">
        <Text className="text-[40px] font-700 leading-[48px] text-[#0A0A0A] text-center">{t('features_page.common.who_should_use')}</Text>

        <Grid
          className="gap-6 w-full"
          _extra={{
            className: 'grid-cols-2 lg:grid-cols-4',
          }}
        >
          {userTypes.map((userType, index) => (
            <GridItem
              key={index}
              _extra={{
                className: 'col-span-1',
              }}
            >
              <Box className={`rounded-[24px] overflow-hidden h-[377px] w-full relative ${index % 2 === 1 ? '' : 'translate-y-[48px]'}`} style={{ backgroundColor: userType.backgroundColor }}>
                <Image source={userType.imageUrl} alt={userType.title} width={'100%'} height={'auto'}
                style={{
                  width: '100%',
                  height: 'auto',
                  maxHeight: '100%',
                  position: 'absolute',
                  left: 0,
                  right: 0,
                  bottom: 0,
                  objectFit: 'cover',
                }}
                />
                <Box style={{
                  backdropFilter: 'blur(2px)' as any,
                  position: 'absolute',
                  top: '75%',
                  left: 0,
                  right: 0,
                  bottom: 0,
                } as any}></Box>
                <VStack className='p-6 justify-end h-full'>
                  <Text className="text-[20px] font-500 leading-[1] text-[#FFFFFF]">{userType.title}</Text>
                </VStack>
              </Box>
            </GridItem>
          ))}
        </Grid>
      </VStack>
    </Box>
  );
};

export default UserTypesSection;
