import { Box } from '@quantum/components/ui/box';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import { Grid, GridItem } from '@quantum/components/ui/grid';
import { HStack } from '@quantum/components/ui/hstack';
import { RiCheckFill } from 'react-icons/ri';
import Image from '@unitools/image';

export interface DescriptionCardProps {
  title?: string | null;
  features: {
    title: string;
    subtitle?: string;
    descriptions: string[];
    image?: any;
  }[];
}
export default function DescriptionCard({ title, features }: DescriptionCardProps) {
  return (
    <Box className="py-[120px] px-[142px] bg-white">
      <VStack className="gap-[120px]">
        {!!title && <Text className="text-[40px] font-700 leading-[48px] text-center text-[#0A0A0A]">{title}</Text>}

        <Grid
          className="gap-6 w-full"
          _extra={{
            className: 'grid-cols-1 md:grid-cols-2',
          }}
        >
          {features.map((feature, index) => (
            <GridItem
              key={index}
              _extra={{
                className: `col-span-1 ${index === 4 ? 'md:col-span-2' : ''}`,
              }}
            >
              <HStack className={`rounded-[24px] bg-[#F8F8F8] p-8 h-full items-center`}>
                <VStack className="gap-[30px] flex-1 h-full">
                  <VStack className="gap-[20px]">
                    <Text className="text-[24px] font-700 leading-[1] text-[#000000]">{feature.title}</Text>
                    {!!feature.subtitle && (
                      <Text className="text-[20px] font-400 leading-[30px] text-[#808080]">{feature.subtitle}</Text>
                    )}
                  </VStack>
                  {!!feature.image && (
                    <Box className={`w-full ${index === 4 ? 'md:hidden' : ''}`}>
                      <Image source={feature.image} alt="Feature Image" width={'100%'} height={'auto'} style={{ width: '100%', height: 'auto' }} />
                    </Box>
                  )}

                  <VStack className={`gap-[12px] mt-4 flex-1 ${index === 4 ? 'md:justify-end' : ''}`}>
                    {feature.descriptions.map((desc, idx) => (
                      <HStack key={idx} className="gap-4 items-start">
                        <RiCheckFill size={24} color="#808080" className="mt-[4px]" />
                        <Text className="text-[20px] font-400 leading-[30px] text-[#333333] flex-1">{desc}</Text>
                      </HStack>
                    ))}
                  </VStack>
                </VStack>
                {!!feature.image && (
                  <Box className={`flex-1 hidden ${index === 4 ? 'md:block' : ''}`}>
                    <Image source={feature.image} alt="Feature Image" width={'100%'} height={'auto'} style={{ width: '100%', height: 'auto' }} />
                  </Box>
                )}
              </HStack>
            </GridItem>
          ))}
        </Grid>
      </VStack>
    </Box>
  );
}
