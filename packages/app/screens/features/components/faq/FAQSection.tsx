'use client';

import React, { useState } from 'react';
import { Box } from '@quantum/components/ui/box';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import FAQItem from './FAQItem';
import type { FAQItemProps } from './FAQItem';
import { useTranslation } from 'react-i18next';

export interface FAQ {
  question: string;
  answer: string;
}

export interface FAQSectionProps {
  /**
   * FAQ数据数组
   */
  faqs: FAQ[];
  /**
   * 标题
   */
  title?: string;
  /**
   * 容器样式类名
   */
  containerClassName?: string;
  /**
   * 标题样式类名
   */
  titleClassName?: string;
  /**
   * FAQ列表容器样式类名
   */
  faqListClassName?: string;
  /**
   * FAQ项样式类名
   */
  faqItemClassName?: string;
  /**
   * FAQ问题文本样式类名
   */
  questionClassName?: string;
  /**
   * FAQ答案文本样式类名
   */
  answerClassName?: string;
}

/**
 * FAQ部分组件 - 展示常见问题与解答
 */
const FAQSection = ({ 
  faqs, 
  title, 
  containerClassName = "px-[142px] py-[120px] bg-white",
  titleClassName = "text-[40px] font-700 leading-[48px] text-[#0A0A0A]",
  faqListClassName = "w-full gap-[40px]",
  faqItemClassName = "border-b border-b-[#E6E6E6] w-full pb-[40px] gap-[32px]",
  questionClassName = "text-[24px] font-700 leading-[28px] text-[#0A0A0A]",
  answerClassName = "text-[20px] font-400 leading-[28px] text-[#808080]"
}: FAQSectionProps) => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const { t } = useTranslation();

  const handleToggle = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  // 使用默认标题，如果没有提供
  const finalTitle = title || t('features_page.common.faq');

  return (
    <Box className={containerClassName}>
      <VStack className="gap-[120px] items-center">
        {finalTitle && <Text className={titleClassName}>{finalTitle}</Text>}

        <VStack className={faqListClassName}>
          {faqs.map((faq, index) => (
            <FAQItem
              key={index}
              question={faq.question}
              answer={faq.answer}
              isOpen={openIndex === index}
              onToggle={() => handleToggle(index)}
              className={faqItemClassName}
              questionClassName={questionClassName}
              answerClassName={answerClassName}
            />
          ))}
        </VStack>
      </VStack>
    </Box>
  );
};

export default FAQSection; 