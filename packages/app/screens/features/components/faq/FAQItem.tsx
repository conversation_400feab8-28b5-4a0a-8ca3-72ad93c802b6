'use client';

import React from 'react';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { Pressable } from '@quantum/components/ui/pressable';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { RiArrowDownSLine, RiArrowUpSLine } from 'react-icons/ri';

export interface FAQItemProps {
  question: string;
  answer: string;
  isOpen?: boolean;
  onToggle: () => void;
  /**
   * 自定义样式类名
   */
  className?: string;
  /**
   * 问题文本的样式类名
   */
  questionClassName?: string;
  /**
   * 答案文本的样式类名
   */
  answerClassName?: string;
}

/**
 * FAQ项组件 - 单个问答项
 */
const FAQItem = ({ 
  question, 
  answer, 
  isOpen = false, 
  onToggle,
  className = "border-b border-b-[#E6E6E6] w-full pb-[40px] gap-[32px]",
  questionClassName = "text-[24px] font-700 leading-[28px] text-[#0A0A0A]",
  answerClassName = "text-[20px] font-400 leading-[28px] text-[#808080]"
}: FAQItemProps) => {
  return (
    <VStack className={className}>
      <Pressable onPress={onToggle}>
        <HStack className="justify-between items-center w-full gap-6">
          <Text className={questionClassName}>{question}</Text>
          {!isOpen ? <RiArrowDownSLine color="#0A0A0A" size={24} /> : <RiArrowUpSLine color="#0A0A0A" size={24} />}
        </HStack>
      </Pressable>

      {isOpen && (
        <Box className="">
          <Text className={answerClassName}>{answer}</Text>
        </Box>
      )}
    </VStack>
  );
};

export default FAQItem; 