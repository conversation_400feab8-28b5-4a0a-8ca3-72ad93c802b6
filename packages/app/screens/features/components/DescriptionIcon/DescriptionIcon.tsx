'use client';

import React from 'react';
import { Box } from '@quantum/components/ui/box';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import { Grid, GridItem } from '@quantum/components/ui/grid';


export interface AIFeatureItemProps {
    icon: React.ReactNode;
    title: string;
    description: string;
  }
  
  const AIFeatureItem = ({ icon, title, description }: AIFeatureItemProps) => {
    return (
      <VStack className="gap-[24px]">
        <Box className="w-8 h-8">{icon}</Box>
        <VStack className="flex-1 gap-3">
          <Text className="text-[24px] font-600 leading-[1] text-[#0A0A0A]">{title}</Text>
          <Text className="text-[20px] font-400 leading-[30px] text-[#808080]">{description}</Text>
        </VStack>
      </VStack>
    );
}
export interface AIFeaturesSectionProps {
  title: string;
  features: AIFeatureItemProps[];
}

const DescriptionIcon = ({ title, features }: AIFeaturesSectionProps) => {
  return (
    <Box className="px-[142px] py-[120px] bg-[#ffffff]">
      <VStack className="gap-[120px]">
        <Text className="text-[40px] font-700 leading-[48px] text-[#0A0A0A] text-center">{title}</Text>

        <Grid
          className="gap-[64px] w-full"
          _extra={{
            className: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
          }}
        >
          {features.map((feature, index) => (
            <GridItem
              key={index}
              _extra={{
                className: 'col-span-1',
              }}
            >
              <AIFeatureItem key={index} icon={feature.icon} title={feature.title} description={feature.description} />
            </GridItem>
          ))}
        </Grid>
      </VStack>
    </Box>
  );
};

export default DescriptionIcon;
