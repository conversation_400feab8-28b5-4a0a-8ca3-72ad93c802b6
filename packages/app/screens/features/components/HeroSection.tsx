'use client';

import React from 'react';
import { View } from 'react-native';
import { Text } from '@quantum/components/ui/text';
import { Box } from '@quantum/components/ui/box';
import { VStack } from '@quantum/components/ui/vstack';
import { HStack } from '@quantum/components/ui/hstack';
import { Pressable } from '@quantum/components/ui/pressable';
import { Button, ButtonText } from '@quantum/components/ui/button';
import { useTranslation } from 'react-i18next';
import useRouter from '@unitools/router';
import { useRequest } from 'ahooks';
import { authStore } from '../../../store/auth.store';
import { observer } from 'mobx-react-lite';
import { joinWaitlist } from '../../../services/waitlist/waitlist.api';
import { waitlistStore } from '../../../store/waitlist.store';
import { useCustomToast } from '../../../components/Toast/ToastProvider';
import { Image } from '@quantum/components/ui/image';

export interface HeroSectionProps {
  /**
   * 主标题
   */
  title: string;
  /**
   * 副标题
   */
  subtitle: string;
  /**
   * 描述文本
   */
  description: string;
  /**
   * CTA按钮文本
   */
  ctaText?: string;
  /**
   * CTA按钮点击处理函数
   */
  onCtaPress?: () => void;
  /**
   * 容器样式类名
   */
  containerClassName?: string;
  /**
   * 标题样式类名
   */
  titleClassName?: string;
  /**
   * 副标题样式类名
   */
  subtitleClassName?: string;
  /**
   * 描述文本样式类名
   */
  descriptionClassName?: string;
  /**
   * CTA按钮样式类名
   */
  ctaClassName?: string;
  /**
   * CTA文本样式类名
   */
  ctaTextClassName?: string;
  /**
   * 图片URL
   */
  imageUrl?: string;
}

/**
 * 英雄/头部区域组件 - 通常用于页面顶部的主要内容介绍
 */
const HeroSection = ({
  title,
  subtitle,
  description,
  ctaText,
  onCtaPress,
  containerClassName = "px-[142px] py-[80px]",
  titleClassName = "text-[#05C697] text-[18px] leading-[1] font-700",
  subtitleClassName = "text-[36px] font-700 leading-[1] text-[#0A0A0A]",
  descriptionClassName = "text-[18px] font-400 leading-[1.2] text-[#808080]",
  imageUrl,
}: HeroSectionProps) => {
  const { t } = useTranslation();
  
  // 使用默认值，如果没有提供
  const finalCtaText = ctaText || t('features_page.common.get_started');
  
  const router = useRouter();
 
  const { showToast } = useCustomToast();
  const { run, loading } = useRequest(
    async () => {
      if (!authStore.isLoggedIn) {
        authStore.setShowLoginModal(true);
        return;
      }
      try {
        // const res = await joinWaitlist();
        // await waitlistStore.waitlistStatus.reload();
      } catch (error: any) {
        console.log(error);
        showToast({
          title: t('failed'),
          description: error.msg || t('join_failed'),
          status: 'error',
        });
      }
      router.push('/');
    },
    {
      manual: true,
    },
  );
  return (
    <Box className={containerClassName}>
      <HStack className="w-full gap-[144px]" >
        <VStack className="gap-4 w-full lg:w-[446px]" >
          <Text className={titleClassName}>{title}</Text>
          <Text className={subtitleClassName}>
            {subtitle}
          </Text>
          <Text className={descriptionClassName}>
            {description}
          </Text>
          <HStack className="mt-8">
            <Button
              className="bg-[#05C697] rounded-[56px] h-[56px] w-[180px] min-w-[233px]"
              variant="solid"
              loading={!onCtaPress && loading}
              onPress={() => {
                if (onCtaPress) {
                  onCtaPress();
                  return;
                }
                run();
              }}
            >
              <ButtonText className="text-white font-semibold">{finalCtaText}</ButtonText>
            </Button>
          </HStack>
        </VStack>
        <Box className="h-[342px] flex-1 overflow-hidden rounded-[10px] my-[19px]" >
          {
            !!imageUrl &&
            <Image source={imageUrl} alt="hero" className="w-full h-full" />
          }
        </Box>
      </HStack>
    </Box>
  );
};

export default observer(HeroSection); 