'use client';
import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import { Divider } from '@quantum/components/ui/divider';
import AssetProportionGraph from './AssetProportionGraph';
import { observer } from 'mobx-react-lite';
import { portfolioStore } from '../../../../store/portfolio.store';
import { useMemo } from 'react';
import Empty from '../../../../components/Empty/Empty';
import { averageNumber } from '../../../../uitls/number';
import { useTranslation } from 'react-i18next';

interface AssetProportionItemProps {
  label: string;
  value: number;
  color: string;
}

function AssetProportionItem({ label, value, color }: AssetProportionItemProps) {
  // 将小数转换为百分比格式显示
  const percentValue = `${(value * 100).toFixed(2)}%`;
  
  return (
    <HStack className="items-center justify-between gap-[10px]">
      <HStack className="gap-[6px] items-center justify-center">
        <Box className="w-[10px] h-[10px] rounded-[3px]" style={{ backgroundColor: color }} />
        <Text className="text-[#4D4D4D] text-[14px] font-500 leading-[20px]">{label}</Text>
      </HStack>
      <Divider
        className="h-[1px] flex-1 bg-[none]"
        style={{
          borderStyle: 'dashed',
          borderWidth: 0,
          borderTopWidth: 1,
          borderColor: '#CCCCCC',
        }}
      />
      <Text className=" text-[#4D4D4D] text-right text-[14px] font-500 leading-[20px]">{percentValue}</Text>
    </HStack>
  );
}

const colors = ['#3fbe92', '#b6f8da', '#f5c43b', '#3bcdd5', '#cccccc'];
function AssetProportion() {
  const { t } = useTranslation();

  const loading = portfolioStore.portfolioPositionList.loading;
  const data = portfolioStore.portfolioPositionList.data;

  const list = useMemo(() => {
    if (!data || !Array.isArray(data)) return [];
    
    // 计算每个持仓项的价值 (pos * price)
    const positionValues = data.map(item => ({
      label: item.name || t('portfolio_page.asset_proportion.unknown_asset'),
      value: (Number(item.pos) || 0) * (Number(item.price) || 0),

      originalData: item
    }));
    
    // 按价值降序排序
    const sortedPositions = [...positionValues].sort((a, b) => b.value - a.value);
    
    // 计算总价值
    const totalValue = sortedPositions.reduce((sum, item) => sum + item.value, 0);
    
    // 如果总数不超过5条记录，直接返回所有记录（转换为比例）
    if (sortedPositions.length <= 5) {
      return sortedPositions.map(item => ({
        label: item.label,
        value: totalValue > 0 ? Number((item.value / totalValue).toFixed(4)) : 0,
        value_usd: `$${averageNumber(item.value)}`,
      }));
    }
    
    // 取前四项
    const topFour = sortedPositions.slice(0, 4);
    
    // 计算其余项的总和
    const otherValue = sortedPositions.slice(4).reduce((sum, item) => sum + item.value, 0);
    
    // 组合前四项和"其他"（转换为比例）
    return [
      ...topFour.map(item => ({
        label: item.label,
        value: totalValue > 0 ? Number((item.value / totalValue).toFixed(4)) : 0,
        value_usd: `$${averageNumber(item.value)}`,
      })),
      {
        label: t('portfolio_page.asset_proportion.others'),
        value: totalValue > 0 ? Number((otherValue / totalValue).toFixed(4)) : 0,
        value_usd: `$${averageNumber(otherValue)}`,
      }
    ];
  }, [data, t]);

  if (!data || !Array.isArray(data)) return <Empty content={t('portfolio_page.asset_proportion.no_data')} />;
  return (
    <VStack className="gap-[44px] h-full items-center justify-between">
      <Box className="w-full max-w-[250px] h-[250px]">
        <AssetProportionGraph colors={colors} data={list} />
      </Box>
      <Box className="w-[226px] mx-auto">
        <VStack className="gap-[16px]">
          {list.map((item, index) => (
            <AssetProportionItem key={item.label} {...item} color={colors[index]} />
          ))}
        </VStack>
      </Box>
    </VStack>
  );
}


export default observer(AssetProportion);
