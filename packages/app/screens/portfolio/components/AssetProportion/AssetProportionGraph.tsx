'use client';
import React, { useEffect, useRef } from 'react';
import { Pie<PERSON>hart } from 'echarts/charts';
import { LegendComponent, TooltipComponent } from 'echarts/components';
import * as echarts from 'echarts/core';
import { SVGRenderer } from '@wuba/react-native-echarts';
import { averageNumber } from '../../../../uitls/number';

echarts.use([PieChart, TooltipComponent, LegendComponent, SVGRenderer]);

export default function AssetProportionGraph({ 
  colors, 
  data 
}: { 
  colors: string[]; 
  data: { label: string; value: number; value_usd: string }[] 
}) {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);
  
  // 计算总值用于百分比显示
  const totalValue = React.useMemo(() => {
    return data.reduce((sum, item) => sum + item.value, 0);
  }, [data]);

  // 图表配置
  const option = React.useMemo(() => ({
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const percentage = averageNumber((params.value / totalValue) * 100, 2);
        return `${params.name}: ${params.data.value_usd} (${percentage}%)`;
      }
    },
    series: [
      {
        name: '资产占比',
        type: 'pie',
        radius: ['60%', '90%'], // 扩大环形图的内外半径比例
        // center: ['50%', '50%'], // 确保居中
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 0,
          borderColor: '#fff',
          borderWidth: 1
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          }
        },
        data: data.map((item, index) => ({
          value: item.value,
          name: item.label,
          value_usd: item.value_usd,
          itemStyle: {
            color: colors[index % colors.length]
          }
        }))
      }
    ]
  }), [data, colors, totalValue]);

  useEffect(() => {
    if (!chartRef.current) return;

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        useDirtyRect: true,
      });
    }

    // 更新图表数据
    chartInstance.current.setOption(option);

    const resize = () => chartInstance.current && chartInstance.current.resize();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', resize);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', resize);
      }
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [option]);

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
}
