'use client';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON>dal<PERSON>eader,
  ModalCloseButton,
  ModalBody,
} from '@quantum/components/ui/modal';
import { observer } from 'mobx-react-lite';
import { portfolioStore } from '../../../../store/portfolio.store';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { HStack } from '@quantum/components/ui/hstack';
import { RiCloseFill } from 'react-icons/ri';
import { Divider } from '@quantum/components/ui/divider';
import { Switch } from '@quantum/components/ui/switch';
import { Input, InputField, InputSlot } from '@quantum/components/ui/input';
import { RiSearch2Line } from 'react-icons/ri';
import { useTranslation } from 'react-i18next';
import { useCallback, useMemo, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import {
  getAssetDetail,
  getMarketTrending,
  reportSearchEvent,
  searchAssets,
} from '../../../../services/asset/asset.api';
import { Market, MarketTrendingSort, type SearchResultItem } from '../../../../services/asset/asset.types';
import { Button, ButtonText } from '@quantum/components/ui/button';
import { Pressable } from 'react-native-gesture-handler';
import { useAsyncStorage } from '../../../../hooks/useAsyncStorage';
import TextOrImageLogo from '../../../../components/TextOrImageLogo';
import { Checkbox, CheckboxIndicator, CheckboxIcon, CheckboxLabel } from '@quantum/components/ui/checkbox';
import { Radio, RadioIndicator, RadioIcon, RadioLabel } from '@quantum/components/ui/radio';
import { CircleIcon } from '@quantum/components/ui/icon';
import { watchlistStore } from '../../../../store/watchlist.store';
import Empty from '../../../../components/Empty/Empty';
import { ScrollView } from '@quantum/components/ui/scroll-view';
import { findIndex, isNil } from 'lodash';
import { Skeleton } from '@quantum/components/ui/skeleton';
import { batchAddWatchlist } from '../../../../services/watchlist/watchlist.api';
import ToastProvider, { useCustomToast } from '../../../../components/Toast/ToastProvider';
import { averageNumber } from '../../../../uitls/number';
import { WatchListTrend } from '../PortfolioTable/Watchlist';
import { sleep } from '../../../../uitls';
import { SearchSelector } from './SearchSelector';
import { useHandleErrorMessage } from '../../../../utils/handleErrorMessage';
function AssetInfo({
  params,
}: {
  params: {
    name: string;
    symbol: string;
    market: Market;
  };
}) {
  const { t, ready, i18n } = useTranslation();
  const { data, loading } = useRequest(
    async () => {
      if (!params?.market || !params?.symbol) return null;

      const res = await getAssetDetail({
        market: params.market as Market,
        symbol: params.symbol,
      });

      return res;
    },
    {
      ready: !!params?.market && !!params?.symbol,
      refreshDeps: [params?.market, params?.symbol, i18n.language],
    },
  );

  const infoList = useMemo(() => {
    if (!data) return [];

    return [
      {
        label: t('detail.volume'),
        value: averageNumber(data.value),
      },
      {
        label: t('detail.market_cap'),
        value: averageNumber(data.market_cap),
      },
      {
        label: data?.market === Market.CRYPTO ? t('detail.fully_diluted_market_cap') : t('detail.pe_label'),
        value:
          data?.market === Market.CRYPTO ? averageNumber(data.fdv || 0) || '-' : averageNumber(data.pe || 0) || '-',
      },
      {
        label: data?.market === Market.CRYPTO ? t('detail.max_supply') : t('detail.total_share_label'),
        value:
          data?.market === Market.CRYPTO
            ? averageNumber(data.total_supply || 0) || '-'
            : averageNumber(data.total_share || 0) || '-',
      },
    ];
  }, [data, t]);

  if (!params?.market || !params?.symbol) return null;
  return (
    <VStack className="gap-[16px]">
      <Text className="text-[#0A0A0A] text-[14px] font-500 leading-[20px]">
        {params?.name || '--'} {t('portfolio_page.add_assets.statistics')}
      </Text>
      <VStack className="gap-[8px]">
        <VStack className="gap-[8px] items-center justify-between">
          {loading ? (
            <>
              {new Array(4).fill(undefined).map((_, index) => (
                <HStack key={index} className="gap-[8px] items-center justify-between w-full">
                  <Skeleton className="w-[100px] h-[20px]" key={index} />
                  <Skeleton className="w-[100px] h-[20px]" key={index} />
                </HStack>
              ))}
            </>
          ) : (
            <>
              {infoList.map((item) => (
                <HStack className="gap-[8px] items-center justify-between w-full">
                  <Text className=" text-[#808080] text-[14px] font-400 leading-[20px]">{item.label}</Text>
                  <Text className=" text-[#0A0A0A] text-right text-[14px] font-500 leading-[20px]">{item.value}</Text>
                </HStack>
              ))}
            </>
          )}
        </VStack>
        {loading ? (
          <Skeleton className="w-full h-[100px]" />
        ) : (
          <WatchListTrend
            item={{
              symbol: params?.symbol || '',
              market: params?.market,
            }}
            className="w-full h-[100px]"
          />
        )}
      </VStack>
    </VStack>
  );
}

function AssetItem({ item, onPress }: { item: SearchResultItem; onPress?: (item: SearchResultItem) => void }) {
  const [checked, setChecked] = useState(false);

  const isDisabled = useMemo(() => {
    const list = watchlistStore.watchlist.data || [];
    return list.some((_) => _.symbol === item.symbol && _.market === item.market);
  }, [watchlistStore.watchlist.data]);

  const isSelected = useMemo(() => {
    return portfolioStore.addAssetsList.some((_) => _.symbol === item.symbol && _.market === item.market);
  }, [portfolioStore.addAssetsList]);
  function handleSelect() {
    portfolioStore.addAssets({
      symbol: item.symbol,
      name: item.name,
      market: item.market,
      price: item.price,
      change_rate: item.change_rate,
    });
  }
  return (
    <HStack className="gap-[8px] items-center justify-between hover:bg-[#F0F0F0]">
      <Box className="flex-1">
        <Pressable
          onPress={() => {
            onPress?.(item);
          }}
          className="flex-1 w-full"
        >
          <HStack className="gap-[8px] items-center flex-1 w-full">
            <TextOrImageLogo text={item.name} size={24} />
            <Text className="text-[#0A0A0A] text-[14px] font-500 leading-[20px]">{item.name}</Text>
            <Text className="text-[#808080] text-[14px] font-400 leading-[20px]">{item.symbol}</Text>
          </HStack>
        </Pressable>
      </Box>

      {isDisabled ? (
        <Box className="w-[16px] h-[16px]  border-[4px] border-solid border-[#ccc] rounded-full cursor-not-allowed" />
      ) : (
        <>
          <Pressable
            onPress={() => {
              handleSelect();
            }}
          >
            {isSelected ? (
              <Box className="w-[16px] h-[16px] bg-[#fff] border-[4px] border-solid border-[#1f69ff] rounded-full" />
            ) : (
              <Box className="w-[16px] h-[16px]  border-[2px] border-solid border-[#b3b3b3] rounded-full" />
            )}
          </Pressable>
        </>
      )}
    </HStack>
  );
}

export const AssetItemComp = observer(AssetItem);

function AssetResultBox({
  title,
  list,
  hidden,
  flitter,
  loading,
  onPress,
}: {
  title: string;
  list: SearchResultItem[];
  hidden?: boolean;
  flitter?: Market | null;
  loading?: boolean;
  onPress?: (item: SearchResultItem) => void;
}) {
  const { t } = useTranslation();
  const filterList = useMemo(() => {
    if (flitter) {
      return list.filter((item) => item.market === flitter);
    }
    return list;
  }, [list, flitter]);

  if (!list.length && !loading) {
    return null;
  }
  return (
    <VStack className={`gap-[12px] ${hidden ? 'hidden' : ''}`}>
      <Text className="text-[#808080] text-[12px] font-400 leading-[16px]">{title}</Text>
      <VStack className="gap-[10px]">
        {loading ? (
          <>
            {new Array(3).fill(undefined).map((_, index) => (
              <HStack className="gap-[8px] items-center justify-between hover:bg-[#F0F0F0]" key={index}>
                <HStack className="gap-[8px] items-center flex-1 w-full">
                  <Skeleton variant="circular" className="h-[24px] w-[24px]" />

                  <Skeleton className="h-[20px] w-[90px]" />
                  <Skeleton className="h-[20px] w-[70px]" />
                </HStack>

                <Skeleton variant="circular" className="h-[16px] w-[16px]" />
              </HStack>
            ))}
          </>
        ) : (
          <>
            {filterList.length > 0 ? (
              <>
                {list.map((item, index) => (
                  <AssetItemComp key={index} item={item} onPress={onPress} />
                ))}
              </>
            ) : (
              <Empty content={t('portfolio_page.add_assets.no_data')} />
            )}
          </>
        )}
      </VStack>
    </VStack>
  );
}

function AddAssetForm({ submit, submitLoading }: { submit: () => void; submitLoading: boolean }) {
  const { t } = useTranslation();
  const [keyword, setKeyword] = useState('');
  const inputRef = useRef<any>(null);

  const {
    runAsync,
    data: searchResult = [],
    mutate,
    loading,
  } = useRequest(
    async (keyword: string) => {
      const res = await searchAssets(keyword);
      return res;
    },
    {
      manual: true,
      debounceWait: 500,
    },
  );

  // 防抖处理搜索请求
  const handleSearch = useCallback(async (value: string) => {
    setKeyword(value);

    // 如果输入为空，清空搜索结果并关闭弹窗
    if (!value.trim()) {
      mutate([]);
      return;
    }

    runAsync(value.trim());
  }, []);
  const [activeTab, setActiveTab] = useState<Market | 'hot' | 'recent' | null>(null);
  const tabs = [
    {
      label: t('portfolio_page.add_assets.hot'),
      value: 'hot',
    },
    {
      label: t('portfolio_page.add_assets.recent_search'),
      value: 'recent',
    },
    {
      label: t('portfolio_page.add_assets.crypto'),
      value: Market.CRYPTO,
    },
    {
      label: t('portfolio_page.add_assets.us_stocks'),
      value: Market.US,
    },
    {
      label: t('portfolio_page.add_assets.hk_stocks'),
      value: Market.HK,
    },
  ];
  const resultFilter = useMemo<Market | null>(() => {
    if (activeTab === 'hot' || activeTab === 'recent') {
      return null;
    }
    return activeTab;
  }, [activeTab]);
  const { value: historyList = [], setValue: setHistoryList } = useAsyncStorage<SearchResultItem[]>('historyList');

  const { data: hotList = [], loading: hotLoading } = useRequest(async () => {
    try {
      const res = await getMarketTrending(Market.US, MarketTrendingSort.VALUE_DESC);
      return (res || []).slice(0, 5);
    } catch (error) {
      return [];
    }
  });
  const [showAssetInfo, setShowAssetInfo] = useState<{
    name: string;
    symbol: string;
    market: Market;
  } | null>(null);
  function handleAssetPress(item: SearchResultItem) {
    setShowAssetInfo({
      symbol: item.symbol,
      name: item.name,
      market: item.market,
    });
  }

  async function handleSubmit() {
    await submit();
    const ary: SearchResultItem[] = [];
    for (const item of portfolioStore.addAssetsList) {
      const index = findIndex(searchResult, (_) => _.symbol === item.symbol && _.market === item.market);
      if (index !== -1) {
        ary.push(item);
        reportSearchEvent({
          market: item.market,
          symbol: item.symbol,
          search_text: keyword,
        });
      }
    }
    setHistoryList((res) => {
      // 根据market和symbol去重，并将新点击的项目放在最前面
      const result = res?.filter((hist) => {
        const index = findIndex(ary, (_) => _.symbol === hist.symbol && _.market === hist.market);
        return index === -1;
      });
      return [...ary, ...(result || [])];
    });
  }

  return (
    <Box>
      <VStack className="gap-6 px-[24px]">
        <Input className="flex-1 w-full px-[12px] items-center gap-[8px] rounded-[6px] bg-[#F7F7F7] border-none outline-none shadow-none data-[focus=true]:shadow-[none]">
          <InputSlot>
            <RiSearch2Line size={20} className="text-[#808080]" />
          </InputSlot>
          <InputField
            className="h-[40px] px-0"
            ref={inputRef}
            placeholder={t('portfolio_page.add_assets.search_placeholder')}
            value={keyword}
            onChangeText={handleSearch}
          />
        </Input>

        <HStack className="items-center gap-[8px]">
          {tabs.map((tab) => (
            <Pressable
              key={tab.value}
              onPress={() => {
                if (activeTab === tab.value) {
                  setActiveTab(null);
                } else {
                  setActiveTab(tab.value as Market | 'hot' | 'recent');
                }
              }}
            >
              <HStack
                className={`px-[12px] py-[6px] justify-center items-center gap-[2px] rounded-[4px] ${
                  activeTab === tab.value ? 'bg-[#05C697]' : 'bg-[rgba(5,198,151,0.10)]'
                }`}
              >
                <Text
                  className={`text-[${
                    activeTab === tab.value ? '#fff' : '#05C697'
                  }] text-[14px] font-500 leading-[20px]`}
                >
                  {tab.label}
                </Text>
              </HStack>
            </Pressable>
          ))}
        </HStack>
        <Divider className="bg-[#E6E6E6]" />
        <HStack className="gap-[0px] max-h-[308px]">
          <ScrollView className="flex-1 h-full pr-[24px]">
            <VStack className="gap-[14px]">
              <AssetResultBox
                title={t('portfolio_page.add_assets.search_results')}
                list={searchResult}
                hidden={!!activeTab && (activeTab === 'hot' || activeTab === 'recent')}
                flitter={resultFilter}
                loading={loading}
                onPress={handleAssetPress}
              />

              <AssetResultBox
                title={t('portfolio_page.add_assets.hot')}
                list={hotList}
                hidden={!!activeTab && activeTab !== 'hot'}
                loading={hotLoading}
                onPress={handleAssetPress}
              />
              <AssetResultBox
                title={t('portfolio_page.add_assets.recent_search')}
                list={historyList}
                hidden={!!activeTab && activeTab !== 'recent'}
                onPress={handleAssetPress}
              />
            </VStack>
          </ScrollView>
          <HStack className={`gap-[24px] ${showAssetInfo ? '' : 'hidden'}`}>
            <Divider className="bg-[#E6E6E6]" orientation="vertical" />
            <Box className="w-[234px]">
              <AssetInfo
                params={{
                  name: showAssetInfo?.name || '',
                  symbol: showAssetInfo?.symbol || '',
                  market: showAssetInfo?.market || Market.US,
                }}
              />
            </Box>
          </HStack>
        </HStack>
      </VStack>
      <HStack className="h-[84px] border-t border-solid border-[#E6E6E6] items-center justify-between px-[24px]">
        <Text className="text-[#808080] text-[14px] font-500 leading-[20px]">
          {t('portfolio_page.add_assets.selected')}{' '}
          <Text className="text-[#05C697] text-[14px] font-600 leading-[20px]">
            {portfolioStore.addAssetsList.length || 0}
          </Text>{' '}
          {t('portfolio_page.add_assets.assets')}
        </Text>
        <Button
          className="h-[48px] px-[28px] rounded-[8px] bg-[#05C697]"
          isDisabled={!portfolioStore.addAssetsList.length}
          onPress={() => {
            handleSubmit();
          }}
          loading={submitLoading}
        >
          <ButtonText className="text-[#FFF] text-[16px] font-600 leading-[20px]">{t('portfolio_page.add_assets.add_asset')}</ButtonText>
        </Button>
      </HStack>
    </Box>
  );
}
const AddAssetFormComp = observer(AddAssetForm);

function AddAssets({ onFinish }: { onFinish: () => void }) {
  const { t } = useTranslation();
  const show = portfolioStore.showAddAssetsModal;
  const onClose = () => {
    portfolioStore.setShowAddAssetsModal(false);
  };

  const { showToast } = useCustomToast();

  const { handleErrorMessage } = useHandleErrorMessage();
  const { loading: submitLoading, run: submit } = useRequest(
    async () => {
      try {
        const res = await batchAddWatchlist({
          assets: portfolioStore.addAssetsList.map((_) => ({
            market: _.market,
            symbol: _.symbol,
          })),
        });
        onFinish();
        portfolioStore.clearAssets();
        await sleep(3000);
        await watchlistStore.watchlist.reload();
        onClose();
        return res;
      } catch (error: any) {
        handleErrorMessage(error)

        return null;
      }
    },
    {
      manual: true,
    },
  );


  return (
    <>

      <Modal isOpen={show} onClose={onClose} className="fixed inset-0">
        <ModalBackdrop className="bg-[#000000] opacity-70" />
        <ModalContent
          size="full"
          className="p-0 pt-[32px]  max-w-[800px] w-full min-h-[320px]  rounded-[16px] bg-[#FFF] shadow-[0px_4px_25px_0px_rgba(0,0,0,0.10),0px_4px_10px_0px_rgba(0,0,0,0.04)]"
        >
          <ModalHeader className="px-[24px]">
            <Box>
              <Text className="text-[#0A0A0A] text-[20px] font-600 leading-[24px]">{t('portfolio_page.add_assets.title')}</Text>
            </Box>
            <ModalCloseButton className="w-[24px] h-[24px] items-center justify-center border rounded-[6px] border-solid border-[#cccccc]">
              <RiCloseFill size={16} className="text-[#0a0a0a]" />
            </ModalCloseButton>
          </ModalHeader>
          <ModalBody className="mt-3 mb-0  pb-0">
            <AddAssetFormComp submit={submit} submitLoading={submitLoading} />
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
}

export default observer(AddAssets);
