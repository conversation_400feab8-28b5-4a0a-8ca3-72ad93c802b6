import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import TextOrImageLogo from '../../../../components/TextOrImageLogo';
import { Text } from '@quantum/components/ui/text';
import { RiArrowDownSLine, RiCloseLine, RiSearch2Line } from 'react-icons/ri';
import { Pressable } from '@quantum/components/ui/pressable';
import { Popover, PopoverBackdrop, PopoverContent, PopoverBody } from '@quantum/components/ui/popover';
import { useCallback, useMemo, useRef, useState } from 'react';
import type { SearchResultItem } from '../../../../services/asset/asset.types';
import type { TextInput } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Input, InputField, InputSlot } from '@quantum/components/ui/input';
import Empty from '../../../../components/Empty/Empty';
import { VStack } from '@quantum/components/ui/vstack';
import { SearchItem, SearchSkeleton } from '../../../../components/Nav/Search';
import { Divider } from '@quantum/components/ui/divider';
import { useAsyncStorage } from '../../../../hooks/useAsyncStorage';
import { reportSearchEvent, searchAssets } from '../../../../services/asset/asset.api';
import { useRequest } from 'ahooks';
export function SearchSelector({
  onSelect,
  value,
  disabled = false,
}: {
  onSelect: (value: SearchResultItem) => void;
  value: SearchResultItem | null;
  disabled?: boolean;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [keyword, setKeyword] = useState('');
  const inputRef = useRef(null);
  const { t } = useTranslation();

  const {
    runAsync,
    data: results = [],
    mutate,
    loading,
  } = useRequest(
    async (keyword: string) => {
      const res = await searchAssets(keyword);
      return res;
    },
    {
      manual: true,
      debounceWait: 500,
    },
  );

  // 防抖处理搜索请求
  const handleSearch = useCallback(async (value: string) => {
    setKeyword(value);

    // 如果输入为空，清空搜索结果并关闭弹窗
    if (!value.trim()) {
      mutate([]);
      return;
    }

    runAsync(value.trim());
  }, []);

  const handleOpen = () => {
    setIsOpen(true);
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  const reset = () => {
    setKeyword('');
    mutate([]);
    handleClose();
  };

  const { value: historyList = [], setValue: setHistoryList } = useAsyncStorage<SearchResultItem[]>('historyList');
  const showEmpty = useMemo(() => {
    return results.length === 0 && historyList.length === 0 && !loading;
  }, [keyword, results, historyList, loading]);

  // 抽象出处理点击搜索项的通用方法
  const handleItemSelect = (item: SearchResultItem, searchText: string = '') => {
    // 报告搜索事件
    reportSearchEvent({
      market: item.market,
      symbol: item.symbol,
      search_text: searchText,
    });

    // 根据market和symbol去重，并将新点击的项目放在最前面
    const filteredHistory = historyList.filter((hist) => !(hist.market === item.market && hist.symbol === item.symbol));
    setHistoryList([item, ...filteredHistory]);
    onSelect(item);
    reset();
  };

  const handleResultClick = (result: SearchResultItem) => {
    handleItemSelect(result, keyword);
  };

  const handleHistoryClick = (item: SearchResultItem) => {
    handleItemSelect(item);
  };

  const handleClearHistory = () => {
    setHistoryList([]);
  };
  

  return (
    <>
      <Popover
        isOpen={isOpen}
        onOpen={() => {
          handleOpen();
        }}
        onClose={handleClose}
        placement="bottom left"
        initialFocusRef={inputRef}
        trigger={(triggerProps) => {
          return (
            <Pressable {...triggerProps} className="inline-block" disabled={disabled}>
              <HStack className="flex p-[10px] justify-between items-center self-stretch rounded-[8px] border-[1px] border-solid border-[#CCC]">
                {value ? (
                  <HStack className="flex-1 items-center gap-[8px]">
                    <TextOrImageLogo text={value.name} size={24} />
                    <Text className="text-[#0A0A0A] text-[14px] font-500 leading-[20px]">{value.name}</Text>
                  </HStack>
                ) : (
                  <Text className="text-[#9d9b9b] text-[14px] font-500 leading-[20px]">{t('portfolio_page.search_selector.search')}</Text>
                )}
                {
                    !disabled && <RiArrowDownSLine size={16} color="#B3B3B3" />
                }
                
              </HStack>
            </Pressable>
          );
        }}
      >
        <PopoverBackdrop />
        <PopoverContent className="w-[430px]  max-h-[300px] overflow-auto p-[10px] rounded-[6px] border-[1px] border-solid border-[#E6E6E6] bg-[#FFF] shadow-[0px_8px_20px_0px_rgba(0,0,0,0.10)]">
          <PopoverBody>
            <Input className="flex-1 w-full px-[12px] py-[6px] items-center gap-[6px] rounded-[6px] bg-[#F7F7F7] border-none outline-none shadow-none data-[focus=true]:shadow-[none]">
              <InputSlot>
                <RiSearch2Line size={16} className="text-[#808080]" />
              </InputSlot>
              <InputField
                ref={inputRef}
                placeholder={t('portfolio_page.search_selector.search_placeholder')}
                value={keyword}
                onChangeText={handleSearch}
              />
            </Input>
            <input type="text" />
            {showEmpty ? (
              <Empty className="py-4" content={keyword.trim() ? t('portfolio_page.search_selector.no_result') : t('portfolio_page.search_selector.no_keyword')} />
            ) : (
              <>
                <VStack className="w-full h-full gap-2">
                  {loading ? (
                    <VStack className="w-full h-full gap-2">
                      {new Array(5).fill(0).map((_, index) => (
                        <SearchSkeleton key={index} />
                      ))}
                    </VStack>
                  ) : (
                    <>
                      {results.length > 0 && (
                        <VStack className="w-full h-full gap-2">
                          {results.map((item) => (
                            <SearchItem key={item.market + item.symbol} item={item} onPress={handleResultClick} />
                          ))}
                        </VStack>
                      )}
                    </>
                  )}

                  {(loading || results.length > 0) && historyList.length > 0 && <Divider className="bg-[#F5F5F5]" />}
                  {historyList.length > 0 && (
                    <>
                      <VStack className="w-full h-full gap-2">
                        <HStack className="w-full h-full p-2 justify-between items-center">
                          <Text className="self-stretch text-[#808080] font-Poppins text-[12px] font-not-italic font-[400] leading-[16px]">
                            {t('portfolio_page.search_selector.search_history')}
                          </Text>
                          <Pressable onPress={handleClearHistory}>
                            <Text className="flex-[1_0_0] text-[#05C697] text-right font-Poppins text-[12px] font-not-italic font-[400] leading-[16px]">
                              {t('portfolio_page.search_selector.clear_history')}
                            </Text>
                          </Pressable>
                        </HStack>
                        <VStack className="w-full h-full">
                          {historyList?.map((item, index) => (
                            <Pressable
                              onPress={() => handleHistoryClick(item)}
                              key={`history-${item.market}-${item.symbol}`}
                            >
                              <HStack
                                key={`${item.market}-${item.symbol}-${index}`}
                                className="items-center justify-between px-[8px] py-[10px] hover:bg-[rgba(5,198,151,0.05)]"
                              >
                                <Text
                                  className="flex-[1_0_0] text-[#0A0A0A] font-Poppins text-[14px] font-not-italic font-[400] leading-[20px]"
                                  isTruncated
                                  numberOfLines={1}
                                >
                                  {item.name}
                                </Text>
                                <Pressable
                                  onPress={(e) => {
                                    e.stopPropagation();
                                    const newHistoryList = [...historyList];
                                    newHistoryList.splice(index, 1);
                                    setHistoryList(newHistoryList);
                                  }}
                                >
                                  <RiCloseLine size={16} className="text-[#b3b3b3]" />
                                </Pressable>
                              </HStack>
                            </Pressable>
                          ))}
                        </VStack>
                      </VStack>
                    </>
                  )}
                </VStack>
              </>
            )}
          </PopoverBody>
        </PopoverContent>
      </Popover>
    </>
  );
}
