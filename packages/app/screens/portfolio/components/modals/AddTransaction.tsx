'use client';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>nt,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
} from '@quantum/components/ui/modal';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { RiCloseFill, RiEditLine, RiMoneyDollarCircleLine } from 'react-icons/ri';
import { portfolioStore } from '../../../../store/portfolio.store';
import { observer } from 'mobx-react-lite';
import { HStack } from '@quantum/components/ui/hstack';
import { Button, ButtonText } from '@quantum/components/ui/button';
import { Pressable } from '@quantum/components/ui/pressable';
import { useEffect, useMemo, useState } from 'react';
import { VStack } from '@quantum/components/ui/vstack';
import { SearchSelector } from './SearchSelector';
import { Input, InputField, InputSlot } from '@quantum/components/ui/input';
import dayjs from 'dayjs';
import { BigNumber } from 'bignumber.js';
import { useRequest } from 'ahooks';
import { addPortfolioTransaction } from '../../../../services/portfolio/portfolio.api';
import { PortfolioSide, type PortfolioTxAddParam } from '../../../../services/portfolio/portfolio.types';
import type { SearchResultItem } from '../../../../services/asset/asset.types';
import { Market } from '../../../../services/asset/asset.types';
import { useCustomToast } from '../../../../components/Toast/ToastProvider';
import { useTranslation } from 'react-i18next';
import DateTimePicker from '../../../../components/DateTimePicker/DateTimePicker';
import { useHandleErrorMessage } from '../../../../utils/handleErrorMessage';

function sanitizeInput(text: string, decimalPlaces: number = 16) {
  // 只允许输入正的小数，可限制小数位数
  // decimalPlaces: 小数位数限制，0表示只能输入整数
  // 1. 先移除所有非数字和小数点字符
  // 2. 如果decimalPlaces为0，则不允许小数点
  // 3. 限制小数点后位数

  if (decimalPlaces === 0) {
    // 只允许整数，移除所有非数字字符
    let sanitized = text.replace(/[^\d]/g, '');
    // 去除前导0
    if (sanitized.startsWith('0') && sanitized.length > 1) {
      sanitized = sanitized.replace(/^0+/, '');
      if (sanitized === '') sanitized = '0';
    }
    return sanitized;
  }

  let sanitized = text.replace(/[^\d.]/g, ''); // 只保留数字和小数点
  // 只保留第一个小数点
  const firstDot = sanitized.indexOf('.');
  if (firstDot !== -1) {
    sanitized = sanitized.slice(0, firstDot + 1) + sanitized.slice(firstDot + 1).replace(/\./g, '');
  }
  // 去除前导0（保留"0."的情况）
  if (sanitized.startsWith('0') && sanitized[1] !== '.' && sanitized.length > 1) {
    sanitized = sanitized.replace(/^0+/, '');
    if (sanitized === '') sanitized = '0';
  }
  // 限制小数点后位数
  if (firstDot !== -1) {
    const [intPart, decPart] = sanitized.split('.');
    sanitized = intPart + '.' + (decPart ? decPart.slice(0, decimalPlaces) : '');
  }
  // 只允许正数
  if (sanitized.startsWith('-')) {
    sanitized = sanitized.replace(/^-+/, '');
  }
  return sanitized;
}

function LabelBox({ label, children }: { label: string; children: React.ReactNode }) {
  return (
    <VStack className="gap-[6px]">
      <Text className="text-[#0A0A0A] text-[14px] font-500 leading-[20px]">{label}</Text>
      {children}
    </VStack>
  );
}

function AddTransactionForm({
  submit,
  submitLoading,
}: {
  submit: (params: PortfolioTxAddParam) => void;
  submitLoading: boolean;
}) {
  const { t } = useTranslation();
  const [type, setType] = useState('buy');
  const tabs = [
    {
      label: t('portfolio_page.add_transaction.buy'),
      value: 'buy',
    },
    {
      label: t('portfolio_page.add_transaction.sell'),
      value: 'sell',
    },
  ];

  const [date, setDate] = useState<number | string | null>(null);
  const [amount, setAmount] = useState('');
  const [price, setPrice] = useState('');
  const [fee, setFee] = useState('');
  const [note, setNote] = useState('');
  const [showFee, setShowFee] = useState(false);
  const [showNote, setShowNote] = useState(true);
  const total = useMemo(() => {
    if (amount === '' || price === '') {
      return '';
    }
    return Number(BigNumber(amount)
      .multipliedBy(price)
      .plus(fee || 0)
      .toFixed(8, BigNumber.ROUND_DOWN));
  }, [amount, price, fee]);

  const [selectedItem, setSelectedItem] = useState<SearchResultItem | null>(null);

  useEffect(() => {
    if (portfolioStore.paramsAddTransactionModal) {
      setSelectedItem({
        market: portfolioStore.paramsAddTransactionModal.market,
        symbol: portfolioStore.paramsAddTransactionModal.symbol,
        name: portfolioStore.paramsAddTransactionModal.name,
        price: '',
        change_rate: '',
      });
    } else {
      setSelectedItem(null);
    }
  }, [portfolioStore.paramsAddTransactionModal]);
  return (
    <Box className="h-full flex-1">
      <Box className="px-[24px] pb-[24px]">
        <VStack className="gap-[24px]">
          <HStack className="flex p-[4px] items-start gap-[2px] self-stretch rounded-[8px] bg-[rgba(0,0,0,0.04)] backdrop-blur-[2px]">
            {tabs.map((tab) => (
              <Pressable className="flex-1" key={tab.value} onPress={() => setType(tab.value)}>
                <HStack className={`items-center justify-center h-8 ${type === tab.value ? 'bg-[#ffff]' : ''}`}>
                  <Text
                    className={`text-[#808080] text-center text-[14px] font-400 leading-[20px] ${
                      type === tab.value ? 'text-[#0A0A0A]' : ''
                    }`}
                  >
                    {tab.label}
                  </Text>
                </HStack>
              </Pressable>
            ))}
          </HStack>
          <VStack className="gap-[16px]">
            <SearchSelector
              value={selectedItem}
              onSelect={(item) => {
                setSelectedItem(item);
              }}
              disabled={!!portfolioStore.paramsAddTransactionModal}
            />

            <HStack className="gap-4">
              <Box className="flex-1">
                <LabelBox label={t('portfolio_page.add_transaction.amount')}>
                  <Input className="flex py-0 pl-[0px] pr-[154px] items-center self-stretch rounded-[8px] bg-[#F0F0F0] border-[0] outline-none shadow-none data-[focus=true]:shadow-[none]">
                    <InputField
                      className="h-[48px]"
                      placeholder="0.00"
                      value={amount}
                      onChangeText={(text) => {
                        setAmount(sanitizeInput(text, selectedItem?.market === Market.CRYPTO ? 8 : 0));
                      }}
                    />
                  </Input>
                </LabelBox>
              </Box>
              <Box className="flex-1">
                <LabelBox label={t('portfolio_page.add_transaction.price_per_coin')}>
                  <Input className="flex py-0 pl-[0px] pr-[154px] items-center self-stretch rounded-[8px] bg-[#F0F0F0] border-[0] outline-none shadow-none data-[focus=true]:shadow-[none]">
                    <InputSlot className="pl-[12px]">$</InputSlot>
                    <InputField
                      className="pl-1 h-[48px]"
                      placeholder="0.00"
                      value={price}
                      onChangeText={(text) => {
                        setPrice(sanitizeInput(text, selectedItem?.market === Market.CRYPTO ? 8 : 2));
                      }}
                    />
                  </Input>
                </LabelBox>
              </Box>
            </HStack>
            <HStack className="gap-4 items-center">
              <DateTimePicker
                onChange={() => {}}
                onSelect={() => {}}
                onOk={(dateString, date) => {
                  setDate(date.valueOf());
                }}
              />
              {/* <Pressable onPress={() => setShowFee(!showFee)}>
                <Box
                  className={`flex w-[88px] h-[48px] px-[8px] py-[4px] justify-center items-center gap-[6px] rounded-[8px] bg-[#F0F0F0] ${
                    showFee ? 'bg-[rgba(5,198,151,0.10)]' : ''
                  }`}
                >
                  <HStack className={`gap-[6px] items-center text-[#0A0A0A] ${showFee ? 'text-[#05C697]' : ''}`}>
                    <RiMoneyDollarCircleLine size={16} />
                    <Text
                      className={`text-[#0A0A0A] text-[14px] font-500 leading-[20px] ${
                        showFee ? 'text-[#05C697]' : ''
                      }`}
                    >
                      费用
                    </Text>
                  </HStack>
                </Box>
              </Pressable> */}
              <Pressable onPress={() => setShowNote(!showNote)}>
                <Box
                  className={`flex w-[88px] h-[48px] px-[8px] py-[4px] justify-center items-center gap-[6px] rounded-[8px] bg-[#F0F0F0] ${
                    showNote ? 'bg-[rgba(5,198,151,0.10)]' : ''
                  }`}
                >
                  <HStack className={`gap-[6px] items-center text-[#0A0A0A] ${showNote ? 'text-[#05C697]' : ''}`}>
                    <RiEditLine size={16} />
                    <Text
                      className={`text-[#0A0A0A] text-[14px] font-500 leading-[20px] ${
                        showNote ? 'text-[#05C697]' : ''
                      }`}
                    >
                      {t('portfolio_page.add_transaction.note')}
                    </Text>
                  </HStack>
                </Box>
              </Pressable>
            </HStack>

            {showFee && (
              <Box className="flex-1">
                <LabelBox label={t('portfolio_page.add_transaction.add_fee')}>
                  <Input className="flex py-0 pl-[0px] pr-[154px] items-center self-stretch rounded-[8px] bg-[#F0F0F0] border-[0] outline-none shadow-none data-[focus=true]:shadow-[none]">
                    <InputSlot className="pl-[12px]">$</InputSlot>
                    <InputField
                      className="pl-1 h-[48px]"
                      placeholder="0.00"
                      value={fee}
                      onChangeText={(text) => {
                        setFee(sanitizeInput(text, selectedItem?.market === Market.CRYPTO ? 8 : 2));
                      }}
                    />
                  </Input>
                </LabelBox>
              </Box>
            )}
            {showNote && (
              <Box className="flex-1">
                <LabelBox label={t('portfolio_page.add_transaction.add_note')}>
                  <Input className="flex py-0 pl-[0px] pr-[154px] items-center self-stretch rounded-[8px] bg-[#F0F0F0] border-[0] outline-none shadow-none data-[focus=true]:shadow-[none]">
                    <InputField
                      className="h-[48px]"
                      placeholder={t('portfolio_page.add_transaction.note_placeholder')}
                      value={note}
                      onChangeText={(text) => {
                        setNote(text);
                      }}
                    />
                  </Input>
                </LabelBox>
              </Box>
            )}
            <Box className="flex-1">
              <LabelBox label={t('portfolio_page.add_transaction.total_cost')}>
                <Input
                  className="flex py-0 pl-[0px] pr-[154px] items-center self-stretch rounded-[8px] bg-[#F0F0F0] border-[0] outline-none shadow-none data-[focus=true]:shadow-[none]"
                  isDisabled={true}
                >
                  <InputSlot className="pl-[12px]">$</InputSlot>
                  <InputField className="pl-1 h-[48px]" placeholder="0.00" value={total} />
                </Input>
              </LabelBox>
            </Box>
          </VStack>
        </VStack>
      </Box>
      <HStack className="h-[84px] border-t border-solid border-[#E6E6E6] items-center justify-between px-[24px] py-[18px]">
        <Button
          className="h-[48px] px-[28px] rounded-[8px] bg-[#05C697] w-full"
          onPress={() => {
            if (!selectedItem) {
              return;
            }
            submit({
              market: selectedItem?.market,
              symbol: selectedItem?.symbol,
              side: type === 'buy' ? PortfolioSide.BUY : PortfolioSide.SELL,
              amount: Number(amount),
              price: Number(price),
              cost: Number(total),
              intro: showNote ? note : '',
              ts: Number(date ? date : null),
            });
          }}
          loading={submitLoading}
        >
          <ButtonText className="text-[#FFF] text-[16px] font-600 leading-[20px]">
            {t('portfolio_page.add_transaction.add_transaction')}
          </ButtonText>
        </Button>
      </HStack>
    </Box>
  );
}

const AddTransactionFormComp = observer(AddTransactionForm);

function AddTransaction({ onFinish }: { onFinish: () => void }) {
  const { t } = useTranslation();
  const show = portfolioStore.showAddTransactionModal;
  const onClose = () => {
    portfolioStore.setShowAddTransactionModal(false);
    portfolioStore.setParamsAddTransactionModal(null);
  };
  const { showToast } = useCustomToast();
  const { handleErrorMessage } = useHandleErrorMessage();
  const { run: submit, loading: submitLoading } = useRequest(
    async (params: PortfolioTxAddParam) => {
      if (!params) {
        return;
      }
      if (!params.ts) {
        handleErrorMessage({ msg: t('portfolio_page.add_transaction.check_input') });
        return;
      }
      try {
        const res = await addPortfolioTransaction(params);
        onFinish();
        await portfolioStore.portfolioPositionList.reload();
        onClose();
      } catch (error) {
        handleErrorMessage(error);
      }
    },
    {
      manual: true,
    },
  );
  return (
    <Modal isOpen={show} onClose={onClose} className="fixed inset-0 z-[1]">
      <ModalBackdrop className="bg-[#000000] opacity-70" />
      <ModalContent
        size="full"
        focusScope={false}
        className="p-0 pt-[32px]  max-w-[480px] w-full min-h-[320px]  rounded-[16px] bg-[#FFF] shadow-[0px_4px_25px_0px_rgba(0,0,0,0.10),0px_4px_10px_0px_rgba(0,0,0,0.04)]"
      >
        <ModalHeader className="px-[24px]" focusable={false}>
          <Box>
            <Text className="text-[#0A0A0A] text-[20px] font-600 leading-[24px]">
              {t('portfolio_page.add_transaction.title')}
            </Text>
          </Box>
          <ModalCloseButton className="w-[24px] h-[24px] items-center justify-center border rounded-[6px] border-solid border-[#cccccc]">
            <RiCloseFill size={16} className="text-[#0a0a0a]" />
          </ModalCloseButton>
        </ModalHeader>
        <ModalBody className="mt-3 mb-0  pb-0">
          <AddTransactionFormComp submit={submit} submitLoading={submitLoading} />
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}

export default observer(AddTransaction);
