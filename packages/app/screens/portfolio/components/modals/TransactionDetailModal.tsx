import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ModalCloseButton,
  ModalBody,
} from '@quantum/components/ui/modal';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { RiCloseFill } from 'react-icons/ri';
import { portfolioStore } from '../../../../store/portfolio.store';
import { observer } from 'mobx-react-lite';
import { VStack } from '@quantum/components/ui/vstack';
import { useRequest } from 'ahooks';
import { getPortfolioTransactionList } from '../../../../services/portfolio/portfolio.api';
import TableUi from '../../../../components/TableUi/TableUi';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { HStack } from '@quantum/components/ui/hstack';
import { PortfolioSide, type TransactionItem } from '../../../../services/portfolio/portfolio.types';
import TextOrImageLogo from '../../../../components/TextOrImageLogo';
import { averageNumber } from '../../../../uitls/number';
import dayjs from 'dayjs';

function TransactionDetail() {
  const { data = [], loading } = useRequest(async () => {
    if (!portfolioStore.paramsTransactionDetailModal) {
      return [];
    }
    const res = await getPortfolioTransactionList({
      pos_id: portfolioStore.paramsTransactionDetailModal.pos_id,
    });
    return res;
  });

  const { t } = useTranslation();

  const columns: any[] = useMemo(() => {
    const list = [
      {
        key: 'pair',
        title: '交易对',
        render: (value: any, item: TransactionItem, index: number) => (
          <HStack className="gap-1 items-center">
            <HStack>
              <TextOrImageLogo text={item.name || '111'} size={22} />
            </HStack>
            <Text className="flex-[1_0_0] text-[#0A0A0A] text-[12px] font-500 leading-[16px]">{item.symbol}/USDT</Text>
          </HStack>
        ),
      },
      {
        key: 'side',
        title: '交易类型',
        render: (value: any, item: TransactionItem, index: number) => (
          <Text
            className={` text-[#05C697] text-[12px] font-500 leading-[16px] ${
              item.side === PortfolioSide.BUY ? 'text-[#05C697]' : 'text-[#df4234]'
            }`}
          >
            {item.side === PortfolioSide.BUY ? '买入' : '卖出'}
          </Text>
        ),
      },
      {
        key: 'price',
        title: '成本价',
        render: (value: any, item: TransactionItem, index: number) => (
          <Text className="text-[#0A0A0A] text-[12px] font-500 leading-[16px]">${averageNumber(item.price)}</Text>
        ),
      },
      {
        key: 'amount',
        title: '数量',
        render: (value: any, item: TransactionItem, index: number) => (
          <Text
            className={` text-[#05C697] text-[12px] font-500 leading-[16px] ${
              item.side === PortfolioSide.BUY ? 'text-[#05C697]' : 'text-[#df4234]'
            }`}
          >
            {item.side === PortfolioSide.BUY ? '+' : '-'}
            {averageNumber(item.amount)}
          </Text>
        ),
      },
      {
        key: 'time',
        title: '时间',
        align: 'right',
        render: (value: any, item: TransactionItem, index: number) => (
          <Text className="text-[#0A0A0A] text-[12px] font-500 leading-[16px]">
            {dayjs(item.ts).format('YYYY-MM-DD HH:mm:ss')}
          </Text>
        ),
      },
    ];
    return list.filter(Boolean);
  }, [t]);

  return (
    <TableUi
      columns={columns}
      data={data}
      loading={loading}
      handleSort={() => {}}
      headerClassName="h-[32px] px-[8px] py-[0] items-center gap-[8px] self-stretch bg-[#0000000A] border-[0]"
      thClassName="px-[12px] py-0 "
      thTextClassName="text-[#4D4D4D] text-[12px] font-400 leading-[32px] h-[32px]"
      tdClassName='px-[12px]'
      tdTextClassName="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]"
    />
  );
}

function TransactionDetailModal() {
  const show = portfolioStore.showTransactionDetailModal;
  const onClose = () => {
    portfolioStore.setShowTransactionDetailModal(false);
    portfolioStore.setParamsTransactionDetailModal(null);
  };
  return (
    <Modal isOpen={show} onClose={onClose} className="fixed inset-0">
      <ModalBackdrop className="bg-[#000000] opacity-70" />
      <ModalContent
        size="full"
        focusScope={false}
        className="p-0 pt-[32px]  max-w-[600px] w-full min-h-[320px]  rounded-[16px] bg-[#FFF] shadow-[0px_4px_25px_0px_rgba(0,0,0,0.10),0px_4px_10px_0px_rgba(0,0,0,0.04)]"
      >
        <ModalHeader className="px-[24px]" focusable={false}>
          <Box>
            <Text className="text-[#0A0A0A] text-[20px] font-600 leading-[24px]">查看交易</Text>
          </Box>
          <ModalCloseButton className="w-[24px] h-[24px] items-center justify-center border rounded-[6px] border-solid border-[#cccccc]">
            <RiCloseFill size={16} className="text-[#0a0a0a]" />
          </ModalCloseButton>
        </ModalHeader>
        <ModalBody className="mt-3 mb-0  pb-[24px] px-[24px]">
          <TransactionDetail />
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}

export default observer(TransactionDetailModal);
