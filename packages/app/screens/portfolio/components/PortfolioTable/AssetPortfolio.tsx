'use client';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import TableUi, { type TableColumn } from '../../../../components/TableUi/TableUi';
import { useTranslation } from 'react-i18next';
import { useMap, useRequest } from 'ahooks';
import { getExchangeSpotMarkets } from '../../../../services/crypto_exchange/crypto_exchange.api';
import type { MarketSymbol } from '../../../../services/crypto_exchange/crypto_exchange.types';
import { averageNumber } from '../../../../uitls/number';
import TextOrImageLogo from '../../../../components/TextOrImageLogo';
import ChangeValue from '../../../../components/ChangeValue';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { Button } from '@quantum/components/ui/button';
import { RiAddCircleLine, RiMore2Fill } from 'react-icons/ri';
import { Menu, MenuItem, MenuItemLabel } from '@quantum/components/ui/menu';
import { settingStore } from '../../../../store/setting.store';
import { useEffect, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { portfolioStore } from '../../../../store/portfolio.store';
import { authStore } from '../../../../store/auth.store';
import {
  PortfolioSide,
  type PortfolioTxAddParam,
  type PositionItem,
} from '../../../../services/portfolio/portfolio.types';
import { addPortfolioTransaction } from '../../../../services/portfolio/portfolio.api';
import { useCustomToast } from '../../../../components/Toast/ToastProvider';
import { BigNumber } from 'bignumber.js';
import dayjs from 'dayjs';
import TransactionDetailModal from '../modals/TransactionDetailModal';

function AssetOperate({ item }: { item: PositionItem }) {
  const { t } = useTranslation();
  const { showToast } = useCustomToast();
  const { run: submit, loading: submitLoading } = useRequest(
    async (params: PortfolioTxAddParam) => {
      if (!params) {
        return;
      }
      try {
        const res = await addPortfolioTransaction(params);
        portfolioStore.portfolioPositionList.reload();
      } catch (error) {
        console.error(error);
        showToast({
          title: t('portfolio_page.asset_portfolio.operation_failed'),
          description: t('portfolio_page.asset_portfolio.check_input'),
          status: 'error',
        });
      }
    },
    {
      manual: true,
    },
  );
  return (
    <HStack className="justify-end items-center px-[12px] gap-2">
      <Button
        variant="link"
        className="p-0"
        onPress={() => {
          portfolioStore.setParamsAddTransactionModal({
            market: item.market,
            symbol: item.symbol,
            name: item.name,
          });
          portfolioStore.setShowAddTransactionModal(true);
        }}
      >
        <RiAddCircleLine size={16} className="text-[#05C697]" />
      </Button>
      <Menu
        placement="right"
        offset={5}
        trigger={({ ...triggerProps }) => {
          return (
            <Button variant="link" className="p-0" {...triggerProps}>
              <RiMore2Fill size={16} className="text-[#05C697]" />
            </Button>
          );
        }}
      >
        <MenuItem
          disabled={submitLoading}
          onPress={() => {
            submit({
              market: item.market,
              symbol: item.symbol,
              side: PortfolioSide.SELL,
              amount: BigNumber(item.pos).toNumber(),
              price: BigNumber(item.price).toNumber(),
              cost: BigNumber(item.pos).multipliedBy(item.price).toNumber(),
              intro: '',
              ts: dayjs().valueOf(),
            });
          }}
        >
          <MenuItemLabel>{t('portfolio_page.asset_portfolio.remove_token')}</MenuItemLabel>
        </MenuItem>
        <MenuItem
          onPress={() => {
            portfolioStore.setParamsTransactionDetailModal({
              pos_id: item.pos_id,
            });
            portfolioStore.setShowTransactionDetailModal(true);
          }}
        >
          <MenuItemLabel>{t('portfolio_page.asset_portfolio.view_transaction')}</MenuItemLabel>
        </MenuItem>
      </Menu>
    </HStack>
  );
}

const AssetOperateComp = observer(AssetOperate);

function AssetPortfolio() {
  const { t } = useTranslation();

  const loading = portfolioStore.portfolioPositionList.loading;
  const data = portfolioStore.portfolioPositionList.data;

  const columns: any[] = useMemo(() => {
    const list = [
      {
        key: 'index',
        title: '#',
        render: (value: any, item: PositionItem, index: number) => (
          <Text className="px-[12px] text-[#0A0A0A] text-[12px] font-500 leading-[16px]">{index + 1}</Text>
        ),
      },
      {
        key: 'currency',
        title: t('portfolio_page.asset_portfolio.currency'),
        width: '230px',
        render: (value: any, item: PositionItem, index: number) => (
          <HStack className="gap-[12px] px-3">
            <Box className="w-[22px] h-[22px]">
              <TextOrImageLogo text={item.name} size={22} />
            </Box>
            <HStack className="gap-1 flex-1">
              <Text className="text-[#0A0A0A] text-right text-[14px] font-600 leading-[20px]">{item.name}</Text>
              <Text className="text-[#808080] text-right text-[12px] font-400 leading-[20px]">{item.symbol}</Text>
            </HStack>
          </HStack>
        ),
      },
      {
        key: 'price',
        width: '80px',
        title: t('portfolio_page.asset_portfolio.price'),
        render: (value: any, item: PositionItem, index: number) => (
          <HStack className="">
            <Text className="px-3 text-[#0A0A0A] text-[12px] font-500 leading-[16px]">
              ${averageNumber(item.price)}
            </Text>
          </HStack>
        ),
      },
      settingStore.assetPortfolioTable.price_change_24h && {
        key: 'price_change_24h',
        width: '130px',
        title: t('portfolio_page.asset_portfolio.price_change_24h'),
        render: (value: any, item: PositionItem, index: number) => (
          <Text className="px-[12px] text-[#0A0A0A] text-[12px] font-500 leading-[16px]">
            {averageNumber(item.price_change_24h, 2)}%
          </Text>
        ),
      },
      settingStore.assetPortfolioTable.price_change_7d && {
        key: 'price_change_7d',
        width: '130px',
        title: t('portfolio_page.asset_portfolio.price_change_7d'),
        render: (value: any, item: PositionItem, index: number) => (
          <Text className="px-[12px] text-[#0A0A0A] text-[12px] font-500 leading-[16px]">
            {averageNumber(item.price_change_7d, 2)}%
          </Text>
        ),
      },
      settingStore.assetPortfolioTable.market_cap && {
        key: 'market_cap',
        width: '130px',
        title: t('portfolio_page.asset_portfolio.market_cap'),
        render: (value: any, item: PositionItem, index: number) => (
          <Text className="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]">
            ${averageNumber(item.market_cap, 2)}
          </Text>
        ),
      },
      {
        key: 'pos',
        width: '130px',
        title: t('portfolio_page.asset_portfolio.holding'),
        render: (value: any, item: PositionItem, index: number) => (
          <Text className="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]">
            {averageNumber(item.pos, 2)}
          </Text>
        ),
      },
      {
        key: 'volume',
        title: t('portfolio_page.asset_portfolio.pnl'),
        width: '150px',
        render: (value: any, item: PositionItem, index: number) => (
          <VStack className="gap-[2px] px-[12px] items-start">
            <Text className=" text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]">
              {Number(item.pnl_value) > 0 ? '+' : '-'}${averageNumber(Math.abs(Number(item.pnl_value)), 2)}
            </Text>
            <ChangeValue change={`${averageNumber(item.pnl_rate.replace('%', ''), 2)}%`} />
          </VStack>
        ),
      },
      {
        key: 'operate',
        title: t('portfolio_page.asset_portfolio.operation'),
        align: 'right',
        width: '100px',
        render: (value: any, item: PositionItem, index: number) => <AssetOperateComp item={item} />,
      },
    ];
    return list.filter(Boolean);
  }, [t, settingStore.assetPortfolioTable]);

  return (
    <>
      <TableUi
        columns={columns}
        data={data}
        loading={loading}
        handleSort={() => {}}
        tableClassName="min-w-[900px] w-[auto]"
        headerClassName="px-[12px]"
        thClassName="px-[12px] py-0"
        thTextClassName="text-[#4D4D4D] text-center text-[14px] font-not-italic font-400 leading-[20px]"
        tdTextClassName="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]"
        pageConfig={{
          pageSize: settingStore.assetPortfolioTable.page_size,
        }}
      />
      <TransactionDetailModal />
    </>
  );
}

export default observer(AssetPortfolio);
