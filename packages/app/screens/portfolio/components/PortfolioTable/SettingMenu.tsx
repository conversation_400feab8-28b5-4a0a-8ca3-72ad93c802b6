import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import {
  <PERSON><PERSON>,
  <PERSON>dalB<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalB<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalCloseButton,
} from '@quantum/components/ui/modal';
import { RiCloseFill } from 'react-icons/ri';
import { Text } from '@quantum/components/ui/text';
import { Divider } from '@quantum/components/ui/divider';
import { VStack } from '@quantum/components/ui/vstack';
import { settingStore } from '../../../../store/setting.store';
import { observer } from 'mobx-react-lite';
import { Switch } from '@quantum/components/ui/switch';
import colors from 'tailwindcss/colors';
import { Pressable } from '@quantum/components/ui/pressable';

function SettingMenu({ show, onClose }: { show: boolean; onClose: () => void }) {
  const list = [
    {
      label: '价格更改',
      options: [
        {
          label: '24 小时',
          key: 'price_change_24h',
          type: 'switch',
          value: settingStore.assetPortfolioTable.price_change_24h,
        },
        {
          label: '7 天',
          key: 'price_change_7d',
          type: 'switch',
          value: settingStore.assetPortfolioTable.price_change_7d,
        },
      ],
    },
    {
      label: '衡量指标',
      options: [
        {
          label: '总市值',
          key: 'market_cap',
          type: 'switch',
          value: settingStore.assetPortfolioTable.market_cap,
        },
      ],
    },
    {
      label: '其他',
      options: [
        {
          label: '行',
          key: 'page_size',
          type: 'tab',
          value: settingStore.assetPortfolioTable.page_size,
          options: [
            {
              label: '10',
              key: '10',
              value: 10,
            },
            {
              label: '20',
              key: '20',
              value: 20,
            },
            {
              label: '50',
              key: '50',
              value: 50,
            },
          ],
        },
      ],
    },
  ];
  return (
    <Modal isOpen={show} onClose={onClose} className="fixed inset-0">
      <ModalBackdrop className="bg-[#000000] opacity-70" />
      <ModalContent
        size="full"
        className="p-5 max-w-[320px] w-full min-h-[320px]  rounded-[16px] bg-[#FFF] shadow-[0px_4px_25px_0px_rgba(0,0,0,0.10),0px_4px_10px_0px_rgba(0,0,0,0.04)]"
      >
        <ModalHeader>
          <Box></Box>
          <ModalCloseButton className="w-[24px] h-[24px] items-center justify-center border rounded-[6px] border-solid border-[#cccccc]">
            <RiCloseFill size={16} className="text-[#0a0a0a]" />
          </ModalCloseButton>
        </ModalHeader>
        <ModalBody className="mt-3">
          <VStack className="gap-6">
            {list.map((item) => (
              <VStack className="gap-2">
                <HStack className="gap-2 items-center">
                  <Text className="text-[#05C697] text-[14px] font-600 leading-[20px]">{item.label}</Text>
                  <Divider className="flex-1 h-[1px] bg-[#E6E6E6]" orientation="vertical" />
                </HStack>
                {item.options.map((option) => (
                  <HStack className="gap-2 items-center justify-between ">
                    <Text className="text-[#4D4D4D] text-[14px] font-400 leading-[20px]">{option.label}</Text>
                    {option.type === 'switch' && (
                      <Switch
                        onValueChange={(value) => {
                          settingStore.setAssetPortfolioTable(
                            option.key as keyof typeof settingStore.assetPortfolioTable,
                            value,
                          );
                        }}
                        defaultValue={typeof option.value === 'boolean' ? option.value : false}
                        isDisabled={false}
                        trackColor={{ false: colors.gray[300], true: colors.blue[600] }}
                        thumbColor={colors.gray[50]}
                        {...({ activeThumbColor: colors.gray[50] } as any)}
                      />
                    )}
                    {option.type === 'tab' && 'options' in option && (
                      <HStack className="p-[4px] items-start gap-[2px] rounded-[6px] bg-[rgba(0,0,0,0.04)] backdrop-blur-[2px]">
                        {option.options.map((tabOption) => (
                          <Pressable
                            key={tabOption.key}
                            className={`flex px-[16px] py-[2px] justify-center items-center rounded-[2px] shadow-[0px_0px_6px_0px_rgba(0,0,0,0.10)] ${settingStore.assetPortfolioTable.page_size === tabOption.value ? 'bg-[#fff]' : ''}`}
                            onPress={() => {
                              settingStore.setAssetPortfolioTable(
                                option.key as keyof typeof settingStore.assetPortfolioTable,
                                tabOption.value,
                              );
                            }}
                          >
                            <Text className={`text-[#808080] text-center text-[12px] font-400 leading-[16px] ${settingStore.assetPortfolioTable.page_size === tabOption.value ? 'text-[#0A0A0A]' : ''}`}>
                              {tabOption.label}
                            </Text>
                          </Pressable>
                        ))}
                      </HStack>
                    )}
                  </HStack>
                ))}
              </VStack>
            ))}
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}

export default observer(SettingMenu);
