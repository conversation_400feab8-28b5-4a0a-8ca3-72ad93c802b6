'use client';
import { Box } from '@quantum/components/ui/box';
import { Button, ButtonText } from '@quantum/components/ui/button';
import { HStack } from '@quantum/components/ui/hstack';
import { observer } from 'mobx-react-lite';
import { RiAddCircleLine } from 'react-icons/ri';
import { Tabs } from '../../../../components/Tabs/Tabs';
import AssetPortfolio from './AssetPortfolio';
import Watchlist from './Watchlist';
import SettingMenu from './SettingMenu';
import { useEffect, useMemo, useState } from 'react';
import { portfolioStore } from '../../../../store/portfolio.store';
import { useTranslation } from 'react-i18next';
import { watchlistStore } from '../../../../store/watchlist.store';
import { isNil } from 'lodash';
import { ScrollView } from '@quantum/components/ui/scroll-view';
import { Popover, PopoverBackdrop, PopoverBody, PopoverContent } from '@quantum/components/ui/popover';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import { scroller, Element } from 'react-scroll';

function PortfolioTable() {
  const { t } = useTranslation();

  const tabs = [
    {
      label: t('portfolio_page.table.asset_portfolio'),
      children: <ScrollView className='overflow-x-auto'>
        <AssetPortfolio key="AssetPortfolio" />
      </ScrollView>,
    },
    {
      label: t('portfolio_page.table.watchlist'),
      children: <ScrollView className='overflow-x-auto'>
        <Watchlist key="WatchListTable" />
        </ScrollView>,
    },
  ];
  const [showSettingMenu, setShowSettingMenu] = useState(false);
  const defaultIndex = useMemo(() => {

    if (!portfolioStore.portfolioPositionList.data?.length && !!watchlistStore.watchlist.data?.length) {
      return 1;
    }
    return 0;
  }, [watchlistStore.watchlist.data, portfolioStore.portfolioPositionList.data]);

  const [selectedIndex, setSelectedIndex] = useState(defaultIndex);
  useEffect(() => {
    if (portfolioStore.showGuideStep2) {
      setSelectedIndex(1);
    }
  }, [portfolioStore.showGuideStep2])
  return (
    <Box id={'portfolio_table'}>
      <Tabs
        selectedIndex={selectedIndex}
        onSelectedIndexChange={setSelectedIndex}
        labelClassName="text-[#808080] text-[18px] font-500 leading-[54px]"
        labelActiveClassName="text-[#0A0A0A] text-[18px] font-600 leading-[54px]"
        height={54}
        operate={(index) => {
          if (index !== 0) {
            return (
              <HStack className="gap-[12px] items-center">
                <Popover
                  shouldOverlapWithTrigger={true}
                  isOpen={portfolioStore.showGuideStep2}
                  // onClose={() => portfolioStore.setGuideStep1(false)}
                  placement="left"
                  trigger={(triggerProps) => {
                    return (
                      <Button
                        id={'btn-portfolio_page.empty.add_asset'}
                        className="flex min-w-[113px] px-[16px] py-[8px] justify-center items-center gap-[8px] rounded-[60px] bg-[rgba(5,198,151,0.10)]"
                        {...triggerProps}
                        onPress={() => portfolioStore.setShowAddAssetsModal(true)}

                      >
                        <ButtonText className="text-[#05C697] text-[14px] font-500 leading-[20px]">
                          <HStack className="items-center gap-[8px]">
                            <RiAddCircleLine size={16} />
                            <Box>{t('portfolio_page.empty.add_asset')}</Box>
                          </HStack>
                        </ButtonText>
                      </Button>
                    );
                  }}
                >
                  <PopoverBackdrop className={'bg-[#000000]'}  />
                  <PopoverContent className={'bg-transparent p-0 border-0 rounded-0 max-w-[562px]'}>
                    <PopoverBody>
                      <HStack className={'items-center gap-[15px]'}>
                        <Box className={'bg-[#fff] rounded-[12px] flex-1 overflow-hidden'}>
                          <VStack className={'flex-1 px-[24px] py-[16px] gap-[12px]'}
                                  style={{
                                    background: 'linear-gradient(180deg, rgba(31, 203, 219, 0.10) 0%, rgba(5, 198, 151, 0.10) 100%)'
                                  }}
                          >
                            <VStack className={'gap-1'}>
                              <Text className={'text-[#000] text-[18px] font-600 leading-[24px]'}>
                                {t('portfolio_page.empty.add_asset')}
                              </Text>
                              <Text className={'self-stretch text-[#4D4D4D] text-[14px] font-400 leading-[20px]'}>
                                {t('portfolio_page.guide_watch_tips')}
                              </Text>
                            </VStack>
                            <Box>
                              <Button variant={'outline'} className={'w-[88px] h-[28px] rounded-[18px] border-[1px] border-solid border-[#b3b3b3]'}
                                      onPress={() => {
                                        portfolioStore.setGuideStep2(false);
                                        portfolioStore.setShowSuggestionModal(true)
                                      }}
                              >
                                <ButtonText className={'text-[#0A0A0A] text-[12px] font-500 leading-[16px]'}>
                                  {t('portfolio_page.next')}
                                </ButtonText>
                              </Button>
                            </Box>
                          </VStack>
                        </Box>
                        <Box className={'bg-[#fff] min-w-[113px] rounded-[60px] flex-[0 0 113px]'}>
                          <HStack className="flex h-[36px] min-w-[113px] px-[16px] py-[8px] justify-center items-center gap-[8px] rounded-[60px] bg-[#05C6971A]">
                            <Text className="text-[#05C697] text-[14px] font-500 leading-[20px]">
                              <HStack className="items-center gap-[8px]">
                                <RiAddCircleLine size={16} />
                                <Box>{t('portfolio_page.empty.add_asset')}</Box>
                              </HStack>
                            </Text>
                          </HStack>
                        </Box>
                      </HStack>
                    </PopoverBody>
                  </PopoverContent>
                </Popover>

              </HStack>
            );
          }
          return (
            <HStack className="gap-[12px] items-center">
              <Button
                className="flex min-w-[113px] px-[16px] py-[8px] justify-center items-center gap-[8px] rounded-[60px] bg-[rgba(5,198,151,0.10)]"
                onPress={() => portfolioStore.setShowAddTransactionModal(true)}
              >
                <ButtonText className="text-[#05C697] text-[14px] font-500 leading-[20px]">
                  <HStack className="items-center gap-[8px]">
                    <RiAddCircleLine size={16} />
                    <Box>{t('portfolio_page.table.add_transaction')}</Box>
                  </HStack>
                </ButtonText>
              </Button>
              <Button variant="link" onPress={() => setShowSettingMenu(true)}>
                <ButtonText className="text-[#0A0A0A] text-[14px] font-500 leading-[20px]">
                  {t('portfolio_page.table.customize_features')}
                </ButtonText>
              </Button>
            </HStack>
          );
        }}
      >
        {tabs.map((tab) => (
          <Tabs.Tab key={tab.label} label={tab.label}>
            {tab.children}
          </Tabs.Tab>
        ))}
      </Tabs>
      <SettingMenu show={showSettingMenu} onClose={() => setShowSettingMenu(false)} />
    </Box>
  );
}

export default observer(PortfolioTable);
