'use client';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import TableUi, { type TableColumn } from '../../../../components/TableUi/TableUi';
import type { WatchListItem } from '../../../../services/watchlist/watchlist.types';
import ChangeValue from '../../../../components/ChangeValue';
import { watchlistStore } from '../../../../store/watchlist.store';
import { authStore } from '../../../../store/auth.store';
import { averageNumber } from '../../../../uitls/number';
import { HStack } from '@quantum/components/ui/hstack';
import { useRequest } from 'ahooks';
import { useTranslation } from 'react-i18next';
import TextOrImageLogo from '../../../../components/TextOrImageLogo';
import { observer } from 'mobx-react-lite';
import LineGraph from '../../../../components/LineGraph/LineGraph';
import { getKline } from '../../../../services/kline/kline.api';
import { AliKlineInterval } from '../../../../services/kline/kline.types';
import { ButtonStar } from '../../../../components/ButtonStar/ButtonStar';
import { useToggleStar } from '../../../../hooks/useToggleStar';
import { sleep } from '../../../../uitls';
import { useCustomToast } from '../../../../components/Toast/ToastProvider';
import { useEffect, useMemo, useState } from 'react';
import type { Market } from '../../../../services/asset/asset.types';

function WatchListItem({ item }: { item: WatchListItem }) {
  const { t } = useTranslation();
  const { runAsync: callOnToggleStar } = useToggleStar(item.market, item.symbol);
  const [defaultStar, setDefaultStar] = useState(false);

  function initDefaultStar() {
    const res = watchlistStore.watchlist.data?.some((ele) => {
      return ele.market === item.market && ele.symbol === item.symbol;
    });
    setDefaultStar(!!res);
  }

  useEffect(() => {
    initDefaultStar();
  }, [watchlistStore.watchlist.data]);
  const { showToast } = useCustomToast();
  const handleOnToggleStar = async (value: boolean) => {
    try {
      setDefaultStar(value);
      await callOnToggleStar(value);
      await sleep(1000);
      await watchlistStore.watchlist.reload();
    } catch (error: any) {
      initDefaultStar();
      showToast({
        title: t('portfolio_page.watchlist.error'),
        description: error.msg,
        status: 'error',
      });
    }
  };
  return (
    <HStack className="gap-[16px]">
      <ButtonStar
        className="flex w-full h-full items-end"
        size={16}
        defaultStar={defaultStar}
        onToggleStar={handleOnToggleStar}
      />
      <HStack className="gap-2">
        <TextOrImageLogo text={item.symbol} size={22} />
        <Text className="text-[#0A0A0A] text-right text-[14px] font-600 leading-[20px]">{item.name}</Text>
      </HStack>
    </HStack>
  );
}

export function WatchListTrend({
  item,
  className,
}: {
  item: {
    market: Market;
    symbol: string;
  };
  className?: string;
}) {
  const { data = [], loading } = useRequest(
    async () => {
      const res = await getKline(item.market, item.symbol, AliKlineInterval.DAY_1, 7);
      return res;
    },
    {
      refreshDeps: [item.market, item.symbol],
      cacheKey: `kline-${item.market}-${item.symbol}-day-7`,
      staleTime: 600 * 1000, // 10分钟缓存
    },
  );

  return (
    <Box className={`w-[67px] h-[40px] ${className}`}>
      <LineGraph data={data} loading={loading} isInteractive={false} />
    </Box>
  );
}

function Watchlist() {
  const loading = watchlistStore.watchlist.loading;
  const { t, ready, i18n } = useTranslation();
  useRequest(
    async () => {
      await watchlistStore.watchlist.reload();
    },
    {
      ready: !!ready && !!authStore.isLoggedIn,
      refreshDeps: [i18n.language, authStore.currentToken],
    },
  );
  const [list, setList] = useState<WatchListItem[]>([]);
  useEffect(() => {
    if (!list.length) {
      const list = watchlistStore.watchlist.data || [];
      setList(list);
    }
  }, [watchlistStore.watchlist.data]);

  const columns: TableColumn[] = useMemo(() => {
    return [
      {
        key: 'index',
        title: t('portfolio_page.watchlist.currency'),
        render: (value: any, item: WatchListItem, index: number) => <WatchListItem item={item} />,
      },
      {
        key: 'symbol',
        title: t('portfolio_page.watchlist.code'),
      },
      {
        key: 'price',
        title: t('portfolio_page.watchlist.price'),
        render: (value: any, item: WatchListItem, index: number) => (
          <HStack className="">
            <Text className=" px-3 text-[#0A0A0A] text-[12px] font-500 leading-[16px]">
              ${averageNumber(item.price)}
            </Text>
          </HStack>
        ),
      },
      {
        key: 'change_rate',
        title: t('portfolio_page.watchlist.change_24h'),
        render: (value: any, item: WatchListItem, index: number) => (
          <ChangeValue change={averageNumber(item.change_rate, 2) + '%'} />
        ),
      },
      {
        key: 'kline',
        title: t('portfolio_page.watchlist.trend'),
        render: (value: any, item: WatchListItem, index: number) => {
          return <WatchListTrend item={item} />;
        },
      },
      {
        key: 'market_cap',
        title: t('portfolio_page.watchlist.market_cap'),
        render: (value: any, item: WatchListItem, index: number) => (
          <HStack className="">
            <Text className=" px-3 text-[#0A0A0A] text-[12px] font-500 leading-[16px]">
              ${averageNumber(item.market_cap)}
            </Text>
          </HStack>)
      },
    ];
  }, [t]);
  return (
    <TableUi
      columns={columns}
      data={list}
      loading={loading && list.length === 0}
      handleSort={() => {}}
      headerClassName="px-[12px]"
      thClassName="px-[12px] py-0"
      thTextClassName="text-[#4D4D4D] text-center text-[14px] font-not-italic font-400 leading-[20px]"
      tdTextClassName="px-[12px] text-[#4D4D4D] text-center text-[14px] font-not-italic font-500 leading-[20px]"
    />
  );
}

export default observer(Watchlist);
