'use client';
import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { LineChart } from 'echarts/charts';
import { DataZoomComponent, GridComponent, TooltipComponent } from 'echarts/components';
import * as echarts from 'echarts/core';
import { SVGRenderer } from '@wuba/react-native-echarts';
import dayjs from 'dayjs';
import { averageNumber } from '../../../uitls/number';

echarts.use([LineChart, DataZoomComponent, GridComponent, TooltipComponent, SVGRenderer]);

interface VolumeLineGraphProps {
  data: any[];
  loading?: boolean;
}

export default function VolumeLineGraph({ data: defaultData = [], loading = false }: VolumeLineGraphProps) {
  const { t } = useTranslation();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);

  // 数据处理
  const { xData, volumeData, isEmpty } = React.useMemo(() => {
    if (!defaultData?.length) {
      // 当数据为空时，创建最近四个月的日期标签
      const now = dayjs();
      const emptyXData = [
        now.subtract(3, 'month').format('MM/DD'),
        now.subtract(2, 'month').format('MM/DD'),
        now.subtract(1, 'month').format('MM/DD'),
        now.format('MM/DD'),
      ];
      const emptyVolumeData = [4000, 4000, 4000, 4000];
      return { xData: emptyXData, volumeData: emptyVolumeData, isEmpty: true };
    }
    const xData = defaultData.map((item: any) => dayjs.unix(item.t).format('MM/DD'));
    const volumeData = defaultData.map((item: any) => Number(item.v));
    return { xData, volumeData, isEmpty: false };
  }, [defaultData]);

  // 计算Y轴范围
  const { volumeMin, volumeMax } = React.useMemo(() => {
    if (isEmpty) {
      return {
        volumeMin: -10,
        volumeMax: 10,
      };
    }

    if (!volumeData.length) {
      return {
        volumeMin: 0,
        volumeMax: 100,
      };
    }

    const volumeMin = Math.min(...volumeData);
    const volumeMax = Math.max(...volumeData);

    // 计算合适的范围，留出10%的边距
    const volumeRange = volumeMax - volumeMin;

    return {
      volumeMin: Math.max(0, volumeMin - volumeRange * 0.1), // 确保最小值不小于0
      volumeMax: volumeMax + volumeRange * 0.1,
    };
  }, [volumeData, isEmpty]);

  const isDown = defaultData?.length > 1 ? defaultData[defaultData.length - 1]?.v - defaultData[0]?.v < 0 : false;
  const lineColor = isDown ? '#CC3D3D' : '#00B268';

  // 静态配置
  const staticOption = React.useMemo(
    () => ({
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const idx = params?.[0]?.dataIndex ?? 0;
          const item = defaultData[idx];
          const timeString = item.t ? dayjs.unix(item.t).format('YYYY-MM-DD HH:mm:ss') : params?.[0]?.axisValue;
          let html = `${timeString || ''}<br/>`;
          params.forEach((p: any) => {
            html += `<span style=\"color:${p.color}\">●</span> ${t('portfolio_page.chart.income')}: <b>${
              isEmpty ? 0 : averageNumber(p.data)
            }</b><br/>`;
          });
          return html;
        },
      },
      grid: { left: 10, right: 50, top: 10, bottom: 20 },
      xAxis: {
        type: 'category',
        axisLabel: { show: true },
        boundaryGap: false,
      },
      yAxis: {
        type: 'value',
        position: 'right',
        show: !isEmpty,
        scale: true,
        axisLabel: {
          formatter: (v: number) => averageNumber(v),
        },
        splitLine: {
          show: false,
        },
        // min: volumeMin,
        // max: volumeMax,
      },
      dataZoom: [
        {
          type: 'slider',
          show: false,
          xAxisIndex: 0,
          bottom: 10,
          height: 20,
          start: 0,
          end: 100,
          labelFormatter: (value: number) => {
            if (xData.length && value >= 0 && value < xData.length) {
              return dayjs(xData[Math.floor(value)]).format('MM/DD');
            }
            return '';
          },
        },
      ],
      series: [
        {
          name: t('chart.volume'),
          type: 'line',
          showSymbol: false,
          connectNulls: true,
          smooth: true,
          itemStyle: {
            color: isEmpty ? '#CCC' : lineColor,
          },
          lineStyle: {
            color: isEmpty ? '#CCC' : lineColor,
            width: 2,
          },
          areaStyle: {
            color: isEmpty
              ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(204, 204, 204, 0.3)' },
                  { offset: 1, color: 'rgba(204, 204, 204, 0.05)' },
                ])
              : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: isDown ? 'rgba(204, 61, 61, 0.5)' : 'rgba(5, 198, 151, 0.5)', // 顶部颜色，100%不透明度
                  },
                  {
                    offset: 1,
                    color: isDown ? 'rgba(204, 61, 61, 0.05)' : 'rgba(5, 198, 151, 0.05)', // 底部颜色，完全透明
                  },
                ]),
          },
          z: 2,
        },
      ],
    }),
    [volumeMin, volumeMax, t, xData, isEmpty],
  );

  useEffect(() => {
    if (!chartRef.current) return;

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        useDirtyRect: true,
      });

      chartInstance.current.setOption(staticOption);
    }

    // 控制加载状态
    if (loading) {
      // 清空数据
      chartInstance.current.setOption({
        xAxis: {
          data: [],
        },
        series: [
          {
            data: [],
          },
        ],
      });
      chartInstance.current.showLoading({
        text: 'Loading...',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        textColor: '#000',
        spinnerRadius: 10,
        lineWidth: 5,
        showSpinner: true,
        showText: true,
        animation: true,
      });
    } else {
      chartInstance.current.hideLoading();
      // 更新图表数据（包括空数据的情况）
      chartInstance.current.setOption({
        xAxis: {
          data: xData,
        },
        series: [
          {
            data: volumeData,
          },
        ],
      });
    }

    const resize = () => chartInstance.current && chartInstance.current.resize();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', resize);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', resize);
      }
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [xData, volumeData, staticOption, loading]);

  return <div ref={chartRef} style={{ width: '100%', height: '206px' }} />;
}
