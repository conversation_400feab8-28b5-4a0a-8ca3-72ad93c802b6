import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import { Button, ButtonText } from '@quantum/components/ui/button';
import { RiAddCircleLine } from 'react-icons/ri';
import { Box } from '@quantum/components/ui/box';
import { Grid, GridItem } from '@quantum/components/ui/grid';
import ChangeValue from '../../../components/ChangeValue';
import TextOrImageLogo from '../../../components/TextOrImageLogo';
import { Divider } from '@quantum/components/ui/divider';
import PortfolioLineChart from './PortfolioLineChart';
import { useRequest } from 'ahooks';
import { portfolioStore } from '../../../store/portfolio.store';
import { authStore } from '../../../store/auth.store';
import { useEffect } from 'react';
import { averageNumber } from '../../../uitls/number';
import { isNil } from 'lodash';
import { observer } from 'mobx-react-lite';
import { useTranslation } from 'react-i18next';
import { Popover, PopoverBackdrop, PopoverContent, PopoverBody, PopoverArrow } from '@quantum/components/ui/popover';
import { scroller } from 'react-scroll';

function PortfolioDataItem({
  title,
  content,
  color,
}: {
  title: string | React.ReactNode;
  content: React.ReactNode;
  color?: string;
}) {
  return (
    <VStack className="h-full gap-[4px] p-3 border-[1px] border-solid border-[#F5F5F5] bg-[#F7F7F7] rounded-[12px]">
      {typeof title === 'string' ? (
        <Text className={`text-[${color}] text-[18px] font-600 leading-[24px]`}>{title}</Text>
      ) : (
        title
      )}
      {content}
    </VStack>
  );
}

function PortfolioData() {
  const { t } = useTranslation();
  const loading = portfolioStore.portfolioGroupOverview.loading;
  const data = portfolioStore.portfolioGroupOverview.data;

  return (
    <VStack className="gap-[24px]">
      <HStack className="items-center justify-between">
        <VStack className="gap-[4px]">
          <Text className="text-[#0A0A0A] text-[48px] font-700 leading-[56px]">
            {!isNil(data?.total_value) ? `$${averageNumber(data?.total_value)}` : '--'}
          </Text>
          <Text className="self-stretch text-[#05C697] text-[14px] font-500 leading-[20px]">
            {isNil(data?.value_change_24h)
              ? '--'
              : `${Number(data?.value_change_24h) > 0 ? '+' : '-'}$${averageNumber(data?.value_change_24h)}`}
            (24h)
          </Text>
        </VStack>
        <Popover
          shouldOverlapWithTrigger={true}
          isOpen={portfolioStore.showGuideStep1}
          // onClose={() => portfolioStore.setGuideStep1(false)}
          placement="left"
          trigger={(triggerProps) => {
            return (
              <Button
                className="flex h-[36px] min-w-[113px] px-[16px] py-[8px] justify-center items-center gap-[8px] rounded-[60px] bg-[rgba(5,198,151,0.10)]"

                {...triggerProps}
                onPress={() => portfolioStore.setShowAddTransactionModal(true)}
              >
                <ButtonText className="text-[#05C697] text-[14px] font-500 leading-[20px]">
                  <HStack className="items-center gap-[8px]">
                    <RiAddCircleLine size={16} />
                    <Box>{t('portfolio_page.table.add_transaction')}</Box>
                  </HStack>
                </ButtonText>
              </Button>
            );
          }}
        >
          <PopoverBackdrop className={'bg-[#000000]'}  />
          <PopoverContent className={'bg-transparent p-0 border-0 rounded-0 max-w-[562px]'}>
            <PopoverBody>
              <HStack className={'items-center gap-[15px]'}>
                <Box className={'bg-[#fff] rounded-[12px] flex-1 overflow-hidden'}>
                  <VStack className={'flex-1 px-[24px] py-[16px] gap-[12px]'}
                          style={{
                            background: 'linear-gradient(180deg, rgba(31, 203, 219, 0.10) 0%, rgba(5, 198, 151, 0.10) 100%)'
                          }}
                  >
                    <VStack className={'gap-1'}>
                      <Text className={'text-[#000] text-[18px] font-600 leading-[24px]'}>
                        {
                          t('portfolio_page.table.add_transaction')
                        }
                      </Text>
                      <Text className={'self-stretch text-[#4D4D4D] text-[14px] font-400 leading-[20px]'}>
                        {
                          t('portfolio_page.guide_tx_tips')
                        }
                      </Text>
                    </VStack>
                    <Box>
                      <Button variant={'outline'} className={'w-[88px] h-[28px] rounded-[18px] border-[1px] border-solid border-[#b3b3b3]'}
                      onPress={() => {
                        portfolioStore.setGuideStep1(false);
                        scroller.scrollTo('portfolio_table')
                        portfolioStore.setGuideStep2(true)
                      }}
                      >
                        <ButtonText className={'text-[#0A0A0A] text-[12px] font-500 leading-[16px]'}>
                          {t('portfolio_page.next')}
                        </ButtonText>
                      </Button>
                    </Box>
                  </VStack>
                </Box>
                <Box className={'bg-[#fff] min-w-[113px] rounded-[60px] flex-[0 0 113px]'}>
                  <HStack className="flex h-[36px] min-w-[113px] px-[16px] py-[8px] justify-center items-center gap-[8px] rounded-[60px] bg-[#05C6971A]">
                    <Text className="text-[#05C697] text-[14px] font-500 leading-[20px]">
                      <HStack className="items-center gap-[8px]">
                        <RiAddCircleLine size={16} />
                        <Box>{t('portfolio_page.table.add_transaction')}</Box>
                      </HStack>
                    </Text>
                  </HStack>
                </Box>
              </HStack>
            </PopoverBody>
          </PopoverContent>
        </Popover>
      </HStack>

      <Grid
        className="flex-1 gap-6"
        _extra={{
          className: 'grid-cols-3',
        }}
      >
        <GridItem
          _extra={{
            className: 'col-span-1 md:col-span-1',
          }}
        >
          <PortfolioDataItem
            color={Number(data?.pnl_value) > 0 ? '#05C697' : '#FF4D4F'}
            title={
              isNil(data?.value_change_24h)
                ? '--'
                : `${Number(data?.value_change_24h) > 0 ? '+' : '-'}$${averageNumber(Math.abs(data?.value_change_24h))}`
            }
            content={
              <HStack className="items-center gap-[8px]">
                <Text className="text-[#4D4D4D] text-[14px] font-500 leading-[20px]">
                  {t('portfolio_page.data.portfolio_change_24h')}
                </Text>
                {isNil(data?.value_change_24h) ? (
                  <Text className="text-[#4D4D4D] text-[14px] font-500 leading-[20px]">--</Text>
                ) : (
                  <ChangeValue change={`${averageNumber(data?.value_change_24h, 2)}%`} />
                )}
              </HStack>
            }
          />
        </GridItem>
        <GridItem
          _extra={{
            className: 'col-span-1 md:col-span-1',
          }}
        >
          <PortfolioDataItem
            color={Number(data?.pnl_value) > 0 ? '#05C697' : '#FF4D4F'}
            title={
              isNil(data?.pnl_value)
                ? '--'
                : `${Number(data?.pnl_value) > 0 ? '+' : '-'}$${averageNumber(Math.abs(data?.value_change_24h))}`
            }
            content={
              <HStack className="items-center gap-[8px]">
                <Text className="text-[#4D4D4D] text-[14px] font-500 leading-[20px]">
                  {t('portfolio_page.data.total_pnl')}
                </Text>
                {isNil(data?.pnl_rate) ? (
                  <Text className="text-[#4D4D4D] text-[14px] font-500 leading-[20px]">--</Text>
                ) : (
                  <ChangeValue change={`${averageNumber(data?.pnl_rate, 2)}%`} />
                )}
              </HStack>
            }
          />
        </GridItem>
        <GridItem
          _extra={{
            className: 'col-span-1 md:col-span-1',
          }}
        >
          <PortfolioDataItem
            title={
              <HStack className="items-center gap-[8px]">
                <TextOrImageLogo size={24} text="Bitcoin" />
                <Text className="text-[#0A0A0A] text-right text-[14px] font-600 leading-[20px]">Bitcoin</Text>
              </HStack>
            }
            content={
              <HStack className="items-center gap-[8px]">
                <Text className="text-[#4D4D4D] text-[14px] font-500 leading-[20px]">
                  {t('portfolio_page.data.highest_change_24h')}
                </Text>
                {isNil(data?.btc_change_high_24h) ? (
                  <Text className="text-[#4D4D4D] text-[14px] font-500 leading-[20px]">--</Text>
                ) : (
                  <ChangeValue change={`${averageNumber(data?.btc_change_high_24h, 2)}%`} />
                )}
              </HStack>
            }
          />
        </GridItem>
      </Grid>
      <Divider className="bg-[#F5F5F5]" />
      <PortfolioLineChart />
    </VStack>
  );
}

export default observer(PortfolioData);
