'use client';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import { getExchangeVolumeChart } from '../../../services/crypto_exchange/crypto_exchange.api';
import { useRequest } from 'ahooks';
import { useState } from 'react';
import { Pressable } from '@quantum/components/ui/pressable';
import { useTranslation } from 'react-i18next';
import VolumeLineGraph from './PortfolioLineGraph';
import { getPortfolioPerformanceChart } from '../../../services/portfolio/portfolio.api';

export default function PortfolioLineChart() {
  const { t } = useTranslation();
  const [selectedRange, setSelectedRange] = useState('24h');

  // 时间范围选项
  const timeRangeOptions = [
    { label: t('exchange_page.volume_chart.time_range.24h'), value: '24h' },
    { label: t('exchange_page.volume_chart.time_range.7d'), value: '7d' },
    { label: t('exchange_page.volume_chart.time_range.1m'), value: '1m' },
    { label: t('exchange_page.volume_chart.time_range.3m'), value: '3m', active: true },
    { label: t('exchange_page.volume_chart.time_range.1y'), value: '1y' },
  ];

  const { data: volumeData, loading } = useRequest(
    async () => {
      let days: 1 | 7 | 30 | 90 | 365 = 30;
      switch (selectedRange) {
        case '24h':
          days = 1;
          break;
        case '7d':
          days = 7;
          break;
        case '1m':
          days = 30;
          break;
        case '3m':
          days = 90;
          break;
        case '1y':
          days = 365;
          break;
        default:
          days = 30;
      }
      const res = await getPortfolioPerformanceChart({ days });
      return res;
    },
    {
      refreshDeps: [selectedRange],
    },
  );

  return (
    <VStack className="gap-[24px]">
      <HStack className="items-center justify-between gap-2">
        <Text className="text-[#0A0A0A] text-[14px] font-400 leading-[20px]">{t('portfolio_page.earnings_performance')}</Text>
        <HStack className="items-center justify-end flex-1">
          <HStack className="bg-[rgba(0,0,0,0.04)] rounded-[8px] p-1 gap-[2px]">
            {timeRangeOptions.map((option) => (
              <Pressable
                key={option.value}
                className={`py-1.5 px-4 rounded-md ${option.value === selectedRange ? 'bg-white' : 'bg-transparent'}`}
                onPress={() => setSelectedRange(option.value)}
              >
                <Text
                  className={`text-xs ${
                    option.value === selectedRange ? 'text-gray-900 font-medium' : 'text-gray-500 font-normal'
                  }`}
                >
                  {option.label}
                </Text>
              </Pressable>
            ))}
          </HStack>
        </HStack>
      </HStack>

      <VolumeLineGraph data={volumeData || []} loading={loading} />
    </VStack>
  );
}
