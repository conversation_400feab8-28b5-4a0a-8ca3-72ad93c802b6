'use client';
import Logo from '@quantum/shared/assets/logo.svg';
import Image from '@unitools/image';
import { Button, ButtonText } from '@quantum/components/ui/button';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import SuggestionModal from './modals/SuggestionModal';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Skeleton } from '@quantum/components/ui/skeleton';
import { portfolioStore } from '../../../store/portfolio.store';

export default function PortfolioNoneData({
  loading,
  handleStartGuide,
}: {
  handleStartGuide?: () => void;
  addAsset?: () => void;
  getSuggestion?: () => void;
  loading?: boolean;
}) {
  const { t } = useTranslation();
  const [showSuggestion, setShowSuggestion] = useState(false);
  return (
    <>
      <VStack className="gap-[32px] items-center justify-center">
        {loading ? (
          <Skeleton className="w-[240px] h-[240px] rounded-full" />
        ) : (
          <Image source={Logo} alt="logo" width={240} height={240} />
        )}
        <VStack className="gap-[8px] items-center justify-center">
          {loading ? (
            <>
              <Skeleton className="w-[192px] h-[24px] rounded-[8px]" />
              <Skeleton className="w-[292px] h-[20px] rounded-[8px]" />
            </>
          ) : (
            <>
              <Text className="text-[#0A0A0A] text-[18px] font-not-italic font-600 leading-[24px] text-center">
                {t('portfolio_page.empty.title')}
              </Text>
              <Text className="text-[#808080] text-[16px] font-not-italic font-400 leading-[20px] text-center">
                {t('portfolio_page.empty.description')}
              </Text>
            </>
          )}
        </VStack>
      </VStack>
      <VStack className="mt-10 gap-[12px] items-center justify-center">
        {loading ? (
          <>
            <Skeleton className="w-[292px] h-[48px] rounded-[8px]" />
            <Skeleton className="w-[292px] h-[48px] rounded-[8px]" />
          </>
        ) : (
          <>
            <Button
              className="max-w-[292px] w-full h-[48px] rounded-[8px] bg-[#05C697]"
              onPress={() => handleStartGuide?.()}
              isDisabled={loading}
            >
              <ButtonText>{t('portfolio_page.button.create_profile')}</ButtonText>
            </Button>
            {/*<Button*/}
            {/*  className="max-w-[292px] w-full h-[48px] rounded-[8px] bg-[rgba(5,198,151,0.10)]"*/}
            {/*  onPress={() => getSuggestion?.()}*/}
            {/*  isDisabled={loading}*/}
            {/*>*/}
            {/*  <ButtonText className="text-[#05C697]">{t('portfolio_page.empty.get_suggestion')}</ButtonText>*/}
            {/*</Button>*/}
          </>
        )}
      </VStack>
    </>
  );
}
