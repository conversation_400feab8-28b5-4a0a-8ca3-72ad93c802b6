'use client';
import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import ScrollMessageBar from '../../components/ScrollMessageBar';
import PortfolioData from './components/PortfolioData';

import { useEffect, useMemo, useState } from 'react';
import PortfolioNoneData from './components/PortfolioNoneData';
import { authStore } from '../../store/auth.store';
import { observer } from 'mobx-react-lite';
import AssetProportion from './components/AssetProportion/AssetProportion';
import PortfolioTable from './components/PortfolioTable/PortfolioTable';
import { portfolioStore } from '../../store/portfolio.store';
import AddAssets from './components/modals/AddAssets';
import { useCustomToast } from '../../components/Toast/ToastProvider';
import AddTransaction from './components/modals/AddTransaction';
import SuggestionModal from './components/modals/SuggestionModal';
import { useTranslation } from 'react-i18next';
import { watchlistStore } from '../../store/watchlist.store';
import DateTimePicker from '../../components/DateTimePicker/DateTimePicker';
import { useRequest } from 'ahooks';
import { getPortfolioUserGuide, markPortfolioUserGuide } from '../../services/portfolio/portfolio.api';

function Portfolio() {
  const { t } = useTranslation();


  const {loading: userGuideLoading, data: userGuideData, run: runUserGuide, mutate: mutateUserGuide} = useRequest(async () => {
    if (!authStore.isLoggedIn) {
      return false;
    }
    const res = await getPortfolioUserGuide();
    return res.result;
  }, {
    refreshDeps: [authStore.currentToken, authStore.isLoggedIn],
  });

  const [showDetailPage, setShowDetailPage] = useState(false);
  const isNullData = useMemo(() => {
    if (!authStore.isLoggedIn) {
      return true;
    }
    return !userGuideData
    // if (!userGuideData) {
    //   return true;
    // }
    // return !portfolioStore.portfolioPositionList.data?.length && !watchlistStore.watchlist.data?.length;
  }, [authStore.isLoggedIn, userGuideData]);
  function handleAddAsset() {
    if (!authStore.isLoggedIn) {
      authStore.setShowLoginModal(true);
      return;
    }
    portfolioStore.setShowAddAssetsModal(true);
  }

  const [showSuggestion, setShowSuggestion] = useState(false);
  function handleGetSuggestion() {
    if (!authStore.isLoggedIn) {
      authStore.setShowLoginModal(true);
      return;
    }
    setShowSuggestion(true);
  }


  useEffect(() => {
    if (!authStore.isLoggedIn) {
      portfolioStore.portfolioGroupOverview.setData(null);
      portfolioStore.portfolioPositionList.setData(null);
      watchlistStore.watchlist.setData(null);
      return
    }
    portfolioStore.portfolioGroupOverview.fetch();
    portfolioStore.portfolioPositionList.reload();
    watchlistStore.watchlist.reload();
  }, [authStore.currentToken, authStore.isLoggedIn]);
  function handleAddAssetsFinish() {
    markPortfolioUserGuide()
    mutateUserGuide(true)
  }

  function handleStartGuide() {
    if (!authStore.isLoggedIn) {
      authStore.setShowLoginModal(true);
      return;
    }
    portfolioStore.setGuideStep1(true)
    setShowDetailPage(true);
  }
  return (
    <>
      <Box className="w-full px-[60px]">

        <AddAssets onFinish={handleAddAssetsFinish} />
        <AddTransaction onFinish={handleAddAssetsFinish} />
        <SuggestionModal onFinish={handleAddAssetsFinish} show={portfolioStore.showSuggestionModal} onClose={() => portfolioStore.setShowSuggestionModal(false)} />

        {isNullData && !showDetailPage ? (
          <Box className="pt-[128px] pb-[48px]">
            <PortfolioNoneData handleStartGuide={handleStartGuide} addAsset={handleAddAsset} getSuggestion={handleGetSuggestion} loading={userGuideLoading ||watchlistStore.watchlist.loading || portfolioStore.portfolioPositionList.loading} />
          </Box>
        ) : (
          <Box className="max-w-[1156px] w-full py-[48px] mx-auto">
            <VStack className="gap-[32px]">
              <HStack className="gap-[32px]">
                <Box className="flex-1 p-4 rounded-[12px] border-[1px] border-solid border-[#E6E6E6] bg-[#FFF]">
                  <PortfolioData />
                </Box>
                <Box className="w-[330px] px-10 py-8 rounded-[12px] border-[1px] border-solid border-[#E6E6E6] bg-[#FFF]">
                  <AssetProportion />
                </Box>
              </HStack>

              <Box className="w-full p-4 rounded-[12px] border-[1px] border-solid border-[#E6E6E6] bg-[#FFF]">
                <PortfolioTable />
              </Box>
            </VStack>
          </Box>
        )}
      </Box>
    </>
  );
}

export default observer(Portfolio);
