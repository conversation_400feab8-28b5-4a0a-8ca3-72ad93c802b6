import { HStack } from '@quantum/components/ui/hstack';
import { Text } from '@quantum/components/ui/text';
import { useMemo } from 'react';
import { RiArrowDownSFill, RiArrowUpSFill } from 'react-icons/ri';
import { averageNumber } from '../../uitls/number';

/**
 * 涨跌幅组件
 * 根据变化值显示不同颜色和箭头图标
 */
interface ChangeValueProps {
  change: string;
  className?: string;
  iconSize?: number;
  space?: string | number;
  hideIcon?: boolean;
}
export default function ChangeValue({ change, className = '', iconSize = 16, space = '2px', hideIcon = false }: ChangeValueProps) {
  const changeValue = useMemo(() => {
    let value = change;
    if (!change.includes('%')) {
      value = averageNumber(Number(change) * 100, 2) + '%';
    }
    if (!change.startsWith('+') && !change.startsWith('-')) {
      value = '+' + value;
    }
    return value;
  }, [change]);
  // 判断涨跌幅是否为 0 或 0.00%
  const isZero = changeValue === '0%' || changeValue === '0.00%';

  // 根据涨跌幅判断使用上升或下降箭头
  const isPositive = changeValue.startsWith('+');
  const textColor = isZero ? 'text-[#808080]' : isPositive ? 'text-[#00AD6B]' : 'text-[#d93526]';

  // 导入箭头图标
    const ArrowIcon = isZero ? null : isPositive ? RiArrowUpSFill : RiArrowDownSFill;

  return (
    <HStack className={`gap-[${space}] items-center`}>
      <Text className={`${textColor} font-Inter text-[12px] font-not-italic font-[600] leading-[1] ${className}`}>
        {changeValue}
      </Text>
      {!hideIcon && ArrowIcon && <ArrowIcon size={iconSize} className={textColor} />}
    </HStack>
  );
}
