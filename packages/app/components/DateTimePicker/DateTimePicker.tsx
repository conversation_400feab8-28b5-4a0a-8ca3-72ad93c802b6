'use client';
import { DatePicker } from '@arco-design/web-react';

const { RangePicker } = DatePicker;
const style = {
  width: 224,
  height: 48,
  borderRadius: 6,
  background: '#F0F0F0',
};

import '@arco-design/web-react/dist/css/arco.css';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { RiCalendarLine } from 'react-icons/ri';

import { ConfigProvider } from '@arco-design/web-react';
import enUS from '@arco-design/web-react/es/locale/en-US';
import zhCN from '@arco-design/web-react/es/locale/zh-CN';
import { useTranslation } from 'react-i18next';

const DateTimePicker = ({
  onChange,
  onSelect,
  onOk,
}: {
  onChange: (value: any) => void;
  onSelect: (value: any) => void;
  onOk: (dateString: string, date: Dayjs) => void;
}) => {
  const {i18n} = useTranslation();
  const locale = i18n.language === 'en' ? enUS : zhCN;
  return (
    <>
      <ConfigProvider locale={locale}>
        <DatePicker
          style={style}
          showTime={{
            defaultValue: '00:00:00',
          }}
          icons={{
            inputSuffix: <RiCalendarLine size={20} color="#808080" />,
          }}
          format="YYYY-MM-DD HH:mm:ss"
          onChange={onChange}
          onSelect={onSelect}
          onOk={onOk}
          triggerProps={{
            style: {
              zIndex: 999999,
              height: 48,
            },
          }}
          panelRender={(panel) => {
            return <div style={{}}>{panel}</div>;
          }}
          disabledDate={(current) => current.isAfter(dayjs())}
        />
      </ConfigProvider>
    </>
  );
};

export default DateTimePicker;
