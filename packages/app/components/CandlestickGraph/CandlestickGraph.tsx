'use client';
import { useEffect, useMemo, useRef, useState } from 'react';
import * as echarts from 'echarts/core';
import { CandlestickChart } from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  DataZoomComponent,
  TitleComponent,
  DataZoomInsideComponent,
  DataZoomSliderComponent,
  MarkLineComponent,
  MarkPointComponent,
} from 'echarts/components';
import { SVGRenderer } from '@wuba/react-native-echarts';
import dayjs from 'dayjs';
import type { KLineDataItem } from '../../services/kline/kline.types';
import { useTranslation } from 'react-i18next';
import { averageNumber } from '../../uitls/number';

echarts.use([
  SVGRenderer,
  CandlestickChart,
  GridComponent,
  TooltipComponent,
  DataZoomComponent,
  TitleComponent,
  DataZoomInsideComponent,
  DataZoomSliderComponent,
  MarkLineComponent,
  MarkPointComponent,
]);

interface CandlestickGraphProps {
  isInteractive?: boolean;
  data?: KLineDataItem[];
  loading?: boolean;
}

// 创建通用的时间格式化函数
const formatTime = (timestamp: number | string) => {
  try {
    // 确保时间戳是数字类型
    const numTimestamp = typeof timestamp === 'string' ? Number(timestamp) : timestamp;
    // 使用标准格式
    return dayjs(numTimestamp).format('YYYY-MM-DD HH:mm');
  } catch (e) {
    console.error('时间格式化错误:', e);
    return '--';
  }
};

export default function CandlestickGraph({ isInteractive = true, data, loading = false }: CandlestickGraphProps) {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setIsLoading(loading);
  }, [loading]);

  const chartContainRef = useRef<HTMLDivElement>(null);
  const myChartRef = useRef<any>(null);
  const roRef = useRef<any>(null);
  // 使用ref存储缩放范围，而不是每次数据变化都重新计算
  const zoomRangeRef = useRef<{ start: number; end: number }>({ start: 0, end: 100 });

  // 将K线数据格式转换为内部使用的格式
  const formatData = useMemo(() => {
    if (data && data.length > 0) {
      // 将K线数据转换为内部格式
      return data.map((item) => ({
        ts: Number(item[0]), // 时间戳
        open: Number(item[1]), // 开盘价
        high: Number(item[2]), // 最高价
        low: Number(item[3]), // 最低价
        close: Number(item[4]), // 收盘价
        volume: Number(item[5]), // 成交量
      }));
    }
    return [];
  }, [data]);

  const dateList = useMemo(() => {
    return formatData.filter((item) => item.ts).map((item) => item.ts * 1000);
  }, [formatData]);

  const candlestickData = useMemo(() => {
    return formatData
      .filter((item) => item.ts)
      .map((item) => [
        item.open, // 开盘价
        item.close, // 收盘价
        item.low, // 最低价
        item.high, // 最高价
      ]);
  }, [formatData]);

  const isSetZoomRange = useRef(false);
  // 首次有数据时计算默认缩放范围
  useEffect(() => {
    // 只有在dateList首次有数据且尚未设置过zoomRange时执行计算
    if (dateList.length > 0 && zoomRangeRef.current.start === 0 && zoomRangeRef.current.end === 100) {
      const totalPoints = dateList.length;
      const pointsToShow = 50;

      if (totalPoints <= pointsToShow) {
        // 数据点少于或等于50个，全部显示
        zoomRangeRef.current = { start: 0, end: 100 };
      } else {
        if (isSetZoomRange.current) return;
        // 计算显示最后50个点对应的百分比
        const startPercent = Math.max(0, ((totalPoints - pointsToShow) / totalPoints) * 100);
        zoomRangeRef.current = { start: startPercent, end: 100 };
      }
      isSetZoomRange.current = true;
    }
  }, [dateList]);

  // 仅在组件首次加载时初始化图表
  useEffect(() => {
    // 初始化图表实例和ResizeObserver
    function initChart() {
      if (!chartContainRef.current) return;

      // 如果已经存在图表实例，先销毁它
      if (myChartRef.current) {
        myChartRef.current.dispose();
      }

      myChartRef.current = echarts.init(chartContainRef.current, 'light', {
        renderer: 'svg',
        useDirtyRect: true, // 增加渲染性能
      });

      // 如果已存在ResizeObserver，先断开连接
      if (roRef.current) {
        roRef.current.disconnect();
      }

      // 创建新的ResizeObserver
      roRef.current = new ResizeObserver((entries) => {
        if (myChartRef.current) {
          myChartRef.current.resize();
        }
      });

      // 开始观察
      roRef.current.observe(chartContainRef.current);
    }

    initChart();

    // 组件卸载时清理资源
    return () => {
      // 清理时断开ResizeObserver连接
      if (roRef.current) {
        roRef.current.disconnect();
      }
      // 清理图表事件监听和实例
      if (myChartRef.current) {
        myChartRef.current.off('dataZoom');
        myChartRef.current.dispose();
        myChartRef.current = null;
      }
    };
  }, []); // 空依赖数组，只在首次渲染时执行

  // 只在数据或交互设置发生变化时更新图表选项
  useEffect(() => {
    // 如果图表实例不存在或没有数据，不执行更新
    if (!myChartRef.current || !formatData?.length) return;

    // 获取最后一个收盘价用于标线
    const lastPrice = formatData.length > 0 ? formatData[formatData.length - 1].close : 0;

    // 判断趋势方向（用于颜色设置）
    const isDown = formatData.length > 1 ? formatData[formatData.length - 1].close - formatData[0].open < 0 : false;
    const lineColor = isDown ? '#CC3D3D' : '#00B268';

    const option = {
      // 根据交互状态设置是否响应交互
      silent: !isInteractive,
      hoverLayerThreshold: isInteractive ? Infinity : 0,

      animation: isInteractive, // 根据交互状态启用/禁用动画效果

      // 添加全局axisPointer配置
      axisPointer: {
        link: [{ xAxisIndex: 'all' }], // 链接所有x轴的指示器
        snap: true, // 全局启用吸附功能
        lineStyle: {
          color: lineColor,
          width: 1,
        },
        label: {
          backgroundColor: lineColor,
        },
      },

      tooltip: {
        show: isInteractive, // 根据交互状态显示/隐藏tooltip
        trigger: 'axis',
        confine: true, // 确保tooltip不会超出图表区域
        position: function (
          point: [number, number],
          params: any,
          dom: HTMLElement,
          rect: { x: number; y: number; width: number; height: number },
          size: { contentSize: [number, number]; viewSize: [number, number] },
        ) {
          // 计算tooltip的位置，使其跟随鼠标但不溢出边界
          const [x, y] = point;

          // 水平方向位置计算
          let tooltipX = x + 10; // 默认位置在鼠标右侧10px
          if (tooltipX + size.contentSize[0] > size.viewSize[0]) {
            // 如果会溢出右边界，改为显示在鼠标左侧
            tooltipX = x - size.contentSize[0] - 10;
          }

          // 垂直方向位置计算
          let tooltipY = y + 10; // 默认位置在鼠标下方10px
          if (tooltipY + size.contentSize[1] > size.viewSize[1]) {
            // 如果会溢出底部边界，改为显示在鼠标上方
            tooltipY = y - size.contentSize[1] - 10;
          }

          return [tooltipX, tooltipY];
        },
        formatter(params: any) {
          // 尝试从不同属性获取时间值
          let timestamp;
          if (params[0].axisValue) {
            timestamp = params[0].axisValue;
          } else if (params[0].name) {
            timestamp = params[0].name;
          } else if (dateList && dateList[params[0].dataIndex]) {
            timestamp = dateList[params[0].dataIndex];
          } else {
            // 如果都获取不到，使用当前时间作为后备
            timestamp = Date.now();
          }

          // 使用dayjs格式化时间
          let formattedDate = '';
          try {
            formattedDate = formatTime(timestamp);
          } catch (e) {
            console.error('日期格式化错误:', e);
            formattedDate = '时间数据无效';
          }

          // 获取当前K线数据
          const dataIndex = params[0].dataIndex;
          let klineData;

          if (data && data[dataIndex]) {
            klineData = data[dataIndex];
          } else if (formatData && formatData[dataIndex]) {
            const item = formatData[dataIndex];
            klineData = [0, item.open, item.high, item.low, item.close, item.volume];
          }

          // 构建tooltip内容
          if (klineData) {
            return `
            <div style='font-weight: 400; font-family: "PingFang SC Regular";color: #0A0A0A; font-size: 14px; line-height: 20px;'>
              <div>${t('chart.time')}: ${formattedDate}</div>
              <div>${t('chart.open')}: ${averageNumber(klineData[1])}</div>
              <div>${t('chart.high')}: ${averageNumber(klineData[2])}</div>
              <div>${t('chart.low')}: ${averageNumber(klineData[3])}</div>
              <div>${t('chart.close')}: ${averageNumber(klineData[4])}</div>
              <div>${t('chart.volume')}: ${averageNumber(klineData[5])}</div>
            </div>
            `;
          } else {
            // 如果没有具体K线数据，显示简化版tooltip
            const candleData = params[0].data;
            return `
            <div style='font-weight: 400; font-family: "PingFang SC Regular";color: #0A0A0A; font-size: 14px; line-height: 20px;'>
              <div>${t('chart.time')}: ${formattedDate}</div>
              <div>${t('chart.open')}: ${averageNumber(candleData[0])}</div>
              <div>${t('chart.close')}: ${averageNumber(candleData[1])}</div>
              <div>${t('chart.low')}: ${averageNumber(candleData[2])}</div>
              <div>${t('chart.high')}: ${averageNumber(candleData[3])}</div>
            </div>
            `;
          }
        },
        axisPointer: {
          type: 'cross',
          lineStyle: {
            color: lineColor,
            width: 1,
            type: 'solid',
          },
        },
      },

      xAxis: {
        type: 'category',
        data: dateList,
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        axisTick: { show: false },
        splitLine: { show: false },
        show: isInteractive,
        axisLabel: {
          formatter: function (value: number) {
            return formatTime(value);
          },
          showMinLabel: true,
          showMaxLabel: true,
        },
        axisPointer: {
          show: isInteractive,
          snap: true,
          label: {
            show: isInteractive,
            formatter: function (params: { value: number }) {
              return formatTime(params.value);
            },
          },
        },
        silent: !isInteractive,
      },

      yAxis: {
        scale: true,
        splitLine: { show: true },
        position: 'right',
        show: isInteractive,
        axisLabel: {
          inside: false,
          margin: 8,
          formatter: function (value: number) {
            return averageNumber(value);
          },
        },
        axisLine: {
          show: false,
        },
        axisPointer: {
          show: isInteractive,
          label: {
            show: isInteractive,
            formatter: function (params: { value: number }) {
              return averageNumber(params.value);
            },
          },
        },
        silent: !isInteractive,
      },

      dataZoom: isInteractive
        ? [
            {
              type: 'inside',
              start: zoomRangeRef.current.start,
              end: zoomRangeRef.current.end,
              zoomLock: false,
              throttle: 50,
              rangeMode: ['value', 'value'],
              filterMode: 'filter',
              disabled: false,
              zoomOnMouseWheel: true,
              moveOnMouseMove: true,
              preventDefaultMouseMove: false,
              orient: 'horizontal',
              zoomInCursor: 'pointer',
              zoomOutCursor: 'pointer',
            },
          ]
        : [],

      series: [
        {
          name: '蜡烛图',
          type: 'candlestick',
          data: candlestickData,
          itemStyle: {
            color: '#00B268',
            color0: '#CC3D3D',
            borderColor: '#00B268',
            borderColor0: '#CC3D3D',
          },
          emphasis: {
            disabled: !isInteractive,
            scale: isInteractive,
            itemStyle: {
              shadowColor: lineColor,
              shadowBlur: 5,
              borderWidth: 2,
            },
          },
          cursor: isInteractive ? 'pointer' : 'default',
          silent: !isInteractive,
          hoverAnimation: isInteractive,

          // 添加当前价格标线
          markLine: {
            silent: true,
            symbol: ['none', 'none'],
            lineStyle: {
              color: lineColor,
              type: 'dashed',
              width: 1,
            },
            emphasis: {
              disabled: false,
            },
            label: {
              show: true,
              position: 'insideStart',
              formatter: `${t('last_price')}: ${averageNumber(lastPrice)}`,
              backgroundColor: lineColor,
              color: '#fff',
              padding: [3, 6],
              borderRadius: 3,
              fontSize: 12,
              distance: 8,
              align: 'left',
            },
            precision: 18,
            data: [
              {
                name: '当前价格',
                type: 'value',
                valueDim: 'y',
                yAxis: lastPrice,
              },
            ],
          },
        },
      ],

      grid: {
        top: isInteractive ? 10 : 0,
        left: 10,
        right: 50,
        bottom: isInteractive ? 30 : 0,
        containLabel: true,
      },
    };

    myChartRef.current.setOption(option);

    // 监听缩放事件，用于更新缩放范围
    myChartRef.current.off('dataZoom');
    myChartRef.current.on('dataZoom', function (params: any) {
      // 更新缩放范围
      if (params.batch && params.batch[0]) {
        if (params.batch[0].start !== undefined && params.batch[0].end !== undefined) {
          zoomRangeRef.current = {
            start: params.batch[0].start,
            end: params.batch[0].end,
          };
        }
      } else if (params.start !== undefined && params.end !== undefined) {
        zoomRangeRef.current = {
          start: params.start,
          end: params.end,
        };
      }
    });
  }, [formatData, dateList, candlestickData, isInteractive, isLoading, data, t]);

  return <div className="w-full h-full" ref={chartContainRef}></div>;
}
function useTranslations(arg0: string) {
  throw new Error('Function not implemented.');
}
