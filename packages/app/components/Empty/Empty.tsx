import { VStack } from "@quantum/components/ui/vstack";
import { Text } from "@quantum/components/ui/text";
import Image from "@unitools/image";    
import emptyImage from "./icon-empty.svg";
export default function Empty({ className, content }: { className?: string, content: string }) {
  return (
    <VStack className={`gap-4 items-center justify-center w-full h-full flex-1 ${className}`}>
      <Image source={emptyImage} alt="empty" height={110} width={178} />
      <Text className="text-[#0A0A0A] text-center  text-[14px] font-not-italic font-[500] leading-[20px]">{content}</Text>
    </VStack>
  );
}