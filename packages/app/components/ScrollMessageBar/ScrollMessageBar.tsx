'use client';
import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import { Popover, PopoverBody, PopoverContent } from '@quantum/components/ui/popover';
import { Text } from '@quantum/components/ui/text';
import Link from '@unitools/link';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RiArrowRightSLine } from 'react-icons/ri';
import { Platform, ScrollView, View } from 'react-native';
import type { TopBar, TopBarAssetItem } from '../../services/asset/asset.types';
import ChangeValue from '../ChangeValue';
import ItemMoreData from './ItemMoreData';
import { useRequest } from 'ahooks';
import { getTopBarAssets } from '../../services/asset/asset.api';
import { Pressable } from '@quantum/components/ui/pressable';
// 为 Web 平台定义样式
const webCursorStyle = Platform.select({
  web: { cursor: 'default' as const },
  default: {},
});

// 添加针对Web平台的CSS类
const scrollViewClassName = Platform.OS === 'web' ? 'cursor-pointer flex-1' : 'flex-1';

interface MessageItemProps {
  item: TopBarAssetItem;
  onHoverChange?: (isHovering: boolean) => void; // 新增回调函数，用于通知 ScrollView 暂停滚动
}

function MessageItem({ item, onHoverChange }: MessageItemProps) {
  const [isOpen, setIsOpen] = useState(false);
  const popoverTriggerRef = useRef<any>(null); // 使用 any 类型避免平台类型冲突
  const popoverContentRef = useRef<any>(null); // 使用 any 类型避免平台类型冲突
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const CLOSE_DELAY = 0; // 延时关闭时间，单位为毫秒

  // 生成唯一ID用于查找 Popover 内容（备用方案）
  const popoverContentId = useRef(
    `popover-content-${item.symbol}-${Math.random().toString(36).substring(2, 9)}`,
  ).current;

  // 存储最新的回调函数
  const onHoverChangeRef = useRef(onHoverChange);

  // 更新回调引用
  useEffect(() => {
    onHoverChangeRef.current = onHoverChange;
  }, [onHoverChange]);

  // 处理鼠标进入 Popover 触发区域
  const handleMouseEnter = (e: MouseEvent) => {
    if (Platform.OS === 'web') {
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
        closeTimeoutRef.current = null;
      }
      setIsOpen(true);
      // 通知 ScrollView 组件暂停滚动
      onHoverChangeRef.current?.(true);
    }
  };

  // 处理鼠标离开 Popover 触发区域
  const handleMouseLeave = (e: MouseEvent) => {
    if (Platform.OS === 'web') {
      // 设置延时关闭，以便用户有时间移动到 Popover 内容区域
      closeTimeoutRef.current = setTimeout(() => {
        setIsOpen(false);
        // 通知 ScrollView 组件恢复滚动
        onHoverChangeRef.current?.(false);
      }, CLOSE_DELAY);
    }
  };

  // 添加触发器鼠标事件监听器
  useEffect(() => {
    if (Platform.OS === 'web') {
      // 获取触发器和内容区域的DOM元素
      const triggerElement = popoverTriggerRef.current;

      // 为触发器添加事件监听器
      if (triggerElement) {
        triggerElement.addEventListener('mouseenter', handleMouseEnter);
        triggerElement.addEventListener('mouseleave', handleMouseLeave);
      }

      // 在组件卸载时移除事件监听器
      return () => {
        if (triggerElement) {
          triggerElement.removeEventListener('mouseenter', handleMouseEnter);
          triggerElement.removeEventListener('mouseleave', handleMouseLeave);
        }
        if (closeTimeoutRef.current) {
          clearTimeout(closeTimeoutRef.current);
        }
      };
    }
  }, []); // 空依赖数组，只在组件挂载和卸载时执行

  // 使用全局鼠标事件来监听 Popover 内容区域
  useEffect(() => {
    if (Platform.OS === 'web' && isOpen) {
      // 创建检查鼠标是否在 Popover 内容区域的函数
      const handleGlobalMouseMove = (e: MouseEvent) => {
        // 尝试通过多种方式获取内容元素
        let contentElement = popoverContentRef.current;

        // 如果 ref 为空，尝试通过 ID 查找
        if (!contentElement) {
          const popoverContents = document.querySelectorAll('.popover-content');
          if (popoverContents.length > 0) {
            // 遍历所有 popover 内容元素，找到最近添加到 DOM 的一个
            // 这通常是最后打开的 Popover
            contentElement = popoverContents[popoverContents.length - 1];
          }
        }

        if (contentElement) {
          // 检查鼠标是否在内容区域内
          const rect = contentElement.getBoundingClientRect();
          const isInside =
            e.clientX >= rect.left && e.clientX <= rect.right && e.clientY >= rect.top && e.clientY <= rect.bottom;

          if (isInside) {
            // 鼠标在内容区域内，清除关闭定时器
            if (closeTimeoutRef.current) {
              clearTimeout(closeTimeoutRef.current);
              closeTimeoutRef.current = null;
            }
            onHoverChangeRef.current?.(true);
          } else if (!closeTimeoutRef.current && !isMouseOverElement(e, popoverTriggerRef.current)) {
            // 鼠标不在内容区域内且不在触发器上，设置关闭定时器
            closeTimeoutRef.current = setTimeout(() => {
              setIsOpen(false);
              onHoverChangeRef.current?.(false);
            }, CLOSE_DELAY);
          }
        }
      };

      // 检查鼠标是否在特定元素上
      const isMouseOverElement = (e: MouseEvent, element: HTMLElement) => {
        if (!element) return false;
        const rect = element.getBoundingClientRect();
        return e.clientX >= rect.left && e.clientX <= rect.right && e.clientY >= rect.top && e.clientY <= rect.bottom;
      };

      // 添加全局鼠标移动事件监听器
      document.addEventListener('mousemove', handleGlobalMouseMove);

      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove);
      };
    }
  }, [isOpen]);

  return (
    <Popover
      isOpen={isOpen}
      placement="bottom left"
      offset={8}
      focusScope={false}
      initialFocusRef={false as any}
      trigger={(triggerProps) => {
        return (
          <Pressable {...triggerProps}>
            <Box ref={popoverTriggerRef}>
              <Link href={`/detail/${item.market}/${item.symbol}`}>
                <HStack className="gap-[6px] items-center">
                  <Text className="text-[#0A0A0A] text-[12px] weight-600 leading-[16px] whitespace-nowrap">
                    {item.name}
                  </Text>
                  <ChangeValue change={`${item.change_rate}%`} />
                </HStack>
              </Link>
            </Box>
          </Pressable>
        );
      }}
    >
      <PopoverContent
        className="popover-content w-[186px] p-[0] flex-col items-start gap-[8px] rounded-[6px] bg-[#FFF] shadow-[0px_2px_4px_0px_rgba(0,0,0,0.05),0px_4px_16px_0px_rgba(0,0,0,0.15)] outline-none">
        <PopoverBody className="w-full">
          <Box className="w-full" id={popoverContentId} ref={popoverContentRef}>
            <ItemMoreData item={item} showGraph={isOpen} />
          </Box>
        </PopoverBody>
      </PopoverContent>
    </Popover>
  );
}

export default function ScrollMessageBar({ data }: { data: TopBar }) {
  const { t, i18n, ready } = useTranslation();
  const scrollViewRef = useRef<ScrollView>(null);
  const [isPaused, setIsPaused] = useState(false);
  const scrollInterval = useRef<NodeJS.Timeout>();
  const [contentWidth, setContentWidth] = useState(0);
  // 使用ref存储滚动位置，避免状态更新导致的重渲染
  const scrollPositionRef = useRef(0);
  const scrollSpeed = 1; // 控制滚动速度
  const [isScrollViewRendered, setIsScrollViewRendered] = useState(false);
  // 添加useRef来记忆上一次的宽度值，避免不必要的状态更新
  const lastWidthRef = useRef<number>(0);

  const handlePause = () => {
    setIsPaused(true);
  };

  const handleResume = () => {
    setIsPaused(false);
  };

  // MessageItem hover 状态变化的回调函数
  const handleItemHoverChange = (isHovering: boolean) => {
    if (isHovering) {
      handlePause();
    } else {
      handleResume();
    }
  };

  // 处理ScrollView布局完成事件
  const handleScrollViewLayout = () => {
    setIsScrollViewRendered(true);
  };

  // 添加鼠标事件监听器（仅在Web平台上）
  useEffect(() => {
    if (Platform.OS === 'web' && scrollViewRef.current && isScrollViewRendered) {
      try {
        // 使用非空断言获取DOM节点
        const scrollViewNode = scrollViewRef.current as unknown as HTMLElement;

        const handleMouseEnter = () => {
          handlePause();
        };

        const handleMouseLeave = () => {
          handleResume();
        };

        scrollViewNode.addEventListener('mouseenter', handleMouseEnter);
        scrollViewNode.addEventListener('mouseleave', handleMouseLeave);

        return () => {
          scrollViewNode.removeEventListener('mouseenter', handleMouseEnter);
          scrollViewNode.removeEventListener('mouseleave', handleMouseLeave);
        };
      } catch (error) {
        console.error('Error attaching mouse events to ScrollView:', error);
      }
    }
    // 依赖于scrollViewRef.current和isScrollViewRendered
  }, [scrollViewRef.current, isScrollViewRendered]);

  const handleContentSizeChange = (width: number) => {
    // 只有当新的宽度与上一次记录的宽度不同时才更新状态
    // 这可以防止相同宽度值的重复设置引起的无用渲染
    if (width !== lastWidthRef.current && width > 0) {
      // 计算差异百分比，只有当宽度变化超过5%才更新
      const diffPercentage = Math.abs((width - lastWidthRef.current) / lastWidthRef.current) * 100;
      if (lastWidthRef.current === 0 || diffPercentage > 5) {
        lastWidthRef.current = width;
        setContentWidth(width);
      }
    }
  };

  // 优化useEffect中的依赖项，将contentWidth也改为ref引用
  const contentWidthRef = useRef(0);

  useEffect(() => {
    // 当contentWidth变化时更新ref
    contentWidthRef.current = contentWidth;
  }, [contentWidth]);

  useEffect(() => {
    const startAutoScroll = () => {
      if (scrollInterval.current) {
        clearInterval(scrollInterval.current);
      }

      scrollInterval.current = setInterval(() => {
        if (scrollViewRef.current && contentWidthRef.current > 0) {
          const newPosition = scrollPositionRef.current + scrollSpeed;

          // 当滚动到第一份列表的末尾时，立即重置到开始位置
          if (newPosition >= contentWidthRef.current / 3) {
            scrollViewRef.current.scrollTo({
              x: 0,
              y: 0,
              animated: false, // 使用无动画重置，避免视觉上的跳跃
            });
            scrollPositionRef.current = 0;
          } else {
            scrollViewRef.current.scrollTo({
              x: newPosition,
              y: 0,
              animated: false, // 改为false避免动画可能带来的性能问题
            });
            scrollPositionRef.current = newPosition;
          }
        }
      }, 30);
    };

    if (!isPaused) {
      startAutoScroll();
    }

    return () => {
      if (scrollInterval.current) {
        clearInterval(scrollInterval.current);
      }
    };
  }, [isPaused]); // 只保留isPaused依赖

  const { data: apiData } = useRequest(
    async () => {
      const res = await getTopBarAssets();
      return res;
    },
    {
      ready: !!ready,
      refreshDeps: [i18n.language],
    },
  );

  const showData = useMemo(() => {
    return apiData || data;
  }, [apiData, data]);

  // 使用memo缓存计算结果，避免不必要的重新计算
  const duplicatedList = useMemo(() => {
    const baseList = showData?.floating || [];
    return [...baseList, ...baseList, ...baseList];
  }, [showData?.floating]);

  if (duplicatedList.length <= 0) {
    return null;
  }

  return (
    <View>
      <HStack className="w-full h-[30px] flex px-6 justify-between items-center self-stretch border-b-[1px] border-b-solid border-b-[#F5F5F5]">
        {(showData?.fixed || []).length > 0 && (
          <HStack className="h-full items-center px-3 gap-4">
            {showData?.fixed?.map((item, index) => {
              return <MessageItem key={index} item={item} />;
            })}
          </HStack>
        )}

        <ScrollView
          ref={scrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          className={`h-full items-center ${scrollViewClassName}`}
          onContentSizeChange={handleContentSizeChange}
          scrollEventThrottle={16}
          onLayout={handleScrollViewLayout}
          onScroll={() => {
            scrollViewRef.current?.scrollTo({
              x: scrollPositionRef.current,
              y: 0,
              animated: false, // 使用无动画重置，避免视觉上的跳跃
            });
            
          }}
        >
          <HStack className="gap-4">
            {duplicatedList.map((item, index) => {
              return <MessageItem key={index} item={item} onHoverChange={handleItemHoverChange} />;
            })}
          </HStack>
        </ScrollView>

        <Link href="/">
          <HStack className="px-3 gap-1 bg-[#FFFFFF] items-center">
            <Text className="text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[600] leading-[20px]">
              {t('more')}
            </Text>
            <RiArrowRightSLine size={16} className="text-[#808080]" />
          </HStack>
        </Link>
      </HStack>
    </View>
  );
}
