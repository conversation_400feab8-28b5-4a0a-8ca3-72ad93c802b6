import ChangeValue from '../ChangeValue';
import { averageNumber } from '../../uitls/number';
import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import TextOrImageLogo from '../TextOrImageLogo';
import { ButtonStar } from '../ButtonStar/ButtonStar';
import type { TopBarAssetItem } from '../../services/asset/asset.types';
import { useRequest } from 'ahooks';
import { getKline } from '../../services/kline/kline.api';
import { AliKlineInterval } from '../../services/kline/kline.types';
import { Skeleton } from '@quantum/components/ui/skeleton';
import LineGraph from '../LineGraph/LineGraph';
import { useToggleStar } from '../../hooks/useToggleStar';
import { sleep } from '../../uitls';
import { watchlistStore } from '../../store/watchlist.store';
import { observer } from 'mobx-react-lite';
function ItemLineGraph({ item }: { item: TopBarAssetItem }) {
  const { data = [], loading } = useRequest(
    async () => {
      const res = await getKline(item.market, item.symbol, AliKlineInterval.DAY_1, 7);
      return res;
    },
    {
      cacheKey: `kline-${item.market}-${item.symbol}-day-7`,
      staleTime: 600 * 1000, // 10分钟缓存
    },
  );

  return (
    <Box className="w-full h-full">
      {loading ? <Skeleton className="w-full h-full" /> : <LineGraph data={data} isInteractive={false} />}
    </Box>
  );
}
function ItemMoreData({ item, showGraph }: { item: any; showGraph: boolean }) {
  const { runAsync: callOnToggleStar } = useToggleStar(item.market, item.symbol);
  const defaultStar = watchlistStore.watchlist.data?.some((ele) => {
    return ele.market === item.market && ele.symbol === item.symbol;
  });
  const handleOnToggleStar = async (value: boolean) => {
    await callOnToggleStar(value);
    await sleep(1000);
    await watchlistStore.watchlist.reload();
  };

  return (
    <Box className="w-full">
      <VStack className="gap-[8px] w-full p-[12px]">
        <HStack className="gap-[6px]">
          <TextOrImageLogo text={String(item.symbol)} size={20} />
          <Text className="text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[500] leading-[20px]">
            {item.symbol}
          </Text>
        </HStack>

        <HStack>
          <VStack className="flex-1 gap-1">
            <Text className="text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[700] leading-[20px]">
              {(item as any).price != null ? averageNumber((item as any).price) : '--'}
            </Text>

            <ChangeValue
              change={`${item.change_rate}%`}
              className="font-Inter text-[14px] font-not-italic font-[600] leading-[1]"
            />
          </VStack>
          <Box className="w-[56px] h-[42px]">{showGraph && <ItemLineGraph item={item} />}</Box>
        </HStack>
        <Box className="h-[28px] w-full">
          <ButtonStar isButton={true} onToggleStar={handleOnToggleStar} defaultStar={defaultStar} />
        </Box>
      </VStack>
    </Box>
  );
}

export default observer(ItemMoreData);
