import { getTopBarAssets } from '../../services/asset/asset.api';
import ScrollMessageBar from './ScrollMessageBar';

async function fetchPageData() {
  try {
    const res = await getTopBarAssets();
    return res
  } catch (error) {
    return null
  }
}

export default async  function ScrollMessageBarIndex() {
  const data = await fetchPageData();
  return <ScrollMessageBar data={data || {
    fixed: [],
    floating: []
  }} />;
}
