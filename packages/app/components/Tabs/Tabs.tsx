'use client';
import React, { useEffect, useState } from 'react';
import { VStack } from '@quantum/components/ui/vstack';
import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import { Text } from '@quantum/components/ui/text';
import { Pressable } from '@quantum/components/ui/pressable';

export interface TabProps {
  label: string;
  children: React.ReactNode;
}

interface TabsProps {
  children: React.ReactElement<TabProps>[];
  defaultIndex?: number;
  labelClassName?: string;
  labelActiveClassName?: string;
  height?: number;
  operate?: React.ReactNode | ((index: number) => React.ReactNode);
  selectedIndex?: number;
  onSelectedIndexChange?: (index: number) => void;
}

const Tab: React.FC<TabProps> = () => null;

export const Tabs: React.FC<TabsProps> & { Tab: typeof Tab } = ({
  children,
  defaultIndex = 0,
  selectedIndex,
  onSelectedIndexChange,
  labelClassName,
  labelActiveClassName,
  height,
  operate,
}) => {
  const [activeIndex, setActiveIndex] = useState(defaultIndex);

  const tabs = React.Children.toArray(children) as React.ReactElement<TabProps>[];

  useEffect(() => {
    if (selectedIndex !== undefined) {
      setActiveIndex(selectedIndex);
    }
  }, [selectedIndex]);

  return (
    <VStack className="flex-1 w-full">
      <HStack
        className="w-full h-[42px] items-center justify-between border-b-[1px] border-b-solid border-b-[#E6E6E6]"
        style={{ height }}
      >
        <HStack className="flex-1 gap-6 ">
          {tabs.map((tab, index) => (
            <Pressable key={index} onPress={() => {
              setActiveIndex(index);
              onSelectedIndexChange?.(index);
            }} className="relative">
              <Text
                className={`text-[14px] font-not-italic  leading-[42px] ${
                  activeIndex === index
                    ? labelActiveClassName || 'text-[#0a0a0a] font-[500]'
                    : labelClassName || 'text-[#808080] font-[400]'
                }`}
              >
                {tab.props.label}
              </Text>
              <Box
                className={`absolute bottom-0 left-[50%] translate-x-[-50%] w-0 h-[2px] bg-[#0A0A0A] transition-all duration-300`}
                style={{ width: activeIndex === index ? '100%' : '0%' }}
              ></Box>
            </Pressable>
          ))}
        </HStack>
        {typeof operate === 'function' ? operate(activeIndex) : operate}
      </HStack>
      <Box className="pt-[16px] flex-1" key={activeIndex}>
        {tabs[activeIndex]?.props.children}
      </Box>
    </VStack>
  );
};

Tabs.Tab = Tab;
