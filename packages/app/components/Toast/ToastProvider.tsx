import { Button, ButtonText } from '@quantum/components/ui/button';
import { Toast, ToastDescription, ToastTitle, useToast } from '@quantum/components/ui/toast';
import type { ReactNode } from 'react';
import { createContext, useContext } from 'react';

// 创建一个Context，存储toast实例
interface ToastContextType {
  toast: ReturnType<typeof useToast>;
}

// 创建一个上下文，初始值为null
const ToastContext = createContext<ToastContextType | null>(null);

// 创建Provider组件
export function ToastProvider({ children }: { children: ReactNode }) {
  const toast = useToast();
  
  return (
    <ToastContext.Provider value={{ toast }}>
      {children}
    </ToastContext.Provider>
  );
}

// 创建一个自定义hook，用于在组件中访问toast
export function useCustomToast() {
  const context = useContext(ToastContext);
  
  // 判断是否在Provider内使用此hook
  if (!context) {
    throw new Error('useCustomToast必须在ToastProvider内部使用');
  }
  
  const { toast } = context;
  
  // 创建showToast函数，使用户可以更方便地展示toast
  const showToast = ({
    title = '提示',
    description = '',
    status = 'info',
    duration = 3000,
    placement = 'top',
  }: {
    title?: string;
    description?: string;
    status?: 'error' | 'warning' | 'success' | 'info' | 'muted';
    duration?: number;
    placement?: 'top' | 'bottom';
  }) => {
    const id = String(Math.random());
    
    toast.show({
      id,
      placement,
      duration,
      render: ({ id }) => {
        const uniqueToastId = 'toast-' + id;
        return (
          <Toast nativeID={uniqueToastId} action={status} variant="outline">
            <ToastTitle>{title}</ToastTitle>
            {description && <ToastDescription>{description}</ToastDescription>}
          </Toast>
        );
      },
    });
    
    return id;
  };
  
  return {
    showToast,
    closeToast: toast.close,
    closeAll: toast.closeAll,
    isActive: toast.isActive,
  };
}

// 示例组件
export default function ToastExample() {
  const { showToast } = useCustomToast();
  
  const handleShowToast = () => {
    showToast({
      title: '你好!',
      description: '这是一个自定义的toast消息',
      status: 'success',
    });
  };

  return (
    <Button onPress={handleShowToast}>
      <ButtonText>点击我</ButtonText>
    </Button>
  );
}
