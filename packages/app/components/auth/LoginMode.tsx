import { Box } from '@quantum/components/ui/box';
import { But<PERSON> } from '@quantum/components/ui/button';
import { Center } from '@quantum/components/ui/center';
import { Image } from '@quantum/components/ui/image';
import { Text } from '@quantum/components/ui/text';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import ButtonGoogleAuth from './ButtonGoogleAuth/ButtonGoogleAuth.web';

export const LoginMode = ({ onChangeEmailLogin, onFinish }: { onChangeEmailLogin: () => void, onFinish : () => void }) => {
  const { t } = useTranslation();
  return (
    <Box>
      <Center>
        <Text className="text-[#0A0A0A] font-Inter text-[24px] font-not-italic font-[600] leading-[24px] tracking--0.36px">
          {
            t('modal_login.title')
          }
        </Text>
      </Center>
      <Center className='mt-7 mb-6'>
        
        <ButtonGoogleAuth onFinish={onFinish} />
      </Center>
      <Center>
        <Text className="max-w-[296] w-[296px] h-[40px] text-[#0A0A0A] text-center font-Inter text-[14px] font-not-italic font-[400] leading-[20px] underline decoration-solid decoration-none decoration-auto underline-offset-auto text-from-font">
          {
            t('modal_login.agree_terms')
          }
        </Text>
      </Center>
      <Center className="mt-6">
        <Button variant="link" className="h-auto" onPress={() => onChangeEmailLogin()}>
          <Text className="text-[#0A0A0A] font-Poppins text-[16px] font-not-italic font-[600] leading-[20px]">
            {
              t('modal_login.email_login')
            }
          </Text>
        </Button>
      </Center>
    </Box>
  );
};
