import { zodResolver } from '@hookform/resolvers/zod';
import { Box } from '@quantum/components/ui/box';
import { Button } from '@quantum/components/ui/button';
import { Center } from '@quantum/components/ui/center';
import { FormControl, FormControlError, FormControlErrorText } from '@quantum/components/ui/form-control';
import { Input, InputField, InputSlot } from '@quantum/components/ui/input';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { useRequest } from 'ahooks';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Keyboard } from 'react-native';
import { z } from 'zod';
import type { ApiResponse } from '../../services/api';
import { confirmEmailAuth, sendEmailAuth } from '../../services/auth/auth.api';
import { authStore } from '../../store/auth.store';

export const LoginByEmailCode = ({
  onFinish,
  onError,
}: {
  onFinish: () => void;
  onError: (error: ApiResponse) => void;
}) => {
  const [countdown, setCountdown] = useState(0);
  const [isDisabled, setIsDisabled] = useState(false);
  const { t } = useTranslation();
  const signInSchema = z.object({
    email: z.string().min(1, t('verification.email_required')).email(),
    code: z
      .string()
      .min(1, t('verification.verification_code_required'))
      .length(6, t('verification.verification_code_length', { length: 6 })),
  });

  type SignInSchemaType = z.infer<typeof signInSchema>;
  const {
    control,
    formState: { errors },
    handleSubmit,
    reset,
    trigger,
    getValues,
    clearErrors,
  } = useForm<SignInSchemaType>({
    resolver: zodResolver(signInSchema),
  });

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    } else {
      setIsDisabled(false);
    }
    return () => clearInterval(timer);
  }, [countdown]);

  const handleSendCode = async () => {
    const isEmailValid = await trigger('email');

    if (!isEmailValid) {
      return;
    }
    const email = getValues('email');
    try {
      await sendEmailAuth({ inviter: authStore.inviteCode || '', email });
      setCountdown(30);
      setIsDisabled(true);
    } catch (error) {
      console.error('Failed to send verification code:', error);
    }
  };
  const { runAsync: onSubmit, loading } = useRequest(
    async (_data: SignInSchemaType) => {
      try {
        const res = await confirmEmailAuth({
          ..._data,
          inviter: authStore.inviteCode || '',
        });

        const userId = res.email;
        authStore.setToken(userId, res.access_token);
        authStore.setInviteCode(null);
        if (onFinish) {
          onFinish();
        }
        reset();
      } catch (error: any) {
        if (onError) {
          onError(error);
        }
      }
    },
    {
      manual: true,
    },
  );

  const handleKeyPress = () => {
    Keyboard.dismiss();
    handleSubmit(onSubmit)();
  };

  return (
    <Box>
      <Center>
        <Text className="text-[#0A0A0A] font-Inter text-[24px] font-not-italic font-[600] leading-[24px] tracking--0.36px">
          {t('modal_login.title')}
        </Text>
      </Center>
      <VStack className="mt-7 gap-3 w-full max-w-[376px] mx-auto">
        <FormControl isInvalid={!!errors.email} isRequired={true}>
          <Controller
            name="email"
            defaultValue=""
            control={control}
            render={({ field: { onChange, onBlur, value } }) => (
              <Input variant="rounded" className="rounded-[6px] bg-[#F0F0F0] border-none h-12">
                <InputField
                  className="text-[#0A0A0A] font-Poppins text-[14px] font-not-italic font-[500] leading-[20px] placeholder:text-[#808080]"
                  placeholder={t('modal_login.email_placeholder')}
                  type="text"
                  value={value}
                  onChangeText={(text) => {
                    onChange(text);
                    // 当用户输入时清除email字段的错误
                    if (errors.email) {
                      clearErrors('email');
                    }
                  }}
                  onBlur={onBlur}
                  onSubmitEditing={handleKeyPress}
                  returnKeyType="done"
                />
              </Input>
            )}
          />
          <FormControlError>
            <FormControlErrorText>{errors?.email?.message}</FormControlErrorText>
          </FormControlError>
        </FormControl>

        <FormControl isInvalid={!!errors.code && !!errors.code} isRequired={true}>
          <Controller
            name="code"
            defaultValue=""
            control={control}
            render={({ field: { onChange, onBlur, value } }) => (
              <Input
                variant="rounded"
                className="rounded-[6px] bg-[#F0F0F0] border-none h-12"
                isDisabled={false}
                isInvalid={false}
                isReadOnly={false}
              >
                <InputField
                  className="text-[#0A0A0A] font-Poppins text-[14px] font-not-italic font-[500] leading-[20px] placeholder:text-[#808080]"
                  placeholder={t('modal_login.verification_code')}
                  type="text"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  onSubmitEditing={handleKeyPress}
                  returnKeyType="done"
                  maxLength={6}
                />
                <InputSlot className="pr-3">
                  <Button
                    onPress={handleSendCode}
                    variant="link"
                    isDisabled={isDisabled}
                    className="h-auto text-[#0A0A0A] font-Poppins text-[14px] font-not-italic font-[400] leading-[20px]"
                  >
                    <Text>{countdown > 0 ? `${countdown}s` : t('modal_login.send_code')}</Text>
                  </Button>
                </InputSlot>
              </Input>
            )}
          />
          <FormControlError>
            <FormControlErrorText>{errors?.code?.message}</FormControlErrorText>
          </FormControlError>
        </FormControl>

        <Button
          className="h-[48px] rounded-[8px] bg-gradient-to-r from-[#7CFDFF] to-[#6AFF50]"
          onPress={handleSubmit(onSubmit)}
          loading={loading}
        >
          <Text>{t('modal_login.sign_in')}</Text>
        </Button>
      </VStack>
    </Box>
  );
};
