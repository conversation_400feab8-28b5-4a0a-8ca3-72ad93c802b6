import { Box } from '@quantum/components/ui/box';
import {
  Modal,
  ModalBackdrop,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
} from '@quantum/components/ui/modal';
import { Pressable } from '@quantum/components/ui/pressable';
import { useCallback, useState } from 'react';
import { RiArrowLeftSLine, RiCloseFill } from 'react-icons/ri';
import type { ApiResponse } from '../../services/api';
import { useCustomToast } from '../Toast/ToastProvider';
import { LoginByEmailCode } from './LoginByEmailCode';
import { LoginMode } from './LoginMode';
export default function ModalLogin({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const [showEmailLogin, setShowEmailLogin] = useState(false);

  const onFinish = useCallback(function() {
    setShowEmailLogin(false)
    if (onClose) {
      onClose();
    }
  }, [
    onClose
  ])

  const { showToast } = useCustomToast();
  const onError = useCallback(function(error: ApiResponse) {
    console.log(error)
    showToast({
      title: 'Error',
      description: error?.msg,
      status: 'error',
    });
  }, [])
  return (
    <>
      <Modal isOpen={isOpen} onClose={onClose} className='fixed inset-0'>
        <ModalBackdrop className="bg-[#000000] opacity-70" />
        <ModalContent size="full" className="p-5 w-[600px] min-h-[320px]  rounded-[16px] bg-[#FFF] shadow-[0px_4px_25px_0px_rgba(0,0,0,0.10),0px_4px_10px_0px_rgba(0,0,0,0.04)]">
          <ModalHeader>
            <Box>
              {showEmailLogin && (
                <Pressable
                  className="w-[24px] h-[24px] items-center justify-center border rounded-[6px] border-solid border-[#cccccc]"
                  onPress={() => setShowEmailLogin(false)}
                >
                  <RiArrowLeftSLine size={16} className="text-[#0a0a0a]" />
                </Pressable>
              )}
            </Box>
            <ModalCloseButton className="w-[24px] h-[24px] items-center justify-center border rounded-[6px] border-solid border-[#cccccc]">
              <RiCloseFill size={16} className="text-[#0a0a0a]" />
            </ModalCloseButton>
          </ModalHeader>
          <ModalBody className="mt-3">
            {showEmailLogin ? <LoginByEmailCode onFinish={onFinish} onError={onError}/> : <LoginMode onChangeEmailLogin={() => setShowEmailLogin(true)} onFinish={onFinish}/>}
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
}
