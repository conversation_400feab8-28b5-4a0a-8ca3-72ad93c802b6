import { GoogleOAuth<PERSON><PERSON><PERSON>, GoogleLogin, useGoogleLogin } from '@react-oauth/google';
import { Spinner } from '@quantum/components/ui/spinner';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { But<PERSON> } from '@quantum/components/ui/button';
import { Image } from '@quantum/components/ui/image';
import { Box } from '@quantum/components/ui/box';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import { useRequest } from 'ahooks';
import { googleOAuth, googleOAuthToken } from '@quantum/app/services/oauth/oauth.api';
import { authStore } from '../../../store/auth.store';
import { observer } from 'mobx-react-lite';

const ButtonGoogle = ({ onFinish }: { onFinish?: () => void }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useTranslation();
  const { runAsync, loading } = useRequest(
    async (credential) => {
      const res = await googleOAuthToken({
        inviter: authStore.inviteCode || '',
        auth_token: credential,
      });
      const userId = res.email;
      authStore.setToken(userId, res.access_token);
      authStore.setInviteCode(null);
      return res;
    },
    {
      manual: true,
    },
  );
  const handleLoginSuccess = async (response: any) => {
    console.log('handleLoginSuccess', response);
    await runAsync(response.access_token)
    setIsLoading(false);
    if (onFinish) {
      onFinish();
    }
  };
  const handleLoginFailure = (errorResponse: any) => {
    console.log('login failure', errorResponse);
    setIsLoading(false);
  };
  const login = useGoogleLogin({
    // flow: 'auth-code',
    onSuccess: handleLoginSuccess,
    onError: handleLoginFailure,
    onNonOAuthError(nonOAuthError) {
      console.log('non oauth error', nonOAuthError);
      setIsLoading(false);
    }
  });

  return (
    <Button
      className="flex gap-2 items-center justify-center gap-[8px] w-full max-w-[376px] h-[48px] rounded-[48px] bg-gradient-to-r from-[#7CFDFF] to-[#6AFF50]"
      onPress={() => {
        login()
      }}
      loading={loading || isLoading}
    >
      <Image
        source={require('@quantum/shared/assets/icons/Google-Icon.svg')}
        alt="google logo"
        width={20}
        height={20}
        className="w-[20px] h-[20px]"
      />
      {t('modal_login.sign_up_or_login_with_google')}
    </Button>
  );
};
const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '';

function ButtonGoogleAuth({ onFinish }: { onFinish: () => void }) {
  const { runAsync } = useRequest(
    async (credential) => {
      const res = await googleOAuth({
        inviter: authStore.inviteCode || '',
        google_credential: credential,
      });
      const userId = res.email;
      authStore.setToken(userId, res.access_token);
      authStore.setInviteCode(null);
      return res;
    },
    {
      manual: true,
    },
  );
  const handleLoginSuccess = async (response: any) => {
    console.log('handleLoginSuccess', response);
    const { credential } = response;
    const res = await runAsync(credential);

    console.log('data', res);
    if (onFinish) {
      onFinish();
    }
  };
  const handleLoginFailure = () => {
    console.log('login failure');
  };
  const { i18n } = useTranslation();
  return (
    <GoogleOAuthProvider clientId={clientId}>
      <VStack className="gap-3">
        {/*<Text>龙哥说：暂不支持自定义</Text>*/}
        <ButtonGoogle onFinish={onFinish} />
        {/*<Text>先使用这个：</Text>*/}

        {/*<GoogleLogin*/}
        {/*  onSuccess={handleLoginSuccess}*/}
        {/*  onError={handleLoginFailure}*/}
        {/*  theme="outline"*/}
        {/*  shape="pill"*/}
        {/*  text="signin_with"*/}
        {/*  locale={i18n.language}*/}
        {/*  size="large"*/}
        {/*  width="100%"*/}
        {/*  // useOneTap*/}
        {/*/>*/}
      </VStack>
    </GoogleOAuthProvider>
  );
}

export default observer(ButtonGoogleAuth);
