import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import type { Market } from '../services/watchlist/watchlist.types';
import { HStack } from '@quantum/components/ui/hstack';
import Image from '@unitools/image';
import { useMemo } from 'react';
import IconMarketCrypto from '@quantum/shared/assets/waitlist/icon-market-crypto.svg';
import IconMarketHK from '@quantum/shared/assets/waitlist/icon-market-hk.svg';
import IconMarketUS from '@quantum/shared/assets/waitlist/icon-market-us.svg';
import { Tooltip, TooltipContent, TooltipText } from '@quantum/components/ui/tooltip';
import { useTranslation } from 'react-i18next';
export const AssetsTypeIcon = ({ type, size = 36 }: { type: Market; size?: number }) => {
  const { t } = useTranslation();
  const icon = useMemo(() => {
    switch (type) {
      case 'US':
        return IconMarketUS;
      case 'HK':
        return IconMarketHK;
      case 'CRYPTO':
        return IconMarketCrypto;
    }
  }, [type]);

  const marketMap = useMemo(() => {
    return {
      US: t('marketname.us'),
      HK: t('marketname.hk'),
      CRYPTO: t('marketname.cypto'),
    };
  }, [t]);

  return (
    <Tooltip
      placement="top"
      trigger={(triggerProps) => {
        return (
          <HStack
            className={`w-[${size}px] h-[${size}px] rounded-full bg-[rgba(5,198,151,0.10)] items-center justify-center`}
            {...triggerProps}
          >
            <Image source={icon} alt={type} height={size} width={size} />
          </HStack>
        );
      }}
    >
      <TooltipContent>
        <TooltipText>{marketMap[type]}</TooltipText>
      </TooltipContent>
    </Tooltip>
  );
};
