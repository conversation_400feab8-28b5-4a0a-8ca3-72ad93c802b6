import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import { Menu, MenuItem } from '@quantum/components/ui/menu';
import { Button } from '@quantum/components/ui/button';
import { Text } from '@quantum/components/ui/text';
import { RiArrowDownSLine, RiCheckLine } from 'react-icons/ri';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getGrayscalePositionsSymbols } from '../../services/crypto_stat/crypto_stat.api';
import { useRequest } from 'ahooks';


export const defaultSymbolList = [
  {
    value: 'BTC',
    i18nKey: 'crypto_stat.select_crypto_coin.btc',
  },
  {
    value: 'ETH',
    i18nKey: 'crypto_stat.select_crypto_coin.eth',
  },
  {
    value: 'ETC',
    i18nKey: 'crypto_stat.select_crypto_coin.etc',
  },
  {
    value: 'LTC',
    i18nKey: 'crypto_stat.select_crypto_coin.ltc',
  },
  {
    value: 'BCH',
    i18nKey: 'crypto_stat.select_crypto_coin.bch',
  },
  {
    value: 'SOL',
    i18nKey: 'crypto_stat.select_crypto_coin.sol',
  },
  {
    value: 'XLM',
    i18nKey: 'crypto_stat.select_crypto_coin.xlm',
  },
  {
    value: 'LINK',
    i18nKey: 'crypto_stat.select_crypto_coin.link',
  },
  {
    value: 'ZEC',
    i18nKey: 'crypto_stat.select_crypto_coin.zec',
  },
  {
    value: 'MANA',
    i18nKey: 'crypto_stat.select_crypto_coin.mana',
  },
  {
    value: 'ZEN',
    i18nKey: 'crypto_stat.select_crypto_coin.zen',
  },
  {
    value: 'FIL',
    i18nKey: 'crypto_stat.select_crypto_coin.fil',
  },
  {
    value: 'BAT',
    i18nKey: 'crypto_stat.select_crypto_coin.bat',
  },
  {
    value: 'LPT',
    i18nKey: 'crypto_stat.select_crypto_coin.lpt',
  },
];

export default function SelectCryptoCoin({ value, onChange, useRemote = false }: { value: string; onChange: (value: string) => void, useRemote?: boolean }) {
  
  const {data: symbolListData = defaultSymbolList} = useRequest(async () => {
    const res = await getGrayscalePositionsSymbols();
    return res.symbols.map((item: string) => ({
      value: item,
      i18nKey: item,
    }));
  }, {
    ready: useRemote,
  });
  const [selected, setSelected] = useState(value);
  useEffect(() => {
    setSelected(value);
  }, [value]);

  const { t } = useTranslation();
  const symbolList = useMemo(() => {
    type SymbolItem = { value: string; i18nKey: string };

    const prioritySymbols: string[] = [
      'BTC', 'XRP', 'ETH', 'SOL', 'HYPE', 'SUI', 'ADA', 'DOGE', 'LINK', 'AVAX',
      'AAVE', 'UNI', 'DOT', 'TRUMP', 'ENA', 'ONDO', 'WIF', 'WLD', 'LTC', 'PEPE'
    ];

    // 1. 将prioritySymbols转换为所需格式
    const priorityItems: SymbolItem[] = prioritySymbols.map(symbol => ({
      value: symbol,
      i18nKey: symbol,
    }));
    
    // 2. 创建一个Set来存储所有优先币种，方便快速查找
    const prioritySet = new Set(prioritySymbols);
    
    // 3. 过滤symbolListData，只保留不在prioritySymbols中的元素
    const remainingItems = (symbolListData as SymbolItem[]).filter(
      item => !prioritySet.has(item.value)
    );
    
    // 4. 合并两个列表
    return [...priorityItems, ...remainingItems];
  }, [symbolListData]);
  return (
    <Menu
      placement="bottom left"
      offset={5}
      trigger={({ ...triggerProps }) => {
        return (
          <Button variant="link" {...triggerProps} 
          className="h-4 min-w-[120px] border-[1px] border-solid border-[#CCCCCC] bg-[#FFF] rounded-[6px] py-4 px-[10px]" >
            <Text className="text-[#0A0A0A] font-Inter text-[12px] font-not-italic font-[500] leading-[16px] flex-1">
              {t(symbolList.find((item: any) => item.value === selected)?.i18nKey || '')}
            </Text>
            <RiArrowDownSLine size={16} className="text-[#808080]" />
          </Button>
        );
      }}
      className="p-[10px] rounded-[6px] border-[1px] border-solid border-[#E6E6E6] bg-[#FFF] shadow-[0px_8px_20px_0px_rgba(0,0,0,0.10)] max-h-[400px] overflow-y-auto"
      selectionMode="single"
    >
      {symbolList.map((item: any) => (
        <MenuItem
          onPress={() => {
            setSelected(item.value);
            onChange(item.value);
          }}
          key={item.value}
          textValue={item.value}
          className="px-[8px] py-[10px] items-center gap-[16px] self-stretch rounded-[6px] bg-[#FFF] data-[hover=true]:bg-[rgba(0,173,107,0.05)]"
        >
          <HStack className="items-center justify-between w-full">
            <Text className={`text-[#4D4D4D] font-Poppins text-[14px] font-not-italic font-[400] leading-[20px] ${selected == item.value ? 'font-[500]' : ''}`}>
              {t(item.i18nKey || '')}
            </Text>
            {selected == item.value && <RiCheckLine size={16} className="text-[#40C382]" />}
          </HStack>
        </MenuItem>
      ))}
    </Menu>
  );
}
