'use client';
import { VStack } from '@quantum/components/ui/vstack';
import { useRequest } from 'ahooks';
import { getMicroStrategyCost } from '../../../services/crypto_stat/crypto_stat.api';
import type { MicroStrategyCost } from '../../../services/crypto_stat/crypto_stat.types';
import MicroStrategyCostGraph from './MicroStrategyCostGraph';
interface MicroStrategyCostProps {
  defaultData?: MicroStrategyCost[] | null;
}

export default function MicroStrategyCost({ defaultData }: { defaultData: MicroStrategyCost[] | null }) {
  const { data, loading } = useRequest(
    async () => {
      const res = await getMicroStrategyCost();
      return res;
    },
    {
      ready: !defaultData,
    },
  );

  return (
    <VStack className="h-[454px] gap-4 p-4 bg-gradient-to-b from-[rgba(5,198,151,0.012)] to-[rgba(5,198,151,0.03)] rounded-[12px] border-[1px] border-[#E6E6E6] border-solid">
      <div className="w-full h-full flex-1">
        <MicroStrategyCostGraph data={defaultData || data || null} loading={loading} />
      </div>
    </VStack>
  );
}
