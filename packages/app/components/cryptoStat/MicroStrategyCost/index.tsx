import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { Box } from '@quantum/components/ui/box';
import MicroStrategyCost from './MicroStrategyCost';
import CryptoStatPageFAQIndex from '../CryptoStatPageFAQ';
import ResearchReportIndex from '../../ResearchReport';
import type { MicroStrategyCost as TypeMicroStrategyCost } from '../../../services/crypto_stat/crypto_stat.types';
export default function MicroStrategyCostIndex({ defaultData, symbol }: { defaultData: TypeMicroStrategyCost[] | null; symbol: string }) {
  return (
    <HStack className="gap-6">
      <VStack className="gap-[48px] flex-1">
        <MicroStrategyCost defaultData={defaultData} />
        <CryptoStatPageFAQIndex symbol={symbol} />
      </VStack>
      <Box className="w-[377px]">
        <ResearchReportIndex />
      </Box>
    </HStack>
  );
}
