'use client';
import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { LineChart } from 'echarts/charts';
import {
  DataZoomComponent,
  GridComponent,
  TooltipComponent,
  LegendComponent,
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { SVGRenderer } from '@wuba/react-native-echarts';
import dayjs from 'dayjs';
import type { MicroStrategyCost } from '../../../services/crypto_stat/crypto_stat.types';
import { averageNumber } from '../../../uitls/number';

echarts.use([
  LineChart,
  DataZoomComponent,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  SVGRenderer,
]);

interface MicroStrategyCostGraphProps {
  data: MicroStrategyCost[] | null;
  loading?: boolean;
}

export default function MicroStrategyCostGraph({ data: defaultData, loading = false }: MicroStrategyCostGraphProps) {
  const { t } = useTranslation();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);

  // 数据处理
  const { xData, btcData, usdData, costData } = React.useMemo(() => {
    if (!defaultData?.length) {
      return { xData: [], btcData: [], usdData: [], costData: [] };
    }
    const xData = defaultData.map((item: MicroStrategyCost) => dayjs(item.date).format('YYYY/MM/DD'));
    const btcData = defaultData.map((item: MicroStrategyCost) => Number(item.btc_amount));
    const usdData = defaultData.map((item: MicroStrategyCost) => Number(item.usd_value));
    const costData = defaultData.map((item: MicroStrategyCost) => Number(item.cost_indicator));
    return { xData, btcData, usdData, costData };
  }, [defaultData]);

  // 计算Y轴范围
  const { btcMin, btcMax, usdMin, usdMax, costMin, costMax } = React.useMemo(() => {
    if (!btcData.length || !usdData.length || !costData.length) {
      return {
        btcMin: 0,
        btcMax: 100,
        usdMin: 0,
        usdMax: 100,
        costMin: 0,
        costMax: 100,
      };
    }

    const btcMin = Math.min(...btcData);
    const btcMax = Math.max(...btcData);
    const usdMin = Math.min(...usdData);
    const usdMax = Math.max(...usdData);
    const costMin = Math.min(...costData);
    const costMax = Math.max(...costData);

    // 计算合适的范围，留出10%的边距
    const btcRange = btcMax - btcMin;
    const usdRange = usdMax - usdMin;
    const costRange = costMax - costMin;

    return {
      btcMin: btcMin - btcRange * 0.1,
      btcMax: btcMax + btcRange * 0.1,
      usdMin: usdMin - usdRange * 0.1,
      usdMax: usdMax + usdRange * 0.1,
      costMin: costMin - costRange * 0.1,
      costMax: costMax + costRange * 0.1,
    };
  }, [btcData, usdData, costData]);

  // 静态配置
  const staticOption = React.useMemo(() => ({
    legend: {
      data: [
        t('crypto_stat.micro_strategy_cost.btc_total'),
        t('crypto_stat.micro_strategy_cost.btc_market_value'),
        t('crypto_stat.micro_strategy_cost.cost_indicator'),
      ],
      top: 10,
      left: 'center',
      textStyle: {
        fontSize: 12,
      },
      itemWidth: 5,
      itemHeight: 5,
      icon: 'rect',
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const idx = params?.[0]?.dataIndex ?? 0;
        let html = `${params?.[0]?.axisValue || ''}<br/>`;
        params.forEach((p: any) => {
          let value = p.seriesName === t('crypto_stat.micro_strategy_cost.btc_market_value')
            ? `$${averageNumber(p.data)}`
            : averageNumber(p.data);
          html += `<span style="color:${p.color}">●</span> ${p.seriesName}: <b>${(value)}</b><br/>`;
        });
        return html;
      },
    },
    grid: { left: 70, right: 70, top: 50, bottom: 60 },
    xAxis: {
      type: 'category',
      axisLabel: { show: true },
      boundaryGap: false,
    },
    yAxis: [
      {
        type: 'value',
        name: t('crypto_stat.micro_strategy_cost.btc_total'),
        position: 'left',
        scale: true,
        axisLabel: {
          formatter: (v: number) => (averageNumber(v)),
        },
      },
      {
        type: 'value',
        name: t('crypto_stat.micro_strategy_cost.btc_market_value'),
        position: 'right',
        scale: true,
        axisLabel: {
          formatter: (v: number) => `$${averageNumber(v)}`,
        },
        splitLine: { show: false },
      },
      {
        type: 'value',
        name: t('crypto_stat.micro_strategy_cost.cost_indicator'),
        position: 'right',
        scale: true,
        axisLabel: {
          formatter: (v: number) => averageNumber(v),
        },
        splitLine: { show: false },
        show: false,
      },
    ],
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: 0,
        bottom: 10,
        height: 20,
        start: 0,
        end: 100,
        labelFormatter: (value: number) => {
          return dayjs(xData[Math.floor(value)]).format('MM/DD');
        }
      },
    ],
    series: [
      {
        name: t('crypto_stat.micro_strategy_cost.btc_total'),
        type: 'line',
        showSymbol: false,
        connectNulls: true,
        yAxisIndex: 0,
        z: 2,
        lineStyle: { color: '#2ecc40', width: 2 },
      },
      {
        name: t('crypto_stat.micro_strategy_cost.btc_market_value'),
        type: 'line',
        showSymbol: false,
        connectNulls: true,
        yAxisIndex: 1,
        z: 1,
        lineStyle: { color: '#3498db', width: 2 },
      },
      {
        name: t('crypto_stat.micro_strategy_cost.cost_indicator'),
        type: 'line',
        showSymbol: false,
        connectNulls: true,
        yAxisIndex: 2,
        z: 1,
        lineStyle: { color: '#e74c3c', width: 2 },
      },
    ],
  }), [btcMin, btcMax, usdMin, usdMax, costMin, costMax, t]);

  useEffect(() => {
    if (!chartRef.current) return;

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        useDirtyRect: true,
      });
      
      chartInstance.current.setOption(staticOption);
    }

    // 控制加载状态
    if (loading) {
      // 清空数据
      chartInstance.current.setOption({
        xAxis: {
          data: [],
        },
        series: [
          {
            data: [],
          },
          {
            data: [],
          },
          {
            data: [],
          },
        ],
      });
      chartInstance.current.showLoading({
        text: 'Loading...',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        textColor: '#000',
        spinnerRadius: 10,
        lineWidth: 5,
        showSpinner: true,
        showText: true,
        animation: true,
      });
    } else {
      chartInstance.current.hideLoading();
      // 只在有数据时更新图表数据
      if (xData.length) {
        chartInstance.current.setOption({
          xAxis: {
            data: xData,
          },
          series: [
            {
              data: btcData,
            },
            {
              data: usdData,
            },
            {
              data: costData,
            },
          ],
        });
      }
    }

    const resize = () => chartInstance.current && chartInstance.current.resize();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', resize);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', resize);
      }
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [xData, btcData, usdData, costData, staticOption, loading]);


  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
} 