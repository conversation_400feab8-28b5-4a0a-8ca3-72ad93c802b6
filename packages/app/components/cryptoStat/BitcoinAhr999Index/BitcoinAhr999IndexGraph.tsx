'use client';
import React, { useEffect, useRef } from 'react';
import { LineChart } from 'echarts/charts';
import {
  DataZoomComponent,
  GridComponent,
  TooltipComponent,
  MarkLineComponent,
  LegendComponent,
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { SVGRenderer } from '@wuba/react-native-echarts';
import { averageNumber } from '../../../uitls/number';
import type { AHR999 } from '@quantum/app/services/crypto_stat/crypto_stat.types';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';

const BOTTOM_LINE = 0.45;
const INVEST_LINE = 1.2;

echarts.use([
  LineChart,
  DataZoomComponent,
  GridComponent,
  TooltipComponent,
  MarkLineComponent,
  LegendComponent,
  SVGRenderer,
]);

interface BitcoinAhr999IndexGraphProps {
  data: AHR999[];
  loading?: boolean;
}

export default function BitcoinAhr999IndexGraph({ data, loading = false }: BitcoinAhr999IndexGraphProps) {
  const { t } = useTranslation();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);

  const xData = data.map((item) => item.date_string);
  const btcData = data.map((item) => Number(item.current_value));
  const ahr999Data = data.map((item) => Number(item.ahr999_value));
  const cost200dData = data.map((item) => Number(item.average_price));

  // 静态配置
  const staticOption = React.useMemo(() => ({
    legend: {
      data: [
        t('crypto_stat.bitcoin_ahr999_index.dca_cost_200d'),
        t('crypto_stat.bitcoin_ahr999_index.btc_price'),
        t('crypto_stat.bitcoin_ahr999_index.ahr999_index'),
        t('crypto_stat.bitcoin_ahr999_index.bottom_line'),
        t('crypto_stat.bitcoin_ahr999_index.invest_line'),
      ],
      top: 10,
      left: 'center',
      textStyle: { fontSize: 12 },
      itemWidth: 8,
      itemHeight: 8,
      icon: 'rect',
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const idx = params?.[0]?.dataIndex ?? 0;
        let html = `${params?.[0]?.axisValue || ''}<br/>`;
        params.forEach((p: any) => {
          let name = p.seriesName;
          let value = p.data;
          if (name === t('crypto_stat.bitcoin_ahr999_index.btc_price')) {
            html += `<span style="color:${p.color}">●</span> ${t('crypto_stat.bitcoin_ahr999_index.btc_price')}: <b>$${averageNumber(value)}</b><br/>`;
          } else if (name === t('crypto_stat.bitcoin_ahr999_index.dca_cost_200d')) {
            html += `<span style="color:${p.color}">●</span> ${t('crypto_stat.bitcoin_ahr999_index.dca_cost_200d')}: <b>$${averageNumber(value)}</b><br/>`;
          } else if (name === t('crypto_stat.bitcoin_ahr999_index.ahr999_index')) {
            html += `<span style="color:${p.color}">●</span> ${t('crypto_stat.bitcoin_ahr999_index.ahr999_index')}: <b>${averageNumber(value)}</b><br/>`;
          } else if (name === t('crypto_stat.bitcoin_ahr999_index.bottom_line')) {
            html += `<span style="color:${p.color}">●</span> ${t('crypto_stat.bitcoin_ahr999_index.bottom_line')}: <b>${averageNumber(value)}</b><br/>`;
          } else if (name === t('crypto_stat.bitcoin_ahr999_index.invest_line')) {
            html += `<span style="color:${p.color}">●</span> ${t('crypto_stat.bitcoin_ahr999_index.invest_line')}: <b>${averageNumber(value)}</b><br/>`;
          }
        });
        return html;
      },
    },
    grid: {
      left: 50,
      right: 70,
      top: 40,
      bottom: 55,
    },
    xAxis: {
      type: 'category',
      axisLabel: { show: true },
      boundaryGap: false,
    },
    yAxis: [
      {
        type: 'log',
        logBase: 10,
        // name: t('crypto_stat.bitcoin_ahr999_index.ahr999_index'),
        position: 'left',
        // min: 0.1,
        // max: 1000,
        scale: true,
        axisLabel: {
          formatter: (v: number) => v,
        },
      },
      {
        type: 'log',
        // name: t('crypto_stat.bitcoin_ahr999_index.btc_price_dca_cost'),
        // min: 0.1,
        // max: 1000000,
        position: 'right',
        scale: true,
        axisLabel: {
          formatter: (v: number) => averageNumber(v),
        },
        splitLine: { show: false },
      },
    ],
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: 0,
        bottom: 10,
        height: 20,
        start: 0,
        end: 100,
        labelFormatter: (value: number) => {
          return dayjs(xData[Math.floor(value)]).format('MM/DD');
        }
      },
    ],
    series: [
      {
        name: t('crypto_stat.bitcoin_ahr999_index.dca_cost_200d'),
        type: 'line',
        yAxisIndex: 1,
        lineStyle: { color: '#00bcd4', width: 2 },
        showSymbol: false,
        connectNulls: true,
        z: 2,
      },
      {
        name: t('crypto_stat.bitcoin_ahr999_index.btc_price'),
        type: 'line',
        yAxisIndex: 1,
        lineStyle: { color: '#1976d2', width: 2 },
        showSymbol: false,
        connectNulls: true,
        z: 2,
      },
      {
        name: t('crypto_stat.bitcoin_ahr999_index.ahr999_index'),
        type: 'line',
        yAxisIndex: 0,
        lineStyle: { color: '#ffd600', width: 2 },
        showSymbol: false,
        connectNulls: true,
        z: 3,
      },
      {
        name: t('crypto_stat.bitcoin_ahr999_index.bottom_line'),
        type: 'line',
        yAxisIndex: 0,
        lineStyle: { color: '#e53935', type: 'dashed', width: 1 },
        showSymbol: false,
        connectNulls: true,
        z: 1,
        areaStyle: {
          color: '#e53935',
          opacity: 0.3,
        },
      },
      {
        name: t('crypto_stat.bitcoin_ahr999_index.invest_line'),
        type: 'line',
        yAxisIndex: 0,
        lineStyle: { color: '#43a047', type: 'dashed', width: 1 },
        showSymbol: false,
        connectNulls: true,
        z: 1,
      },
    ],
  }), [t]);

  useEffect(() => {
    if (!chartRef.current) return;

    if (!chartInstance.current) {
      // 初始化
      chartInstance.current = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        useDirtyRect: true,
      });
      
      // 设置初始配置
      chartInstance.current.setOption(staticOption);
    }

    // 控制加载状态
    if (loading) {
      // 清空数据
      chartInstance.current.setOption({
        xAxis: {
          data: [],
        },
        series: [
          {
            data: [],
          },
          {
            data: [],
          },
          {
            data: [],
          },
          {
            data: [],
          },
          {
            data: [],
          },
        ],
      });
      chartInstance.current.showLoading({
        text: 'Loading...',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        textColor: '#000',
        spinnerRadius: 10,
        lineWidth: 5,
        showSpinner: true,
        showText: true,
        animation: true,
      });
    } else {
      chartInstance.current.hideLoading();
      // 只在有数据时更新图表数据
      if (xData.length) {
        chartInstance.current.setOption({
          xAxis: {
            data: xData,
          },
          series: [
            {
              data: cost200dData,
            },
            {
              data: btcData,
            },
            {
              data: ahr999Data,
            },
            {
              data: Array(xData.length).fill(BOTTOM_LINE),
            },
            {
              data: Array(xData.length).fill(INVEST_LINE),
            },
          ],
        });
      }
    }

    // 响应式
    const resize = () => chartInstance.current && chartInstance.current.resize();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', resize);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', resize);
      }
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [xData, btcData, ahr999Data, cost200dData, staticOption, loading]);

  if (!xData.length) return <div>{t('crypto_stat.common.no_data')}</div>;

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
}
