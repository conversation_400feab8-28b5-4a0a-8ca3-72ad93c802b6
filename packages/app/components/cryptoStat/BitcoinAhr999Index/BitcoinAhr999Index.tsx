'use client';
import type { AHR999 } from '@quantum/app/services/crypto_stat/crypto_stat.types';
import { Box } from '@quantum/components/ui/box';
import { Grid, GridItem } from '@quantum/components/ui/grid';
import { HStack } from '@quantum/components/ui/hstack';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { averageNumber } from '../../../uitls/number';
import CryptoStatPageFAQ from '../CryptoStatPageFAQ/CryptoStatPageFAQ';
import BitcoinAhr999IndexGraph from './BitcoinAhr999IndexGraph';
import ResearchReportList from '../../ResearchReport/ResearchReportList';
export default function BitcoinAhr999({ defaultData}: { defaultData: AHR999[];}) {
  const { t } = useTranslation();
  // 1. 获取数据
  const data = useMemo(() => {
    return defaultData;
  }, [defaultData]);
  // 2. 数据处理
  const { tableData } = useMemo(() => {
    const arr: AHR999[] = data || [];
    // 表格展示最近10条，倒序
    const tableData = arr.slice(-100).reverse();
    return { tableData };
  }, [data]);

  const info = useMemo(() => {
    const dataLte12 = data.filter((item) => Number(item.ahr999_value) >= 1.2).length;
    const dataBetween = data.filter(
      (item) => Number(item.ahr999_value) >= 0.45 && Number(item.ahr999_value) < 1.2,
    ).length;
    const dataGte045 = data.filter((item) => Number(item.ahr999_value) < 0.45).length;

    return [
      {
        label: t('crypto_stat.bitcoin_ahr999_index.label_gt_1_2'),
        value: dataLte12,
      },
      {
        label: t('crypto_stat.bitcoin_ahr999_index.label_between_0_45_1_2'),
        value: dataBetween,
      },
      {
        label: t('crypto_stat.bitcoin_ahr999_index.label_lt_0_45'),
        value: dataGte045,
      },
    ];
  }, [data, t]);

  return (
     
        <HStack className="gap-6 w-full">
          <VStack className="gap-6 flex-1">
            <VStack className="h-[346px] gap-4 p-4 bg-gradient-to-b from-[rgba(5,198,151,0.012)] to-[rgba(5,198,151,0.03)] rounded-[12px] border-[1px] border-[#E6E6E6] border-solid">
              <Grid
                className="flex-1 gap-4"
                _extra={{
                  className: 'grid-cols-12',
                }}
              >
                {info.map((item) => (
                  <GridItem
                    key={item.label}
                    _extra={{
                      className: 'col-span-12 md:col-span-4',
                    }}
                  >
                    <VStack className="flex-1 gap-[6px] py-2 px-3 border-[1px] border-[#E6E6E6] border-solid rounded-[4px] ">
                      <Text className="text-[#4D4D4D] font-[400] text-[12px] leading-[16px]">{item.label}</Text>
                      <Text className="text-[#0A0A0A] font-[500] text-[12px] leading-[16px]">
                        {item.value} {t('crypto_stat.bitcoin_ahr999_index.day')}
                      </Text>
                    </VStack>
                  </GridItem>
                ))}
              </Grid>
              <BitcoinAhr999IndexGraph data={data} />
            </VStack>
            <Box className="border-solid border-[1px] border-[#E6E6E6] w-full rounded-[12px] h-[500px] overflow-y-auto relative">
              <table>
                <thead className='sticky top-0 bg-[#ffffff] z-10'>
                  <tr className="bg-[#05C69708] text-[#4D4D4D] text-[14px] font-[400] leading-[20px]">
                    <th className="p-3">{t('crypto_stat.bitcoin_ahr999_index.time')}</th>
                    <th className="p-3">{t('crypto_stat.bitcoin_ahr999_index.ahr999_index')}</th>
                    <th className="p-3">{t('crypto_stat.bitcoin_ahr999_index.btc_price')}</th>
                    <th className="p-3">{t('crypto_stat.bitcoin_ahr999_index.dca_cost')}</th>
                  </tr>
                </thead>
                <tbody>
                  {tableData.map((item) => (
                    <tr
                      key={item.date_string}
                      className="h-[64px] text-[#0A0A0A] text-[14px] font-[600] leading-[20px] text-center even:bg-[#05C69708]"
                    >
                      <td className="p-3">{item.date_string}</td>
                      <td className="p-3">{averageNumber(item.ahr999_value)}</td>
                      <td className="p-3">${averageNumber(item.current_value)}</td>
                      <td className="p-3">${averageNumber(item.average_price)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </Box>
          </VStack>
          {/* <Box className="w-[377px]">
          <VStack className="gap-2 p-4 bg-gradient-to-b from-[rgba(5,198,151,0.012)] to-[rgba(5,198,151,0.03)] rounded-[12px] border-[1px] border-[#E6E6E6] border-solid">
            <Text className="text-[#0A0A0A] text-[16px] font-[600] leading-[20px]">
              {t('crypto_stat.bitcoin_ahr999_index.page_title')}
            </Text>
            <Text className="text-[#808080] text-[14px] font-[400] leading-[20px]">
              {t('crypto_stat.bitcoin_ahr999_index.page_content')}
            </Text>
          </VStack>
        </Box> */}
        </HStack>
  );
}
