import ResearchReportIndex from '../../ResearchReport';
import { HStack } from '@quantum/components/ui/hstack';
import { Box } from '@quantum/components/ui/box';
import BitcoinAhr999 from './BitcoinAhr999Index';
import type { AHR999 } from '@quantum/app/services/crypto_stat/crypto_stat.types';
import { VStack } from '@quantum/components/ui/vstack';
import CryptoStatPageFAQIndex from '../CryptoStatPageFAQ';
export default function BitcoinAhr999Index({ defaultData, symbol }: { defaultData: AHR999[], symbol: string}) {
  return (
    <HStack className="gap-6">
      <VStack className="gap-[48px] flex-1">
        <BitcoinAhr999 defaultData={defaultData} />

        <CryptoStatPageFAQIndex symbol={symbol} />
      </VStack>
      <Box className="w-[377px]">
        <ResearchReportIndex />
      </Box>
    </HStack>
  );
}
