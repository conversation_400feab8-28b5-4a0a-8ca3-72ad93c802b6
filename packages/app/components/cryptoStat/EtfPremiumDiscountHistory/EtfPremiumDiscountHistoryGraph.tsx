'use client';
import { useState, useEffect, useRef, useMemo } from 'react';
import { LineChart } from 'echarts/charts';
import { DataZoomComponent, GridComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import * as echarts from 'echarts/core';
import { SVGRenderer } from '@wuba/react-native-echarts';
import type { CommonResponse } from '../../../services/crypto_stat/crypto_stat.types';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';

interface EtfItem {
  nav_usd: number;
  market_price_usd: number;
  premium_discount_details: number;
  ticker: string;
}

interface DataItem {
  timestamp: number;
  list: EtfItem[];
}

interface EtfPremiumDiscountHistoryGraphProps {
  data: CommonResponse['data'];
  selected: string;
  loading?: boolean;
}

echarts.use([LineChart, DataZoomComponent, GridComponent, TooltipComponent, LegendComponent, SV<PERSON>enderer]);

export default function EtfPremiumDiscountHistoryGraph({ data, selected, loading = false }: EtfPremiumDiscountHistoryGraphProps) {
  const { t } = useTranslation();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);

  const selectList = [
    {
      label: t('crypto_stat.etf_premium_discount_history.premium_discount'),
      i18nKey: 'crypto_stat.etf_premium_discount_history.premium_discount',
      value: 'premium_discount_details',
    },
    {
      label: t('crypto_stat.etf_premium_discount_history.market_price'),
      i18nKey: 'crypto_stat.etf_premium_discount_history.market_price',
      value: 'market_price_usd',
    },
    {
      label: t('crypto_stat.etf_premium_discount_history.nav'),
      i18nKey: 'crypto_stat.etf_premium_discount_history.nav',
      value: 'nav_usd',
    },
  ];

  // 静态配置
  const staticOption = useMemo(() => ({
    legend: {
      top: 10,
      left: 'center',
      textStyle: { fontSize: 12 },
      itemWidth: 8,
      itemHeight: 8,
      icon: 'rect',
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const idx = params?.[0]?.dataIndex ?? 0;
        let html = `${params?.[0]?.axisValue || ''}<br/>`;
        params.forEach((p: any) => {
          if (p.data === null) return;
          let value = p.data;
          if (selected === 'premium_discount_details') {
            html += `<span style="color:${p.color}">●</span> ${p.seriesName}: <b>${value}%</b><br/>`;
          } else {
            html += `<span style="color:${p.color}">●</span> ${p.seriesName}: <b>$${value}</b><br/>`;
          }
        });
        return html;
      },
    },
    grid: { left: 50, right: 50, top: 50, bottom: 60 },
    xAxis: {
      type: 'category',
      axisLabel: { show: true },
      boundaryGap: false,
    },
    yAxis: {
      type: 'value',
      name: '',
      position: 'left',
      axisLabel: {
        formatter: (v: number) => {
          if (selected === 'premium_discount_details') {
            return `${v}%`;
          }
          return v > 1000 ? `$${(v / 1000).toFixed(1)}k` : `$${v}`;
        },
      },
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: 0,
        bottom: 10,
        height: 20,
        start: 0,
        end: 100,
        labelFormatter: (value: number) => {
          return dayjs(xData[Math.floor(value)]).format('MM/DD');
        }
      },
    ],
    series: [],
  }), [selected, t]);

  // 数据处理
  const { xData, seriesData } = useMemo(() => {
    const arr = (data || []) as DataItem[];
    const xData = arr.map((item) => dayjs(item.timestamp).format('YYYY/MM/DD'));

    // 获取所有唯一的 ticker
    const tickers = Array.from(new Set(arr.flatMap((item) => item.list.map((etf) => etf.ticker))));

    // 为每个 ticker 创建数据系列
    const seriesData = tickers.map((ticker) => {
      const data = arr.map((item) => {
        const etf = item.list.find((e) => e.ticker === ticker);
        return etf ? Number(etf[selected as keyof EtfItem]) : null;
      });

      return {
        name: ticker,
        type: 'line',
        data,
        smooth: true,
        lineStyle: { width: 2 },
        showSymbol: false,
        connectNulls: true,
      };
    });

    return { xData, seriesData };
  }, [data, selected]);

  useEffect(() => {
    if (!chartRef.current) return;

    if (!chartInstance.current) {
      // 初始化
      chartInstance.current = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        useDirtyRect: true,
      });
      
      // 设置初始配置
      chartInstance.current.setOption(staticOption);
    }

    // 控制加载状态
    if (loading) {
      // 清空数据
      chartInstance.current.setOption({
        xAxis: {
          data: [],
        },
        series: seriesData.map(() => ({
          data: [],
        })),
      });
      chartInstance.current.showLoading({
        text: 'Loading...',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        textColor: '#000',
        spinnerRadius: 10,
        lineWidth: 5,
        showSpinner: true,
        showText: true,
        animation: true,
      });
    } else {
      chartInstance.current.hideLoading();
      // 只在有数据时更新图表数据
      if (xData.length) {
        chartInstance.current.setOption({
          legend: {
            data: seriesData.map((item) => item.name),
          },
          xAxis: {
            data: xData,
          },
          series: seriesData,
        });
      }
    }

    // 响应式
    const resize = () => chartInstance.current && chartInstance.current.resize();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', resize);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', resize);
      }
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [xData, seriesData, staticOption, loading]);

  if (!xData.length) return <div>{t('crypto_stat.common.no_data')}</div>;

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
} 