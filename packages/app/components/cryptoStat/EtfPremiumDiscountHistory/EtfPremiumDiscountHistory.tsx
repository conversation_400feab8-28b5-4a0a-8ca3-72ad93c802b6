'use client';
import { But<PERSON> } from '@quantum/components/ui/button';
import { HStack } from '@quantum/components/ui/hstack';
import { Menu, MenuItem } from '@quantum/components/ui/menu';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { useRequest } from 'ahooks';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RiArrowDownSLine, RiCheckLine } from 'react-icons/ri';
import { getEtfBitcoinPremiumDiscountHistory } from '../../../services/crypto_stat/crypto_stat.api';
import type { CommonResponse } from '../../../services/crypto_stat/crypto_stat.types';
import EtfPremiumDiscountHistoryGraph from './EtfPremiumDiscountHistoryGraph';

export default function EtfPremiumDiscountHistory({
  defaultData,
}: {
  defaultData: CommonResponse['data'];
}) {
  const { data = defaultData } = useRequest(
    async () => {
      const res = await getEtfBitcoinPremiumDiscountHistory();
      return res;
    },
    {
      ready: !defaultData,
    },
  );
  const [selected, setSelected] = useState('premium_discount_details');

  const { t } = useTranslation();
  const selectList = [
    {
      label: t('crypto_stat.etf_premium_discount_history.premium_discount'),
      i18nKey: 'crypto_stat.etf_premium_discount_history.premium_discount',
      value: 'premium_discount_details',
    },
    {
      label: t('crypto_stat.etf_premium_discount_history.market_price'),
      i18nKey: 'crypto_stat.etf_premium_discount_history.market_price',
      value: 'market_price_usd',
    },
    {
      label: t('crypto_stat.etf_premium_discount_history.nav'),
      i18nKey: 'crypto_stat.etf_premium_discount_history.nav',
      value: 'nav_usd',
    },
  ];

  return (
    <VStack className="h-[454px] gap-4 p-4 bg-gradient-to-b from-[rgba(5,198,151,0.012)] to-[rgba(5,198,151,0.03)] rounded-[12px] border-[1px] border-[#E6E6E6] border-solid">
          <HStack>
            <Menu
              placement="bottom left"
              offset={5}
              trigger={({ ...triggerProps }) => {
                return (
                  <Button variant="link" {...triggerProps} className="h-4">
                    <Text className="text-[#0A0A0A] font-Inter text-[12px] font-not-italic font-[400] leading-[16px]">
                      {t(selectList.find((item) => item.value === selected)?.i18nKey || '')}
                    </Text>
                    <RiArrowDownSLine size={16} className="text-[#808080]" />
                  </Button>
                );
              }}
              className="p-[10px] rounded-[6px] border-[1px] border-solid border-[#E6E6E6] bg-[#FFF] shadow-[0px_8px_20px_0px_rgba(0,0,0,0.10)]"
              selectionMode="single"
            >
              {selectList.map((item) => (
                <MenuItem
                  onPress={() => {
                    setSelected(item.value);
                  }}
                  key={item.value}
                  textValue={item.value}
                  className="px-[8px] py-[10px] items-center gap-[16px] self-stretch rounded-[6px] bg-[#FFF] data-[hover=true]:bg-[rgba(0,173,107,0.05)]"
                >
                  <HStack className="items-center justify-between w-full">
                    <Text className="text-[#4D4D4D] font-Poppins text-[14px] font-not-italic font-[400] leading-[20px]">
                      {t(item.i18nKey || '')}
                    </Text>
                    {selected === item.value && <RiCheckLine size={16} className="text-[#40C382]" />}
                  </HStack>
                </MenuItem>
              ))}
            </Menu>
          </HStack>
          <div className="w-full h-full flex-1">
            <EtfPremiumDiscountHistoryGraph data={data || []} selected={selected} />
          </div>
        </VStack>
  );
}
