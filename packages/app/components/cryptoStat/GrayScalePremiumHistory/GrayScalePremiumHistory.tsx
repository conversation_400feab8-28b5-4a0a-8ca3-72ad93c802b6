'use client';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { useRequest } from 'ahooks';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getGrayscalePremiumHistory } from '../../../services/crypto_stat/crypto_stat.api';
import SelectCryptoCoin, { defaultSymbolList as symbolList } from '../SelectCryptoCoin';
import GrayScalePremiumHistoryGraph from './GrayScalePremiumHistoryGraph';

export default function GrayScalePremiumHistory() {
  const { t } = useTranslation();
  const [selected, setSelected] = useState(symbolList[0].value);

  const {
    data = {
      premium_rate_list: [],
      primary_market_price: [],
      secondary_market_price_list: [],
      time_list: [],
    },
    loading,
  } = useRequest(
    async () => {
      const res = await getGrayscalePremiumHistory({
        symbol: selected,
      });
      return res as {
        premium_rate_list: number[];
        primary_market_price: number[];
        secondary_market_price_list: number[];
        time_list: number[];
      };
    },
    {
      refreshDeps: [selected],
    },
  );

  return (
    <VStack className="h-[454px] gap-4 p-4 bg-gradient-to-b from-[rgba(5,198,151,0.012)] to-[rgba(5,198,151,0.03)] rounded-[12px] border-[1px] border-[#E6E6E6] border-solid">
      <HStack>
        <SelectCryptoCoin value={selected} onChange={setSelected} />
      </HStack>
      <div className="w-full h-full flex-1">
        <GrayScalePremiumHistoryGraph data={data} loading={loading} />
      </div>
    </VStack>
  );
}
