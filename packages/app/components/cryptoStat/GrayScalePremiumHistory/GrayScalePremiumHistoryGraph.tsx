'use client';
import React, { useEffect, useRef } from 'react';
import { LineChart } from 'echarts/charts';
import { TooltipComponent, LegendComponent, GridComponent, DataZoomComponent } from 'echarts/components';
import * as echarts from 'echarts/core';
import { SVGRenderer } from '@wuba/react-native-echarts';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

echarts.use([LineChart, DataZoomComponent, TooltipComponent, LegendComponent, GridComponent, SVGRenderer]);

interface GrayScalePremiumHistoryGraphProps {
  data: {
    premium_rate_list: number[];
    primary_market_price: number[];
    secondary_market_price_list: number[];
    time_list: number[];
  };
  loading?: boolean;
}

export default function GrayScalePremiumHistoryGraph({ data, loading = false }: GrayScalePremiumHistoryGraphProps) {
  const { t } = useTranslation();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);

  // 静态配置
  const staticOption = React.useMemo(() => ({
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const idx = params?.[0]?.dataIndex ?? 0;
        let result = `${params?.[0]?.axisValue || ''}<br/>`;
        params.forEach((param: any) => {
          const value = param.value === null ? '-' : param.value;
          result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ${
            param.seriesName !== t('crypto_stat.gray_scale_premium_history_graph.premium_rate') ? '$' : ''
          }${value}${param.seriesName === t('crypto_stat.gray_scale_premium_history_graph.premium_rate') ? '%' : ''}<br/>`;
        });
        return result;
      },
    },
    legend: {
      data: [
        t('crypto_stat.gray_scale_premium_history_graph.secondary_market_price'),
        t('crypto_stat.gray_scale_premium_history_graph.primary_market_price'),
        t('crypto_stat.gray_scale_premium_history_graph.premium_rate')
      ],
      top: 0,
    },
    grid: {
      left: 30,
      right: 70,
      top: 30,
      bottom: 70,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisLabel: { show: true },
    },
    yAxis: [
      {
        type: 'value',
        name: '',
        position: 'left',
        axisLabel: {
          formatter: '${value}',
        },
      },
      {
        type: 'value',
        name: '',
        position: 'right',
        axisLabel: {
          formatter: '{value}%',
        },
      },
    ],
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: 0,
        bottom: 10,
        height: 20,
        start: 95,
        end: 100,
        labelFormatter: (value: number) => {
          return dayjs(xData[Math.floor(value)]).format('MM/DD');
        }
      },
    ],
    series: [
      {
        name: t('crypto_stat.gray_scale_premium_history_graph.secondary_market_price'),
        type: 'line',
        smooth: true,
        showSymbol: false,
      },
      {
        name: t('crypto_stat.gray_scale_premium_history_graph.primary_market_price'),
        type: 'line',
        smooth: true,
        showSymbol: false,
      },
      {
        name: t('crypto_stat.gray_scale_premium_history_graph.premium_rate'),
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        showSymbol: false,
      },
    ],
  }), [t]);

  // 数据处理
  const { xData, secondaryMarketData, primaryMarketData, premiumRateData } = React.useMemo(() => {
    if (!data.time_list?.length) {
      return {
        xData: [],
        secondaryMarketData: [],
        primaryMarketData: [],
        premiumRateData: [],
      };
    }

    const xData = data.time_list.map((ts) => dayjs(ts).format('YYYY/MM/DD'));
    const secondaryMarketData = data.secondary_market_price_list;
    const primaryMarketData = data.primary_market_price;
    const premiumRateData = data.premium_rate_list;

    return {
      xData,
      secondaryMarketData,
      primaryMarketData,
      premiumRateData,
    };
  }, [data]);

  useEffect(() => {
    if (!chartRef.current) return;

    if (!chartInstance.current) {
      // 初始化
      chartInstance.current = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        useDirtyRect: true,
      });
      
      // 设置初始配置
      chartInstance.current.setOption(staticOption);
    }

    // 控制加载状态
    if (loading) {
      // 清空数据
      chartInstance.current.setOption({
        xAxis: {
          data: [],
        },
        series: [
          {
            data: [],
          },
          {
            data: [],
          },
          {
            data: [],
          },
        ],
      });
      chartInstance.current.showLoading({
        text: 'Loading...',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        textColor: '#000',
        spinnerRadius: 10,
        lineWidth: 5,
        showSpinner: true,
        showText: true,
        animation: true,
      });
    } else {
      chartInstance.current.hideLoading();
      // 只在有数据时更新图表数据
      if (xData.length) {
        chartInstance.current.setOption({
          xAxis: {
            data: xData,
          },
          series: [
            {
              data: secondaryMarketData,
            },
            {
              data: primaryMarketData,
            },
            {
              data: premiumRateData,
            },
          ],
        });
      }
    }

    // 响应式
    const resize = () => chartInstance.current && chartInstance.current.resize();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', resize);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', resize);
      }
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [xData, secondaryMarketData, primaryMarketData, premiumRateData, staticOption, loading]);


  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
} 