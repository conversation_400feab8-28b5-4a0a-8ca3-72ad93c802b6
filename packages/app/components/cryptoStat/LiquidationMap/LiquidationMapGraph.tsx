'use client';
import React, { useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON>, LineChart } from 'echarts/charts';
import { TooltipComponent, LegendComponent, GridComponent, DataZoomComponent } from 'echarts/components';
import * as echarts from 'echarts/core';
import { SVGRenderer } from '@wuba/react-native-echarts';
import { useTranslation } from 'react-i18next';
import { averageNumber } from '../../../uitls/number';

echarts.use([BarC<PERSON>, LineChart, DataZoomComponent, TooltipComponent, LegendComponent, GridComponent, SVGRenderer]);

interface LiquidationMapGraphProps {
  data: any[];
  loading?: boolean;
}

export default function LiquidationMapGraph({ data, loading = false }: LiquidationMapGraphProps) {
  const { t } = useTranslation();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);

  // 数据处理
  const { xData, bar10x, bar25x, bar50x, bar100x, longLine, shortLine } = React.useMemo(() => {
    if (!data?.length) {
      return { xData: [], bar10x: [], bar25x: [], bar50x: [], bar100x: [], longLine: [], shortLine: [] };
    }
    const xData = data.map((item: any) => item.btc_price);
    const bar10x = data.map((item: any) => item['10x'] || null);
    const bar25x = data.map((item: any) => item['25x'] || null);
    const bar50x = data.map((item: any) => item['50x'] || null);
    const bar100x = data.map((item: any) => item['100x'] || null);
    const longLine = data.map((item: any) => item.accumulate_liq_usd > 0 ? item.accumulate_liq_usd : null);
    const shortLine = data.map((item: any) => item.accumulate_liq_usd < 0 ? Math.abs(item.accumulate_liq_usd) : null);
    return { xData, bar10x, bar25x, bar50x, bar100x, longLine, shortLine };
  }, [data]);


  // 静态配置
  const staticOption = React.useMemo(() => ({
    backgroundColor: '#ffffff',
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params?.[0]?.axisValue || ''}<br/>`;
        params.forEach((param: any) => {
          if (
            param.value !== null &&
            param.value !== undefined &&
            param.value !== '' &&
            param.value !== '-' &&
            !Number.isNaN(param.value)
          ) {
            result += `<span style="color:${param.color}">●</span> ${param.seriesName}: $${averageNumber(Math.abs(param.value))}<br/>`;
          }
        });
        return result;
      },
    },
    legend: {
      data: [
        '10x ' + t('crypto_stat.liquidation_map.leverage'), 
        '25x ' + t('crypto_stat.liquidation_map.leverage'), 
        '50x ' + t('crypto_stat.liquidation_map.leverage'), 
        '100x ' + t('crypto_stat.liquidation_map.leverage'),
        t('crypto_stat.liquidation_map.long_accumulate'),
        t('crypto_stat.liquidation_map.short_accumulate'),
      ],
      top: 20,
    },
    grid: {
      left: 50,
      right: 70,
      top: 70,
      bottom: 90,
    },
    xAxis: {
      type: 'category',
      axisLine: { lineStyle: { color: '#444', type: 'dashed' } },
      data: xData,
      name: '',
      nameLocation: 'center',
      nameGap: 30,
    },
    yAxis: [
      {
        type: 'value',
        position: 'left',
        axisLabel: {
          formatter: (v: number) => averageNumber(v),
        },
        splitLine: { lineStyle: { color: '#444', type: 'dashed' } },
      },
      {
        type: 'value',
        position: 'right',
        axisLabel: {
          formatter: (v: number) => averageNumber(v),
        },
        splitLine: { show: false },
      },
    ],
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: 0,
        bottom: 30,
        height: 20,
        start: 0,
        end: 100,
        labelFormatter: (value: number) => xData[Math.floor(value)] || '',
      },
    ],
    series: [
      {
        name: '10x ' + t('crypto_stat.liquidation_map.leverage'),
        type: 'bar',
        stack: 'leverage',
        itemStyle: { color: '#3B82F6' },
        data: bar10x,
        barGap: 0,
      },
      {
        name: '25x ' + t('crypto_stat.liquidation_map.leverage'),
        type: 'bar',
        stack: 'leverage',
        itemStyle: { color: '#FACC15' },
        data: bar25x,
      },
      {
        name: '50x ' + t('crypto_stat.liquidation_map.leverage'),
        type: 'bar',
        stack: 'leverage',
        itemStyle: { color: '#FB923C' },
        data: bar50x,
      },
      {
        name: '100x ' + t('crypto_stat.liquidation_map.leverage'),
        type: 'bar',
        stack: 'leverage',
        itemStyle: { color: '#A16207' },
        data: bar100x,
      },
      {
        name: t('crypto_stat.liquidation_map.long_accumulate'),
        type: 'line',
        yAxisIndex: 1,
        itemStyle: { color: '#EF4444' },
        data: longLine,
        symbol: 'none',
        lineStyle: { width: 2 },
      },
      {
        name: t('crypto_stat.liquidation_map.short_accumulate'),
        type: 'line',
        yAxisIndex: 1,
        itemStyle: { color: '#10B981' },
        data: shortLine,
        symbol: 'none',
        lineStyle: { width: 2 },
      },
    ],
  }), [t, xData, bar10x, bar25x, bar50x, bar100x, longLine, shortLine]);

  useEffect(() => {
    if (!chartRef.current) return;

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        useDirtyRect: true,
      });
      chartInstance.current.setOption(staticOption);
    }

    if (loading) {
      chartInstance.current.setOption({
        xAxis: { data: [] },
        series: staticOption.series.map(() => ({ data: [] })),
      });
      chartInstance.current.showLoading({
        text: 'Loading...',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        textColor: '#000',
        spinnerRadius: 10,
        lineWidth: 5,
        showSpinner: true,
        showText: true,
        animation: true,
      });
    } else {
      chartInstance.current.hideLoading();
      if (xData.length) {
        chartInstance.current.setOption({
          xAxis: { data: xData },
          series: [
            { data: bar10x },
            { data: bar25x },
            { data: bar50x },
            { data: bar100x },
            { data: longLine },
            { data: shortLine },
          ],
        });
      }
    }

    const resize = () => chartInstance.current && chartInstance.current.resize();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', resize);
    }
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', resize);
      }
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [xData, bar10x, bar25x, bar50x, bar100x, longLine, shortLine, staticOption, loading]);

  return (
    <div
      ref={chartRef}
      style={{
        width: '100%',
        height: '100%',
        position: 'relative',
        overflow: 'hidden',
      }}
    />
  );
} 