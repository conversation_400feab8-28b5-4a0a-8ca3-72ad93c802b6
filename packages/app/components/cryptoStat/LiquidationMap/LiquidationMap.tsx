'use client';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { useRequest } from 'ahooks';
import { useMemo, useState } from 'react';
import {
  getBtcLinnerLiquidationMap,
  getCoinsLiquidationMap,
  getCoinsLiquidationSymbols,
  getExchangeLiquidationSymbols,
  getHyperLiquidationMap,
  getHyperLiquidationSymbols,
} from '../../../services/crypto_stat/crypto_stat.api';
import SelectUI from '../../SelectUI/SelectUI';
import { useTranslation } from 'react-i18next';
import type {
  CoinsLiqMapParam,
  BTCLinnerLiqMapParam,
  HyperLiqMapParam,
} from '../../../services/crypto_stat/crypto_stat.types';
import LiquidationMapGraph from './LiquidationMapGraph';
import CryptoStatPageFAQ from '../CryptoStatPageFAQ/CryptoStatPageFAQ';
import ResearchReportList from '../../ResearchReport/ResearchReportList';
import { Text } from '@quantum/components/ui/text';
import BlockWrap from './BlockWrap';
import CoinsLiquidationGraph from './CoinsLiquidationGraph';
import HyperLiquidationGraph from './HyperLiquidationGraph';

function useDateList() {
  const { t } = useTranslation();
  const dateList = useMemo(() => {
    return [
      {
        value: 1,
        label: '1' + t('crypto_stat.day'),
      },
      {
        value: 7,
        label: '7' + t('crypto_stat.day'),
      },
      {
        value: 30,
        label: '30' + t('crypto_stat.day'),
      },
    ];
  }, [t]);
  return dateList;
}

function BTCMap() {
  const { t } = useTranslation();

  const [selectedDate, setSelectedDate] = useState<BTCLinnerLiqMapParam['interval']>(1);
  const [selectedSymbol, setSelectedSymbol] = useState<BTCLinnerLiqMapParam['symbol'] | null>(null);
  const { data: defaultSymbolList = [], loading: symbolLoading } = useRequest(async () => {
    const res = await getExchangeLiquidationSymbols();
    const { symbols = [] } = res;
    setSelectedSymbol(symbols[0]);
    return symbols;
  });
  const symbolList = useMemo(() => {
    return defaultSymbolList.map((item: any) => ({
      value: item,
      label: item + ' ' + t('crypto_stat.liquidation_map.perpetual_contract'),
    }));
  }, [defaultSymbolList, t]);
  const dateList = useDateList();
  const { data: liquidationMapData = [], loading } = useRequest(
    async () => {
      if (!selectedSymbol) return [];
      const res = await getBtcLinnerLiquidationMap({
        symbol: selectedSymbol,
        interval: selectedDate,
      });
      return res;
    },
    {
      ready: !!selectedSymbol && !!selectedDate,
      refreshDeps: [selectedSymbol, selectedDate],
    },
  );
  return (
    <BlockWrap>
      <HStack className="gap-2 items-center justify-between">
        <Text>
          {selectedSymbol} {t('crypto_stat.liquidation_map.perpetual_contract_liquidation_map_desc')}
        </Text>
        <HStack className="gap-2">
          <SelectUI list={symbolList} value={selectedSymbol} onChange={(value) => setSelectedSymbol(value)} />
          <SelectUI list={dateList} value={selectedDate} onChange={(value) => setSelectedDate(value)} />
        </HStack>
      </HStack>
      <div className="w-full h-full flex-1">
        <LiquidationMapGraph data={liquidationMapData} loading={symbolLoading || loading} />
      </div>
    </BlockWrap>
  );
}
function CoinsLiquidationMap() {
  const { t } = useTranslation();

  const [selectedDate, setSelectedDate] = useState<CoinsLiqMapParam['interval']>(1);
  const [selectedSymbol, setSelectedSymbol] = useState<CoinsLiqMapParam['symbol'] | null>(null);
  const { data: defaultSymbolList = [], loading: symbolLoading } = useRequest(async () => {
    const res = await getCoinsLiquidationSymbols();
    const { symbols = [] } = res;
    setSelectedSymbol(symbols[0]);
    return symbols;
  });
  const symbolList = useMemo(() => {
    return defaultSymbolList.map((item: any) => ({
      value: item,
      label: item,
    }));
  }, [defaultSymbolList]);
  const dateList = useDateList();
  const { data: liquidationMapData = [], loading } = useRequest(
    async () => {
      if (!selectedSymbol) return [];
      const res = await getCoinsLiquidationMap({
        symbol: selectedSymbol,
        interval: selectedDate,
      });
      return res;
    },
    {
      ready: !!selectedSymbol && !!selectedDate,
      refreshDeps: [selectedSymbol, selectedDate],
    },
  );
  return (
    <BlockWrap>
      <HStack className="gap-2 items-center justify-between">
        <Text>
          {selectedSymbol} {t('crypto_stat.liquidation_map.coins_liquidation_map_desc')}
        </Text>
        <HStack className="gap-2">
          <SelectUI list={symbolList} value={selectedSymbol} onChange={(value) => setSelectedSymbol(value)} />
          <SelectUI list={dateList} value={selectedDate} onChange={(value) => setSelectedDate(value)} />
        </HStack>
      </HStack>
      <div className="w-full h-full flex-1">
        <CoinsLiquidationGraph data={liquidationMapData} loading={symbolLoading || loading} />
      </div>
    </BlockWrap>
  );
}
function HyperLiquidationMap() {
  const { t } = useTranslation();

  const [selectedSymbol, setSelectedSymbol] = useState<HyperLiqMapParam['symbol'] | null>(null);
  const { data: defaultSymbolList = [], loading: symbolLoading } = useRequest(async () => {
    const res = await getHyperLiquidationSymbols();
    const { symbols = [] } = res;
    setSelectedSymbol(symbols[0]);
    return symbols;
  });
  const symbolList = useMemo(() => {
    return defaultSymbolList.map((item: any) => ({
      value: item,
      label: item,
    }));
  }, [defaultSymbolList]);
  const { data: liquidationMapData = [], loading } = useRequest(
    async () => {
      if (!selectedSymbol) return [];
      const res = await getHyperLiquidationMap({
        symbol: selectedSymbol,
      });
      return res;
    },
    {
      ready: !!selectedSymbol,
      refreshDeps: [selectedSymbol],
    },
  );
  return (
    <BlockWrap>
      <HStack className="gap-2 items-center justify-between">
        <Text>{t('crypto_stat.liquidation_map.hyperliquid_map_desc')}</Text>
        <SelectUI list={symbolList} value={selectedSymbol} onChange={(value) => setSelectedSymbol(value)} />
      </HStack>
      <div className="w-full h-full flex-1">
        {!!selectedSymbol && (
          <HyperLiquidationGraph data={liquidationMapData} loading={symbolLoading || loading} symbol={selectedSymbol} />
        )}
      </div>
    </BlockWrap>
  );
}

export default function LiquidationMap() {
  const { t } = useTranslation();
  return (
    <VStack className="gap-[48px]">
      <BTCMap />
      <CoinsLiquidationMap />
      <HyperLiquidationMap />
    </VStack>
  );
}
