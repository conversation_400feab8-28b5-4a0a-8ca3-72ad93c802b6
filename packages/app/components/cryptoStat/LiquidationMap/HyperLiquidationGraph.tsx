'use client';
import React, { useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON>, LineChart } from 'echarts/charts';
import { TooltipComponent, LegendComponent, GridComponent, DataZoomComponent, MarkLineComponent, MarkPointComponent } from 'echarts/components';
import * as echarts from 'echarts/core';
import { SVGRenderer } from '@wuba/react-native-echarts';
import { useTranslation } from 'react-i18next';
import { averageNumber } from '../../../uitls/number';
import type { HyperLiqMapParam } from '../../../services/crypto_stat/crypto_stat.types';

echarts.use([Bar<PERSON><PERSON>, LineChart, DataZoomComponent, TooltipComponent, LegendComponent, GridComponent, SVGRenderer, MarkLineComponent, MarkPointComponent]);

interface HyperLiquidationGraphProps {
  data: any[];
  loading?: boolean;
  symbol: HyperLiqMapParam['symbol'] ;
}

export default function HyperLiquidationGraph({ data, loading = false, symbol }: HyperLiquidationGraphProps) {
  const { t } = useTranslation();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);

  // 数据处理
  const { xData, barLongPos, barShortPos, longLine, shortLine, currentPrice } = React.useMemo(() => {
    if (!data?.length) {
      return { xData: [], barLongPos: [], barShortPos: [], longLine: [], shortLine: [], currentPrice: null };
    }
    const xData = data.map((item: any) => item.price);
    const barLongPos = data.map((item: any) => item.long_pos || null);
    const barShortPos = data.map((item: any) => item.short_pos || null);
    const longLine = data.map((item: any) => item.long_accumulate_liq_usd > 0 ? item.long_accumulate_liq_usd : null);
    const shortLine = data.map((item: any) => item.short_accumulate_liq_usd > 0 ? item.short_accumulate_liq_usd : null);

    // 找到当前价格点（所有数据都为空但price有值的点）
    const currentPrice = data.find((item: any) => {
      return item.price && 
             !item.long_pos && 
             !item.short_pos && 
             !item.long_accumulate_liq_usd && 
             !item.short_accumulate_liq_usd
    })?.price || null;

    return { xData, barLongPos, barShortPos, longLine, shortLine, currentPrice };
  }, [data]);

  // 静态配置
  const staticOption = React.useMemo(() => ({
    backgroundColor: '#ffffff',
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params?.[0]?.axisValue || ''}<br/>`;
        params.forEach((param: any) => {
          if (
            param.value !== null &&
            param.value !== undefined &&
            param.value !== '' &&
            param.value !== '-' &&
            !Number.isNaN(param.value)
          ) {
            result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ${averageNumber(Math.abs(param.value))} ${symbol}<br/>`;
          }
        });
        return result;
      },
    },
    legend: {
      data: [
        t('crypto_stat.liquidation_map.long_pos'),
        t('crypto_stat.liquidation_map.short_pos'),
        t('crypto_stat.liquidation_map.long_accumulate'),
        t('crypto_stat.liquidation_map.short_accumulate'),
      ],
      top: 20,
    },
    grid: {
      left: 50,
      right: 70,
      top: 70,
      bottom: 90,
    },
    xAxis: {
      type: 'category',
      axisLine: { lineStyle: { color: '#444', type: 'dashed' } },
      data: xData,
      name: '',
      nameLocation: 'center',
      nameGap: 30,
    },
    yAxis: [
      {
        type: 'value',
        position: 'left',
        axisLabel: {
          formatter: (v: number) => averageNumber(v),
        },
        splitLine: { lineStyle: { color: '#444', type: 'dashed' } },
      },
      {
        type: 'value',
        position: 'right',
        axisLabel: {
          formatter: (v: number) => averageNumber(v),
        },
        splitLine: { show: false },
      },
    ],
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: 0,
        bottom: 30,
        height: 20,
        start: 0,
        end: 100,
        labelFormatter: (value: number) => xData[Math.floor(value)] || '',
      },
    ],
    series: [
      {
        name: t('crypto_stat.liquidation_map.long_pos'),
        type: 'bar',
        itemStyle: { color: '#EF4444' },
        data: barLongPos,
        barGap: 0,
      },
      {
        name: t('crypto_stat.liquidation_map.short_pos'),
        type: 'bar',
        itemStyle: { color: '#10B981' },
        data: barShortPos,
      },
      {
        name: t('crypto_stat.liquidation_map.long_accumulate'),
        type: 'line',
        yAxisIndex: 1,
        itemStyle: { color: '#EF4444' },
        data: longLine,
        symbol: 'none',
        lineStyle: { width: 2 },
      },
      {
        name: t('crypto_stat.liquidation_map.short_accumulate'),
        type: 'line',
        yAxisIndex: 1,
        itemStyle: { color: '#10B981' },
        data: shortLine,
        symbol: 'none',
        lineStyle: { width: 2 },
      },
      {
        name: t('crypto_stat.liquidation_map.last_price'),
        type: 'line',
        markLine: {
          silent: true,
          symbol: ['arrow', 'none'],
          symbolSize: [12, 0],
          lineStyle: {
            color: '#FF0000',
            type: 'dashed',
            width: 2,
          },
          label: {
            show: true,
            position: 'end',
            formatter: (params: any) => `${t('crypto_stat.liquidation_map.last_price')}: ${params.data.coord[0]}`,
            color: '#FF0000',
            fontSize: 12,
          },
          data: [
            [
              { coord: [String(currentPrice), 'min'], symbol: 'none', symbolSize: 0 },
              { coord: [String(currentPrice), 'max'], symbol: 'arrow', symbolSize: 12 }
            ]
          ]
        },
        data: []
      }
    ],
  }), [t, xData, barLongPos, barShortPos, longLine, shortLine, currentPrice]);

  useEffect(() => {
    if (!chartRef.current) return;

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        useDirtyRect: true,
      });
      chartInstance.current.setOption(staticOption);
    }

    if (loading) {
      chartInstance.current.setOption({
        xAxis: { data: [] },
        series: staticOption.series.map(() => ({ data: [] })),
      });
      chartInstance.current.showLoading({
        text: 'Loading...',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        textColor: '#000',
        spinnerRadius: 10,
        lineWidth: 5,
        showSpinner: true,
        showText: true,
        animation: true,
      });
    } else {
      chartInstance.current.hideLoading();
      if (xData.length) {
        chartInstance.current.setOption({
          xAxis: { data: xData },
          series: [
            { data: barLongPos },
            { data: barShortPos },
            { data: longLine },
            { data: shortLine },
            {
              markLine: {
                data: [
                  [
                    { coord: [String(currentPrice), 'min'], symbol: 'none', symbolSize: 0 },
                    { coord: [String(currentPrice), 'max'], symbol: 'arrow', symbolSize: 12 }
                  ]
                ]
              }
            }
          ],
        });
      }
    }

    const resize = () => chartInstance.current && chartInstance.current.resize();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', resize);
    }
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', resize);
      }
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [xData, barLongPos, barShortPos, longLine, shortLine, staticOption, loading]);

  return (
    <div
      ref={chartRef}
      style={{
        width: '100%',
        height: '100%',
        position: 'relative',
        overflow: 'hidden',
      }}
    />
  );
}
