'use client';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useRequest } from 'ahooks';
import { getFearGreedIndex } from '@quantum/app/services/crypto_stat/crypto_stat.api';
import type { FearGreedIndex } from '@quantum/app/services/crypto_stat/crypto_stat.types';
import FearGreedIndexGraph from './FearGreedIndexGraph';
import { Box } from '@quantum/components/ui/box';
import { VStack } from '@quantum/components/ui/vstack';
import CryptoStatPageFAQ from '../CryptoStatPageFAQ/CryptoStatPageFAQ';
import ResearchReportList from '../../ResearchReport/ResearchReportList';
import { HStack } from '@quantum/components/ui/hstack';
export default function FearGreedIndex({ defaultData }: { defaultData: FearGreedIndex | null }) {
  const { t } = useTranslation();
  const data = defaultData;

  return (
    <VStack className="h-[454px] gap-4 p-4 bg-gradient-to-b from-[rgba(5,198,151,0.012)] to-[rgba(5,198,151,0.03)] rounded-[12px] border-[1px] border-[#E6E6E6] border-solid">
      <FearGreedIndexGraph data={data} />
    </VStack>
  );
}
