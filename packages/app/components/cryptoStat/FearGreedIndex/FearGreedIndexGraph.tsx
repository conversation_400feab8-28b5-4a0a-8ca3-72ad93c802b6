'use client';
import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { LineChart } from 'echarts/charts';
import {
  DataZoomComponent,
  GridComponent,
  TooltipComponent,
  MarkAreaComponent,
  VisualMapComponent,
  LegendComponent,
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { SVGRenderer } from '@wuba/react-native-echarts';
import dayjs from 'dayjs';
import type { FearGreedIndex } from '@quantum/app/services/crypto_stat/crypto_stat.types';
import { useRequest } from 'ahooks';
import { getFearGreedIndex } from '@quantum/app/services/crypto_stat/crypto_stat.api';

echarts.use([
  LineChart,
  DataZoomComponent,
  GridComponent,
  TooltipComponent,
  MarkAreaComponent,
  VisualMapComponent,
  LegendComponent,
  SVGRenderer,
]);

interface FearGreedIndexGraphProps {
  data: FearGreedIndex | null;
  loading?: boolean;
}

export default function FearGreedIndexGraph({ data: defaultData, loading = false }: FearGreedIndexGraphProps) {
  const { t } = useTranslation();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);


  const {data = defaultData} = useRequest(async () => {
    const res = await getFearGreedIndex();
    return res;
  }, {
    ready: !defaultData,
  });

  // 数据处理
  const { xData, yData, btcData } = React.useMemo(() => {
    if (!data || !data.time_list?.length) {
      return { xData: [], yData: [], btcData: [] };
    }
    const xData = data.time_list.map((ts) => dayjs(ts).format('YYYY/MM/DD'));
    const yData = data.index_list;
    const btcData = (data.btc_price_list || []).slice(0, xData.length).map(Number);
    return { xData, yData, btcData };
  }, [data]);

  // 静态配置
  const staticOption = React.useMemo(() => ({
    legend: {
      data: [
        t('crypto_stat.fear_greed_index.fear_greed_index'),
        t('crypto_stat.fear_greed_index.btc'),
      ],
      top: 10,
      left: 'center',
      textStyle: {
        fontSize: 12,
      },
      itemWidth: 5,
      itemHeight: 5,
      icon: 'rect',
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const idx = params?.[0]?.dataIndex ?? 0;
        let html = `${params?.[0]?.axisValue || ''}<br/>`;
        params.forEach((p: any) => {
          if (p.seriesName === t('crypto_stat.fear_greed_index.btc')) {
            html += `<span style="color:#888">●</span> ${t('crypto_stat.fear_greed_index.btc')}: <b>${p.data}</b><br/>`;
          } else {
            html += `<span style="color:${p.color}">●</span> ${t('crypto_stat.fear_greed_index.fear_greed_index')}: <b>${p.data}</b><br/>`;
          }
        });
        return html;
      },
    },
    grid: { left: 50, right: 50, top: 50, bottom: 60 },
    xAxis: {
      type: 'category',
      axisLabel: { show: true },
      boundaryGap: false,
    },
    yAxis: [
      {
        type: 'value',
        min: 0,
        max: 100,
        splitNumber: 5,
        name: t('crypto_stat.fear_greed_index.fear_greed_index'),
        position: 'left',
      },
      {
        type: 'value',
        name: t('crypto_stat.fear_greed_index.btc'),
        position: 'right',
        axisLabel: {
          formatter: (v: number) => (v > 1000 ? `${(v / 1000).toFixed(1)}k` : v),
        },
        splitLine: { show: false },
      },
    ],
    visualMap: {
      top: 10,
      right: 0,
      pieces: [
        { gt: 0, lte: 20, color: '#2ecc40' },
        { gt: 20, lte: 40, color: '#ffeb3b' },
        { gt: 40, lte: 60, color: '#ff9800' },
        { gt: 60, lte: 80, color: '#ff5252' },
        { gt: 80, lte: 100, color: '#b71c1c' },
      ],
      outOfRange: { color: '#999' },
      show: false,
      dimension: 1,
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: 0,
        bottom: 10,
        height: 20,
        start: 95,
        end: 100,
        labelFormatter: (value: number) => {
          return dayjs(xData[Math.floor(value)]).format('MM/DD');
        }
      },
    ],
    series: [
      {
        name: t('crypto_stat.fear_greed_index.fear_greed_index'),
        type: 'line',
        showSymbol: false,
        connectNulls: true,
        yAxisIndex: 0,
        z: 2,
        markArea: {
          itemStyle: {
            borderWidth: 0,
          },
          data: [
            [
              { yAxis: 0, itemStyle: { color: 'rgba(46, 204, 64, 0.1)' } },
              { yAxis: 20 }
            ],
            [
              { yAxis: 80, itemStyle: { color: 'rgba(183, 28, 28, 0.1)' } },
              { yAxis: 100 }
            ]
          ]
        }
      },
      {
        name: t('crypto_stat.fear_greed_index.btc'),
        type: 'line',
        lineStyle: { color: '#888', width: 1 },
        itemStyle: { color: '#888' },
        showSymbol: false,
        connectNulls: true,
        yAxisIndex: 1,
        z: 1,
      },
    ],
  }), [t]);

  useEffect(() => {
    if (!chartRef.current) return;

    if (!chartInstance.current) {
      // 初始化
      chartInstance.current = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        useDirtyRect: true,
      });
      
      // 设置初始配置
      chartInstance.current.setOption(staticOption);
    }

    // 控制加载状态
    if (loading) {
      // 清空数据
      chartInstance.current.setOption({
        xAxis: {
          data: [],
        },
        series: [
          {
            data: [],
          },
          {
            data: [],
          },
        ],
      });
      chartInstance.current.showLoading({
        text: 'Loading...',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        textColor: '#000',
        spinnerRadius: 10,
        lineWidth: 5,
        showSpinner: true,
        showText: true,
        animation: true,
      });
    } else {
      chartInstance.current.hideLoading();
      // 只在有数据时更新图表数据
      if (xData.length) {
        chartInstance.current.setOption({
          xAxis: {
            data: xData,
          },
          series: [
            {
              data: yData,
            },
            {
              data: btcData,
            },
          ],
        });
      }
    }

    // 响应式
    const resize = () => chartInstance.current && chartInstance.current.resize();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', resize);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', resize);
      }
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [xData, yData, btcData, staticOption, loading, t]);

  if (!xData.length) return <div>{t('crypto_stat.common.no_data')}</div>;

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
} 