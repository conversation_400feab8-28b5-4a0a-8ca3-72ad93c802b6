import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { Box } from '@quantum/components/ui/box';
import FearGreed from './FearGreedIndex';
import CryptoStatPageFAQIndex from '../CryptoStatPageFAQ';
import ResearchReportIndex from '../../ResearchReport';
import type { FearGreedIndex as TypeFearGreedIndex } from '@quantum/app/services/crypto_stat/crypto_stat.types';
export default function FearGreedIndex({
  defaultData,
  symbol,
}: {
  defaultData: TypeFearGreedIndex | null;
  symbol: string;
}) {
  return (
    <HStack className="gap-6">
      <VStack className="gap-[48px] flex-1">
        <FearGreed defaultData={defaultData} />
        <CryptoStatPageFAQIndex symbol={symbol} />
      </VStack>
      <Box className="w-[377px]">
        <ResearchReportIndex />
      </Box>
    </HStack>
  );
}
