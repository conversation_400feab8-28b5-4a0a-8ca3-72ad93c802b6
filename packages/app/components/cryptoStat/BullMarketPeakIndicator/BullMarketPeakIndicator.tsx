'use client';
import {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableData,
} from '@quantum/components/ui/table';
import { Box } from '@quantum/components/ui/box';
import { getBullMarketPeakIndicator } from '../../../services/crypto_stat/crypto_stat.api';
import { RiArrowDownSFill, RiArrowUpSFill, RiCheckboxCircleLine, RiCloseCircleFill } from 'react-icons/ri';
import { useRequest } from 'ahooks';
import { ImArrowDown, ImArrowUp } from 'react-icons/im';
import { HStack } from '@quantum/components/ui/hstack';
import type { CommonResponse } from '../../../services/crypto_stat/crypto_stat.types';
import { useTranslation } from 'react-i18next';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import React from 'react';

export default function BullMarketPeakIndicator({
  defaultData,
}: {
  defaultData: CommonResponse['data'];
}) {
  const { t } = useTranslation();
  const data = defaultData;
  const hitNumber = data?.filter((item: any) => item.hit_status).length;
  const totalNumber = data?.length;
  const hitRate = (hitNumber / totalNumber) * 100;
  return (
    <VStack className="gap-6 flex-1">
      <VStack className="w-[160px] gap-[6px] bg-[#F7F7F7] rounded-1 py-2 px-3">
        <Text className="text-[#4D4D4D] text-[12px] font-[400] leading-[16px]">指标命中率:</Text>
        <Text className="text-[#0A0A0A] text-[12px] font-[500] leading-[16px]">
          {hitNumber}/{totalNumber}
        </Text>
      </VStack>
      <Box className="border-solid border-[1px] border-[#E6E6E6] w-full rounded-[12px] overflow-auto">
        <Table className="w-full">
          <TableHeader>
            <TableRow className="bg-[#05C69708]  border-0">
              <TableHead className="p-3 text-center text-[#4D4D4D] text-[14px] font-[400] leading-[20px]">#</TableHead>
              <TableHead className="p-3 text-[#4D4D4D] text-[14px] font-[400] leading-[20px]">
                {t('crypto_stat.bull_market_peak_indicator_table.indicator')}
              </TableHead>
              <TableHead className="p-3 text-center text-[#4D4D4D] text-[14px] font-[400] leading-[20px]">
                {t('crypto_stat.bull_market_peak_indicator_table.current_value')}
              </TableHead>
              <TableHead className="p-3 text-center text-[#4D4D4D] text-[14px] font-[400] leading-[20px]">
                {t('crypto_stat.bull_market_peak_indicator_table.reference_value')}
              </TableHead>
              <TableHead className="p-3 text-center text-[#4D4D4D] text-[14px] font-[400] leading-[20px]">
                {t('crypto_stat.bull_market_peak_indicator_table.hit_status')}
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data?.map((item: any, index: number) => {
              return (
                <TableRow
                  key={index}
                  className="h-[64px] text-[#0A0A0A] text-[14px] font-[600] leading-[20px] text-center even:bg-[#05C69708] border-0"
                >
                  <TableData className="p-3 text-center text-[#0A0A0A] text-[14px] font-[600] leading-[20px]">
                    {index + 1}
                  </TableData>
                  <TableData className="p-3 text-[#0A0A0A] text-[14px] font-[600] leading-[20px]">
                    {item.indicator_name}
                  </TableData>
                  <TableData className="p-3 text-center text-[#0A0A0A] text-[14px] font-[600] leading-[20px]">
                    <HStack className="gap-2 items-center justify-center">
                      {item.current_value}
                      {!!item.change_value && (
                        <>
                          {item.change_value.includes('-') ? (
                            <ImArrowDown className="text-red-500" />
                          ) : (
                            <ImArrowUp className="text-green-500" />
                          )}
                        </>
                      )}
                    </HStack>
                  </TableData>
                  <TableData className="p-3 text-center text-[#0A0A0A] text-[14px] font-[600] leading-[20px]">
                    {item.comparison_type}
                    {item.target_value}
                  </TableData>
                  <TableData className="p-3 text-center">
                    {item.hit_status ? (
                      <RiCheckboxCircleLine className="text-green-500 m-auto" />
                    ) : (
                      <RiCloseCircleFill className="text-red-500 m-auto" />
                    )}
                  </TableData>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </Box>
    </VStack>
  );
}
