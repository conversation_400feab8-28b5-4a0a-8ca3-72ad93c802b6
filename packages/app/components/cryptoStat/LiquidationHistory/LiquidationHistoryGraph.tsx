'use client';
import React, { useEffect, useRef } from 'react';
import { Bar<PERSON>hart } from 'echarts/charts';
import { TooltipComponent, LegendComponent, GridComponent, DataZoomComponent } from 'echarts/components';
import * as echarts from 'echarts/core';
import { SVGRenderer } from '@wuba/react-native-echarts';
import dayjs from 'dayjs';
import { averageNumber } from '../../../uitls/number';
import { useTranslation } from 'react-i18next';

echarts.use([BarChart, DataZoomComponent, TooltipComponent, LegendComponent, GridComponent, SVGRenderer]);

interface LiquidationHistoryGraphProps {
  data: any[];
  loading?: boolean;
}

export default function LiquidationHistoryGraph({ data, loading = false }: LiquidationHistoryGraphProps) {
  const { t } = useTranslation();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);

  // 静态配置
  const staticOption = React.useMemo(() => ({
    backgroundColor: '#ffffff',
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const idx = params?.[0]?.dataIndex ?? 0;
        let result = `${params?.[0]?.axisValue || ''}<br/>`;
        params.forEach((param: any) => {
          const value = param.value === null ? '-' : param.value;
          result += `<span style="color:${param.color}">●</span> ${param.seriesName}: $${averageNumber(Math.abs(value))}<br/>`;
        });
        return result;
      },
    },
    legend: {
      data: [
        t('crypto_stat.liquidation_history.short_liquidation'),
        t('crypto_stat.liquidation_history.long_liquidation'),
      ],
      top: 20,
    },
    grid: {
      left: 30,
      right: 70,
      top: 70,
      bottom: 90,
    },
    xAxis: {
      type: 'category',
      axisLine: { lineStyle: { color: '#444', type: 'dashed' } },
    },
    yAxis: {
      type: 'value',
      position: 'right',
      axisLabel: { 
        formatter: (v: number) => averageNumber(v),
      },
      splitLine: { lineStyle: { color: '#444', type: 'dashed' } },
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: 0,
        bottom: 30,
        height: 20,
        start: 90,
        end: 100,
        labelFormatter: (value: number) => {
          return dayjs(xData[Math.floor(value)]).format('MM/DD');
        }
      },
      
    ],
    series: [
      {
        name: t('crypto_stat.liquidation_history.long_liquidation'),
        type: 'bar',
        itemStyle: { color: '#40C382' },
        barGap: '-100%',
      },
      {
        name: t('crypto_stat.liquidation_history.short_liquidation'),
        type: 'bar',
        itemStyle: { color: '#F35F5F' },
      },
    ],
  }), [t]);

  // 数据处理
  const { xData, longLiqData, shortLiqData } = React.useMemo(() => {
    if (!data?.length) {
      return { xData: [], longLiqData: [], shortLiqData: [] };
    }
    const xData = data.map((item: any) => dayjs(item.time).format('YYYY/MM/DD'));
    const longLiqData = data.map((item: any) => item.long_liq_usd || 0);
    const shortLiqData = data.map((item: any) => -Math.abs(item.short_liq_usd || 0)); // 取负数
    return { xData, longLiqData, shortLiqData };
  }, [data]);

  useEffect(() => {
    if (!chartRef.current) return;

    if (!chartInstance.current) {
      // 初始化
      chartInstance.current = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        useDirtyRect: true,
      });
      
      // 设置初始配置
      chartInstance.current.setOption(staticOption);
    }

    // 控制加载状态
    if (loading) {
      // 清空数据
      chartInstance.current.setOption({
        xAxis: {
          data: [],
        },
        series: [
          {
            data: [],
          },
          {
            data: [],
          },
        ],
      });
      chartInstance.current.showLoading({
        text: 'Loading...',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        textColor: '#000',
        spinnerRadius: 10,
        lineWidth: 5,
        showSpinner: true,
        showText: true,
        animation: true,
      });
    } else {
      chartInstance.current.hideLoading();
      // 只在有数据时更新图表数据
      if (xData.length) {
        chartInstance.current.setOption({
          xAxis: {
            data: xData,
          },
          series: [
            {
              data: longLiqData,
            },
            {
              data: shortLiqData,
            },
          ],
        });
      }
    }

    // 响应式
    const resize = () => chartInstance.current && chartInstance.current.resize();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', resize);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', resize);
      }
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [xData, longLiqData, shortLiqData, staticOption, loading]);


  return (
    <div 
      ref={chartRef} 
      style={{ 
        width: '100%', 
        height: '100%',
        position: 'relative',
        overflow: 'hidden'
      }} 
    />
  );
} 