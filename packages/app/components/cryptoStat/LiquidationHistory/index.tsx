import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { Box } from '@quantum/components/ui/box';
import LiquidationHistory from './LiquidationHistory';
import CryptoStatPageFAQIndex from '../CryptoStatPageFAQ';
import ResearchReportIndex from '../../ResearchReport';

export default function LiquidationHistoryIndex({ marketList, defaultData, symbol }: { marketList: string[]; defaultData: any[]; symbol: string }) {
  return (
    <HStack className="gap-6">
      <VStack className="gap-[48px] flex-1">
        <LiquidationHistory marketList={marketList} defaultData={defaultData} />
        <CryptoStatPageFAQIndex symbol={symbol} />
      </VStack>
      <Box className="w-[377px]">
        <ResearchReportIndex />
      </Box>
    </HStack>
  );
}
