'use client';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { useRequest } from 'ahooks';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getCoinsLiquidationHistory } from '../../../services/crypto_stat/crypto_stat.api';
import SelectCryptoCoin, { defaultSymbolList as symbolList } from '../SelectCryptoCoin';
import LiquidationHistoryGraph from './LiquidationHistoryGraph';

export default function LiquidationHistory({
  marketList,
  defaultData,
}: {
  marketList: string[];
  defaultData: any[];
}) {
  const { t } = useTranslation();
  const [selected, setSelected] = useState(symbolList[0].value);

  const { data = defaultData, loading } = useRequest(
    async () => {
      const res = await getCoinsLiquidationHistory({
        exchange_list: marketList,
        symbol: selected,
        interval: '1d',
      });
      return res;
    },
    {
      refreshDeps: [selected],
    },
  );

  return (
    <VStack className="h-[454px] gap-4 p-4 bg-gradient-to-b from-[rgba(5,198,151,0.012)] to-[rgba(5,198,151,0.03)] rounded-[12px] border-[1px] border-[#E6E6E6] border-solid">
    <HStack>
      <SelectCryptoCoin value={selected} onChange={setSelected} />
    </HStack>
    <div className="w-full h-full flex-1">
      <LiquidationHistoryGraph data={data} loading={loading} />
    </div>
  </VStack>
  );
}
