import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { Box } from '@quantum/components/ui/box';
import GrayScalePositionList from './GrayScalePositionList';
import CryptoStatPageFAQIndex from '../CryptoStatPageFAQ';
import ResearchReportIndex from '../../ResearchReport';
import type { GrayscalePosition } from '../../../services/crypto_stat/crypto_stat.types';

export default function GrayScalePositionListIndex({
    defaultData,
    defaultSymbol,
    symbol,
  }: {
    defaultData: GrayscalePosition[];
    defaultSymbol: string;
    symbol: string;
  }) {
  return (
    <HStack className="gap-6">
      <VStack className="gap-[48px] flex-1">
        <GrayScalePositionList defaultData={defaultData} defaultSymbol={defaultSymbol} />
        <CryptoStatPageFAQIndex symbol={symbol} />
      </VStack>
      <Box className="w-[377px]">
        <ResearchReportIndex />
      </Box>
    </HStack>
  );;
}
