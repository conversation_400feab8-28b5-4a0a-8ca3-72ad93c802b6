'use client';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { useRequest } from 'ahooks';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getGrayscalePositionsHistory } from '../../../services/crypto_stat/crypto_stat.api';
import type { GrayscalePosition } from '../../../services/crypto_stat/crypto_stat.types';
import SelectCryptoCoin from '../SelectCryptoCoin';
import GrayScalePositionListGraph from './GrayScalePositionListGraph';

export default function GrayScalePositionList({
  defaultData,
  defaultSymbol,
}: {
  defaultData: GrayscalePosition[];
  defaultSymbol: string;
}) {
  const { t } = useTranslation();
  const [selected, setSelected] = useState(defaultSymbol);

  const { data = defaultData, loading } = useRequest(
    async () => {
      const res = await getGrayscalePositionsHistory({
        symbol: selected,
      });
      return res;
    },
    {
      refreshDeps: [selected],
    },
  );

  return (
    <VStack className="h-[454px] gap-4 p-4 bg-gradient-to-b from-[rgba(5,198,151,0.012)] to-[rgba(5,198,151,0.03)] rounded-[12px] border-[1px] border-[#E6E6E6] border-solid">
      <HStack>
        <SelectCryptoCoin value={selected} onChange={setSelected} useRemote={true} />
      </HStack>
      <div className="w-full h-full flex-1">
        <GrayScalePositionListGraph data={data} loading={loading} />
      </div>
    </VStack>
  );
}
