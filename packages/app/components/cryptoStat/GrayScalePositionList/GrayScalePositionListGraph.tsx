'use client';
import React, { useEffect, useRef, useMemo } from 'react';
import { <PERSON><PERSON>hart, Bar<PERSON>hart } from 'echarts/charts';
import { TooltipComponent, LegendComponent, GridComponent, DataZoomComponent } from 'echarts/components';
import * as echarts from 'echarts/core';
import { SVGRenderer } from '@wuba/react-native-echarts';
import { averageNumber } from '../../../uitls/number';
import type { GrayscalePosition } from '../../../services/crypto_stat/crypto_stat.types';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';

echarts.use([LineChart, BarChart, TooltipComponent, LegendComponent, GridComponent, DataZoomComponent, SVGRenderer]);

interface GrayScalePositionListGraphProps {
  data: GrayscalePosition[];
  loading?: boolean;
}

export default function GrayScalePositionListGraph({ data, loading = false }: GrayScalePositionListGraphProps) {
  const { t } = useTranslation();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  // 处理数据
  const xData = useMemo(() => {
    return data.map((item) => dayjs(item.date).format('YYYY/MM/DD'));
  }, [data]);

  const priceData = useMemo(() => {
    return data.map((item) => item.coin_price);
  }, [data]);

  const holdCountData = useMemo(() => {
    return data.map((item) => item.hold_count);
  }, [data]);

  // 静态配置
  const staticOption = useMemo(() => ({
    legend: {
      top: 10,
      left: 'center',
      textStyle: { fontSize: 12 },
      itemWidth: 8,
      itemHeight: 8,
      icon: 'rect',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      formatter: (params: any) => {
        const idx = params?.[0]?.dataIndex ?? 0;
        let html = `${params?.[0]?.axisValue || ''}<br/>`;
        params.forEach((p: any) => {
          if (p.data === null) return;
          let value = p.data;
          if (p.seriesName === 'BTC价格') {
            html += `<span style="color:${p.color}">●</span> ${p.seriesName}: <b>$${averageNumber(value)}</b><br/>`;
          } else {
            html += `<span style="color:${p.color}">●</span> ${p.seriesName}: <b>${averageNumber(value)}</b><br/>`;
          }
        });
        return html;
      },
    },
    grid: { left: 50, right: 50, top: 50, bottom: 60 },
    xAxis: {
      type: 'category',
      axisLabel: { show: true },
      boundaryGap: false,
    },
    yAxis: [
      {
        type: 'value',
        name: 'BTC价格',
        position: 'left',
        scale: true,
        min: 'dataMin',
        max: 'dataMax',
        minMargin: 0.05,
        maxMargin: 0.05,
        axisLabel: {
          formatter: (v: number) => `$${v > 1000 ? (v / 1000).toFixed(1) + 'k' : v}`,
        },
      },
      {
        type: 'value',
        name: '持币数量',
        position: 'right',
        scale: true,
        min: 'dataMin',
        max: 'dataMax',
        minMargin: 0.05,
        maxMargin: 0.05,
        axisLabel: {
          formatter: (v: number) => averageNumber(v),
        },
      },
    ],
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: 0,
        bottom: 10,
        height: 20,
        start: 0,
        end: 100,
        labelFormatter: (value: number) => {
          return dayjs(xData[Math.floor(value)]).format('MM/DD');
        }
      },
    ],
    series: [
      {
        name: 'BTC价格',
        type: 'line',
        yAxisIndex: 0,
        data: priceData,
        itemStyle: {
          color: '#FFB800',
        },
        lineStyle: {
          width: 2,
        },
        symbol: 'circle',
        symbolSize: 6,
      },
      {
        name: '持币数量',
        type: 'bar',
        yAxisIndex: 1,
        data: holdCountData,
        itemStyle: {
          color: '#5B8FF9',
        },
      },
    ],
  }), [xData, priceData, holdCountData, t]);

  useEffect(() => {
    if (!chartRef.current) return;

    if (!chartInstance.current) {
      // 初始化
      chartInstance.current = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        useDirtyRect: true,
      });
      
      // 设置初始配置
      chartInstance.current.setOption(staticOption);
    }

    // 控制加载状态
    if (loading) {
      // 清空数据
      chartInstance.current.setOption({
        xAxis: {
          data: [],
        },
        series: [
          {
            data: [],
          },
          {
            data: [],
          },
        ],
      });
      chartInstance.current.showLoading({
        text: 'Loading...',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        textColor: '#000',
        spinnerRadius: 10,
        lineWidth: 5,
        showSpinner: true,
        showText: true,
        animation: true,
      });
    } else {
      chartInstance.current.hideLoading();
      // 只在有数据时更新图表数据
      if (xData.length) {
        chartInstance.current.setOption({
          xAxis: {
            data: xData,
          },
          series: [
            {
              data: priceData,
            },
            {
              data: holdCountData,
            },
          ],
        });
      }
    }

    // 响应式
    const resize = () => chartInstance.current && chartInstance.current.resize();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', resize);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', resize);
      }
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [xData, priceData, holdCountData, staticOption, loading]);

  if (!xData.length) return <div>{t('crypto_stat.common.no_data')}</div>;

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
} 