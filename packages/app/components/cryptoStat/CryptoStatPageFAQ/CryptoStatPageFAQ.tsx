'use client';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import { useTranslation } from 'react-i18next';
import { useMemo } from 'react';
import { usePathnameHook } from '../../../hooks/usePathnameHook';
import { useCryptoStatMenu } from '../../../hooks/useCryptoStatMenu';

import Markdown from 'react-native-markdown-display';

function FAQItem({ title, content }: { title: string; content: string }) {
  return (
    <VStack className="gap-2">
      <h1 className="text-[#0A0A0A] text-[16px] font-600 leading-[20px]">{title}</h1>
      <Markdown
        style={{
          body: { color: '#808080', fontSize: 14, fontWeight: '400', margin: 0 },
          paragraph: {
            marginTop: 0,
            marginBottom: 0,
          },
        }}
      >
        {content}
      </Markdown>
    </VStack>
  );
}

export default function CryptoStatPageFAQ({ list = [] }: { list: any[] }) {
  const { t, i18n } = useTranslation();
  const titleKey = i18n.language === 'zh-CN' ? 'question_zh_cn' : 'question_en';
  const contentKey = i18n.language === 'zh-CN' ? 'answer_zh_cn' : 'answer_en';
  const pathname = usePathnameHook();
  const menu = useCryptoStatMenu();
  const currentRoute = useMemo(() => {
    return menu.find((item) => `/crypto-stat/${item.symbol}` === pathname);
  }, [pathname, menu]);

  if (list.length === 0) {
    return null;
  }
  return (
    <VStack className="gap-6">
      <h1>
        <Text className="text-[#0A0A0A] text-[20px] font-500 leading-[24px]">FAQ - {currentRoute?.label}</Text>
      </h1>

      {list.map((item, index) => (
        <FAQItem key={index} title={item[titleKey]} content={item[contentKey]} />
      ))}
    </VStack>
  );
}
