import CryptoStatPageFAQ from './CryptoStatPageFAQ';
import axios from 'axios';
async function getFAQList(symbol: string) {

  const isGray = process.env.SETUP_ENV === 'gray';

  const env = isGray ? 'gray' : process.env.APP_ENV;
  try {
    const res = await axios.get('http://qqtoa.ulegofi.com/api/frequently_asked_questions:list', {
      headers: {
        Authorization: `Bearer ${process.env.OA_API_KEY}`,
      },
      params: {
        pageSize: 1000,
        page: 1,
        filter: { $and: [{ enable_env: { $anyOf: [env] } }, { page_name: { $eq: symbol } }] },
      },
    });
   
    const list = res.data.data as {
      question_en: string;
      answer_en: string;
      page_name: string;
      question_zh_cn: string;
      answer_zh_cn: string;
    }[];
    return list;
  } catch (error) {
    console.error(error);
    return [];
  }
}
export default async function CryptoStatPageFAQIndex({ symbol }: { symbol: string }) {
  const list = await getFAQList(symbol);
  return <CryptoStatPageFAQ list={list} />;
}
