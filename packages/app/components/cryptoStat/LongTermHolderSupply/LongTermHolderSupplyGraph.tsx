'use client';
import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { LineChart } from 'echarts/charts';
import { DataZoomComponent, GridComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import * as echarts from 'echarts/core';
import { SVGRenderer } from '@wuba/react-native-echarts';
import dayjs from 'dayjs';
import type { LongTermHolderSupply } from '../../../services/crypto_stat/crypto_stat.types';
import { averageNumber } from '../../../uitls/number';

echarts.use([LineChart, DataZoomComponent, GridComponent, TooltipComponent, LegendComponent, SVGRenderer]);

interface LongTermHolderSupplyGraphProps {
  data: LongTermHolderSupply[] | null;
  loading?: boolean;
}

export default function LongTermHolderSupplyGraph({
  data: defaultData,
  loading = false,
}: LongTermHolderSupplyGraphProps) {
  const { t } = useTranslation();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);

  // 数据处理
  const { xData, priceData, holdData } = React.useMemo(() => {
    if (!defaultData?.length) {
      return { xData: [], priceData: [], holdData: [] };
    }
    const xData = defaultData.map((item: LongTermHolderSupply) => dayjs(item.date).format('YYYY/MM/DD'));
    const priceData = defaultData.map((item: LongTermHolderSupply) => Number(item.btc_price));
    const holdData = defaultData.map((item: LongTermHolderSupply) => Number(item.holders_hold_count));
    return { xData, priceData, holdData };
  }, [defaultData]);

  // 计算Y轴范围
  const { priceMin, priceMax, holdMin, holdMax } = React.useMemo(() => {
    if (!priceData.length || !holdData.length) {
      return {
        priceMin: 0,
        priceMax: 100,
        holdMin: 0,
        holdMax: 100,
      };
    }

    const priceMin = Math.min(...priceData);
    const priceMax = Math.max(...priceData);
    const holdMin = Math.min(...holdData);
    const holdMax = Math.max(...holdData);

    // 计算合适的范围，留出10%的边距
    const priceRange = priceMax - priceMin;
    const holdRange = holdMax - holdMin;

    return {
      priceMin: priceMin - priceRange * 0.1,
      priceMax: priceMax + priceRange * 0.1,
      holdMin: holdMin - holdRange * 0.1,
      holdMax: holdMax + holdRange * 0.1,
    };
  }, [priceData, holdData]);

  // 静态配置
  const staticOption = React.useMemo(
    () => ({
      legend: {
        data: [
          t('crypto_stat.long_term_holder_supply.btc_price'),
          t('crypto_stat.long_term_holder_supply.holder_count'),
        ],
        top: 10,
        left: 'center',
        textStyle: {
          fontSize: 12,
        },
        itemWidth: 5,
        itemHeight: 5,
        icon: 'rect',
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const idx = params?.[0]?.dataIndex ?? 0;
          let html = `${params?.[0]?.axisValue || ''}<br/>`;
          params.forEach((p: any) => {
            const value =
              p.seriesName === t('crypto_stat.long_term_holder_supply.btc_price')
                ? `$${averageNumber(p.data)}`
                : averageNumber(p.data);
            html += `<span style=\"color:${p.color}\">●</span> ${p.seriesName}: <b>${value}</b><br/>`;
          });
          return html;
        },
      },
      grid: { left: 50, right: 50, top: 50, bottom: 60 },
      xAxis: {
        type: 'category',
        axisLabel: { show: true },
        boundaryGap: false,
      },
      yAxis: [
        {
          type: 'log',
          name: t('crypto_stat.long_term_holder_supply.btc_price'),
          position: 'right',
          min: 1,
          interval: 1,
          logBase: 10,
          axisLabel: {
            formatter: (v: number) => `$${averageNumber(v)}`,
          },
        },
        {
          type: 'value',
          name: t('crypto_stat.long_term_holder_supply.holder_count'),
          position: 'left',
          axisLabel: {
            formatter: (v: number) => averageNumber(v),
          },
          splitLine: { show: false },
        },
      ],
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: 0,
          bottom: 10,
          height: 20,
          start: 0,
          end: 100,

          labelFormatter: (value: number) => {
            return dayjs(xData[Math.floor(value)]).format('MM/DD');
          }
        },
      ],
      series: [
        {
          name: t('crypto_stat.long_term_holder_supply.btc_price'),
          type: 'line',
          showSymbol: false,
          connectNulls: true,
          yAxisIndex: 0,
          z: 2,
          areaStyle: {
            opacity: 0.2,
          },
        },
        {
          name: t('crypto_stat.long_term_holder_supply.holder_count'),
          type: 'line',
          showSymbol: false,
          connectNulls: true,
          yAxisIndex: 1,
          z: 1,
        },
      ],
    }),
    [priceMin, priceMax, holdMin, holdMax, t],
  );


  useEffect(() => {
    if (!chartRef.current) return;

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        useDirtyRect: true,
      });

      chartInstance.current.setOption(staticOption);
    }

    // 控制加载状态
    if (loading) {
      // 清空数据
      chartInstance.current.setOption({
        xAxis: {
          data: [],
        },
        series: [
          {
            data: [],
          },
          {
            data: [],
          },
        ],
      });
      chartInstance.current.showLoading({
        text: 'Loading...',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        textColor: '#000',
        spinnerRadius: 10,
        lineWidth: 5,
        showSpinner: true,
        showText: true,
        animation: true,
      });
    } else {
      chartInstance.current.hideLoading();
      // 只在有数据时更新图表数据
      if (xData.length) {
        chartInstance.current.setOption({
          xAxis: {
            data: xData,
          },
          series: [
            {
              data: priceData,
            },
            {
              data: holdData,
            },
          ],
        });
      }
    }

    const resize = () => chartInstance.current && chartInstance.current.resize();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', resize);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', resize);
      }
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [xData, priceData, holdData, staticOption, loading]);


  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
}
