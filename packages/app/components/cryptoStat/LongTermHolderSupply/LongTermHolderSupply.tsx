'use client';
import { useRequest } from 'ahooks';
import { getLongTermHolderSupply } from '../../../services/crypto_stat/crypto_stat.api';
import LongTermHolderSupplyGraph from './LongTermHolderSupplyGraph';
import type { LongTermHolderSupply } from '../../../services/crypto_stat/crypto_stat.types';
import { VStack } from '@quantum/components/ui/vstack';
import CryptoStatPageFAQ from '../CryptoStatPageFAQ/CryptoStatPageFAQ';
import ResearchReportList from '../../ResearchReport/ResearchReportList';
import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
interface LongTermHolderSupplyProps {
  defaultData?: LongTermHolderSupply[] | null;
}

export default function LongTermHolderSupply({ defaultData }: { defaultData: LongTermHolderSupply[] | null }) {
  const { data, loading } = useRequest(
    async () => {
      const res = await getLongTermHolderSupply();
      return res;
    },
    {
      ready: !defaultData,
    },
  );

  return (
    <VStack className="h-[454px] gap-4 p-4 bg-gradient-to-b from-[rgba(5,198,151,0.012)] to-[rgba(5,198,151,0.03)] rounded-[12px] border-[1px] border-[#E6E6E6] border-solid">
      <LongTermHolderSupplyGraph data={defaultData || data || null} loading={loading} />
    </VStack>
  );
}
