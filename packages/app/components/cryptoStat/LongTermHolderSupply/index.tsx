import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { Box } from '@quantum/components/ui/box';
import LongTermHolderSupply from './LongTermHolderSupply';
import CryptoStatPageFAQIndex from '../CryptoStatPageFAQ';
import ResearchReportIndex from '../../ResearchReport';
import type { LongTermHolderSupply as TypeLongTermHolderSupply } from '../../../services/crypto_stat/crypto_stat.types';

export default function LongTermHolderSupplyIndex({ defaultData, symbol }: { defaultData: TypeLongTermHolderSupply[] | null; symbol: string }) {
  return (
    <HStack className="gap-6">
      <VStack className="gap-[48px] flex-1">
        <LongTermHolderSupply defaultData={defaultData} />
        <CryptoStatPageFAQIndex symbol={symbol} />
      </VStack>
      <Box className="w-[377px]">
        <ResearchReportIndex />
      </Box>
    </HStack>
  );
}
