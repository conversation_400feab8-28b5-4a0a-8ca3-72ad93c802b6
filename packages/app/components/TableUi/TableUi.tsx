'use client';
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableData,
  TableFooter,
} from '@quantum/components/ui/table';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { HStack } from '@quantum/components/ui/hstack';
import Pagination from '../Pagination/Pagination';
import Empty from '../Empty/Empty';
import { useTranslation } from 'react-i18next';
import { Skeleton } from '@quantum/components/ui/skeleton';
import { Box } from '@quantum/components/ui/box';
import { RiArrowDownSFill, RiArrowUpSFill } from 'react-icons/ri';
import { Pressable } from '@quantum/components/ui/pressable';
import { useState, useMemo, useEffect, useCallback } from 'react';

export type TableColumn = {
  key: string;
  align?: 'left' | 'center' | 'right';
  title?: string | React.ReactNode | JSX.Element;
  render?: (value: any, row: any, index: number) => React.ReactNode | JSX.Element;
  sortKey?: string;
  width?: string;
  enablePress?: boolean;
};

function SortIcon({
  sortKey,
  selectedSortKey,
  sortValue,
  onSort,
}: {
  sortKey: string;
  selectedSortKey: string | null;
  sortValue?: 'asc' | 'desc' | null;
  onSort?: (sortKey: string, sortValue: 'asc' | 'desc' | null) => void;
}) {
  return (
    <Pressable
      onPress={() => {
        console.log('sortValue', sortValue, selectedSortKey, sortKey);
        if (selectedSortKey === sortKey) {
          if (sortValue === 'asc') {
            onSort?.(sortKey, 'desc');
          } else if (sortValue === 'desc') {
            onSort?.(sortKey, null);
          } else {
            onSort?.(sortKey, 'asc');
          }
        } else {
          onSort?.(sortKey, 'desc');
        }
      }}
    >
      <VStack>
        <HStack className="h-[10px]  mb-[-2px] items-center justify-center overflow-hidden">
          <RiArrowUpSFill
            size={16}
            className={selectedSortKey === sortKey && sortValue === 'asc' ? 'text-[#05C697]' : 'text-[#CCCCCC]'}
          />
        </HStack>
        <HStack className="h-[10px] mt-[-2px] items-center justify-center overflow-hidden">
          <RiArrowDownSFill
            size={16}
            className={selectedSortKey === sortKey && sortValue === 'desc' ? 'text-[#05C697]' : 'text-[#CCCCCC]'}
          />
        </HStack>
      </VStack>
    </Pressable>
  );
}

export default function TableUi({
  columns,
  data,
  pageConfig,
  loading,
  handleSort,
  headerClassName,
  thClassName,
  thTextClassName,
  tdClassName,
  tdTextClassName,
  sortValue,
  sortKey,
  onRowPress,
  tableClassName,
}: {
  columns: TableColumn[];
  data?:
    | {
        [key: string]: any;
      }[]
    | null;
  pageConfig?: {
    total?: number;
    pageSize: number;
    currentPage?: number;
    onChange?: (page: number) => void;
  };
  loading?: boolean;
  handleSort?: (sortKey: string, sortValue: 'asc' | 'desc' | null) => void;
  headerClassName?: string;
  thClassName?: string;
  thTextClassName?: string;
  tdClassName?: string;
  tdTextClassName?: string;
  sortValue?: 'asc' | 'desc' | null;
  sortKey?: string | null;
  onRowPress?: (row: any) => void;
  tableClassName?: string;
}) {
  const { t } = useTranslation();
  const [selectedSortKeyTable, setSelectedSortKeyTable] = useState<string | null>(null);
  const [sortValueTable, setSortValueTable] = useState<'asc' | 'desc' | null>(null);
  const [internalCurrentPage, setInternalCurrentPage] = useState<number>(1);
  const defaultPageSize = pageConfig?.pageSize || 10;

  const onSort = (sortKey: string, sortValue: 'asc' | 'desc' | null) => {
    setSelectedSortKeyTable(sortKey);
    setSortValueTable(sortValue);
    handleSort?.(sortKey, sortValue);
  };

  useEffect(() => {
    if (sortValue !== undefined) {
      setSortValueTable(sortValue || null);
    }
  }, [sortValue]);

  useEffect(() => {
    if (sortKey !== undefined) {
      setSelectedSortKeyTable(sortKey || null);
    }
  }, [sortKey]);

  const handleInternalPageChange = useCallback(
    (page: number) => {
      setInternalCurrentPage(page);
    },
    [setInternalCurrentPage],
  );
  const tablePageConfig = useMemo(() => {
    if (pageConfig) {
      // 如果只传入了部分 pageConfig，则合并默认值
      return {
        total: pageConfig.total ?? (data?.length || 0),
        pageSize: pageConfig.pageSize || 10,
        currentPage: pageConfig.currentPage ?? internalCurrentPage,
        onChange: pageConfig.onChange ?? handleInternalPageChange,
      };
    }
    return {
      total: data?.length || 0,
      pageSize: defaultPageSize,
      currentPage: internalCurrentPage,
      onChange: handleInternalPageChange,
    };
  }, [pageConfig, data, internalCurrentPage, handleInternalPageChange, defaultPageSize]);

  // 内部分页逻辑
  const paginatedData = useMemo(() => {
    if (!data) return [];
    const startIndex = (tablePageConfig.currentPage - 1) * tablePageConfig.pageSize;
    const endIndex = startIndex + tablePageConfig.pageSize;
    return data.slice(startIndex, endIndex);
  }, [data, tablePageConfig]);

  // 计算总页数
  const totalItems = data?.length || 0;
  const totalPages = Math.ceil(totalItems / defaultPageSize);

  return (
    <VStack className="gap-6 w-full pb-[20px]">
      <Table className={`w-full ${tableClassName}`}>
        <TableHeader className="bg-[#ffffff]">
          <TableRow className={`bg-[#05C69708] h-[44px] ${headerClassName}`}>
            {columns.map((column) => (
              <TableHead key={column.key} className={`${thClassName} ${column.width ? `w-[${column.width}]` : ''}`}>
                <HStack
                  className={`gap-1 items-center ${
                    column.align === 'center'
                      ? 'justify-center'
                      : column.align === 'right'
                        ? 'justify-end'
                        : 'justify-start'
                  }`}
                >
                  <Text className={`${thTextClassName}`}>{column.title}</Text>

                  {!!column.sortKey && (
                    <SortIcon
                      sortKey={column.sortKey}
                      selectedSortKey={selectedSortKeyTable || null}
                      sortValue={sortValueTable}
                      onSort={onSort}
                    />
                  )}
                </HStack>
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading && (
            <>
              {new Array(5).fill(undefined).map((_, index) => {
                return (
                  <TableRow
                    key={index}
                    className={`border-none h-[44px] px-[8px] py-[14px] ${
                      index % 2 === 0 ? 'bg-[#ffffff]' : 'bg-[#05C69708]'
                    }    `}
                  >
                    {columns.map((column) => {
                      return (
                        <TableData key={column.key} className={tdClassName}>
                          <Box>
                            <Skeleton className="w-full h-[20px]" />
                          </Box>
                        </TableData>
                      );
                    })}
                  </TableRow>
                );
              })}
            </>
          )}
          {!loading && paginatedData?.map((row, index) => (
            <TableRow
              key={row.name}
              className={`border-none h-[64px] px-[8px] py-[14px] ${
                index % 2 === 0 ? 'bg-[#ffffff]' : 'bg-[#05C69708]'
              }    `}
            >
              {columns.map((column) => {
                const value = row[column.key];
                const Content = (
                  <HStack
                    className={`${
                      column.align === 'center'
                        ? 'justify-center'
                        : column.align === 'right'
                          ? 'justify-end'
                          : 'justify-start'
                    } `}
                  >
                    {column.render ? (
                      column.render(value, row, (tablePageConfig.currentPage - 1) * tablePageConfig.pageSize + index)
                    ) : (
                      <Text
                        className={tdTextClassName ? tdTextClassName : 'text-[#4D4D4D] text-[14px] font-500 leading-[20px]'}
                      >
                        {value || '--'}
                      </Text>
                    )}
                  </HStack>
                );
                return (
                  <TableData
                    key={column.key}
                    className={`${
                      column.align === 'center' ? 'text-center' : column.align === 'right' ? 'text-right' : 'text-left'
                    } ${column.width ? `w-[${column.width}]` : ''}   ${tdClassName}  `}
                  >
                    {column.enablePress ? (
                      <Pressable
                        onPress={() => {
                          onRowPress?.(row);
                        }}
                      >
                        {Content}
                      </Pressable>
                    ) : (
                      <>{Content}</>
                    )}
                  </TableData>
                );
              })}
            </TableRow>
          ))}
        </TableBody>
      </Table>
      {!data?.length && !loading && <Empty content={t('empty')} className="mt-[100px] mb-[50px]" />}
      {!loading && tablePageConfig.total > tablePageConfig.pageSize && (
        <HStack className="justify-center">
          <Pagination
            total={tablePageConfig.total || 0}
            pageSize={tablePageConfig.pageSize || 0}
            currentPage={tablePageConfig.currentPage || 1}
            onChange={tablePageConfig.onChange}
          />
        </HStack>
      )}
    </VStack>
  );
}
