import React, { useState, useMemo } from 'react';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { Image } from '@quantum/components/ui/image';
import { StyleSheet } from 'react-native';
import { Skeleton } from '@quantum/components/ui/skeleton';

export interface TextOrImageLogoProps {
  /**
   * 图片URL
   */
  imageUrl?: string;
  /**
   * 文本内容，如果图片加载失败或未提供图片URL，将显示此文本的前两个字符
   */
  text: string;
  /**
   * 圆形Logo的尺寸
   */
  size?: number;
  /**
   * 自定义样式
   */
  className?: string;
}

// 马卡龙色系列表
const macaronColors = [
  '#FFD6DC', // 粉色
  '#D4F0F0', // 薄荷色
  '#FCE1A9', // 杏色
  '#E8F0FF', // 淡蓝色
  '#EBF5DF', // 淡绿色
  '#DECDFF', // 淡紫色
  '#FFE2CC', // 淡橘色
  '#F0F0F0', // 灰白色
];

/**
 * 根据文本生成背景色
 * @param text 文本内容
 * @returns 背景色
 */
const getBackgroundColorByText = (text: string): string => {
  // 使用文本的ASCII码总和来选择颜色
  const sum = text
    .split('')
    .reduce((acc, char) => acc + char.charCodeAt(0), 0);
  
  // 使用余数选择颜色，确保相同文本总是有相同颜色
  return macaronColors[sum % macaronColors.length];
};

/**
 * 获取文本的前两个字符
 * 如果文本包含多个空格分隔的单词，则取前两个单词的首字母
 * @param text 原始文本
 * @returns 处理后的显示文本
 */
const getDisplayText = (text: string): string => {
  // 检查是否包含空格（多个单词）
  if (text.includes(' ')) {
    // 按空格分割成单词数组
    const words = text.trim().split(/\s+/);
    // 取前两个单词的首字母
    let result = '';
    
    // 获取第一个单词首字母
    if (words.length >= 1 && words[0].length > 0) {
      result += words[0].charAt(0).toUpperCase();
    }
    
    // 获取第二个单词首字母
    if (words.length >= 2 && words[1].length > 0) {
      result += words[1].charAt(0).toUpperCase();
    }
    
    // 如果结果少于2个字符，则使用原始文本的前两个字符填充
    if (result.length < 2 && text.length >= 2) {
      result = result.padEnd(2, text.substring(result.length, 2));
    }
    
    return result;
  }
  
  // 原始逻辑：直接返回前两个字符
  return text.substring(0, 2);
};

const TextOrImageLogo: React.FC<TextOrImageLogoProps> = ({
  imageUrl,
  text,
  size = 40,
  className,
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  // 计算显示的文本和背景色
  const displayText = useMemo(() => getDisplayText(text), [text]);
  const backgroundColor = useMemo(() => getBackgroundColorByText(text), [text]);
  
  // 判断是否应该显示文本
  const shouldShowText = !imageUrl || !imageLoaded || imageError;
  
  // 判断是否应该显示骨架屏
  const shouldShowSkeleton = imageUrl && !imageLoaded && !imageError;
  
  // 创建动态样式
  const styles = StyleSheet.create({
    container: {
      width: size,
      height: size,
      borderRadius: size / 2,
      backgroundColor: shouldShowText ? backgroundColor : 'transparent',
    },
    image: {
      width: '100%',
      height: '100%',
    },
    text: {
      position: 'absolute',
      fontSize: size * 0.4,
      color: '#555555',
    }
  });
  
  return (
    <Box
      className={`items-center justify-center overflow-hidden ${className || ''}`}
      style={styles.container}
    >
      {shouldShowSkeleton && (
        <Skeleton 
          variant="circular"
          className="absolute inset-0"
          style={{ width: size, height: size }}
        />
      )}
      
      {imageUrl && !imageError ? (
        <Image
          source={{ uri: imageUrl }}
          style={styles.image}
          className={shouldShowText ? 'opacity-0' : 'opacity-100'}
          onLoad={() => setImageLoaded(true)}
          onError={() => setImageError(true)}
          alt={text}
        />
      ) : null}
      
      {shouldShowText && !shouldShowSkeleton && (
        <Text
          className="text-center font-medium"
          style={styles.text}
        >
          {displayText}
        </Text>
      )}
    </Box>
  );
};

export default TextOrImageLogo;
