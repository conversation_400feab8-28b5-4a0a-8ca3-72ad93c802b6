'use client';
import { HStack } from '@quantum/components/ui/hstack';
import ResearchReportDetail from './ResearchReportDetail';
import { VStack } from '@quantum/components/ui/vstack';
import { ShowContentReportCard } from './ResearchReportList';
import { useTranslation } from 'react-i18next';
import { Divider } from '@quantum/components/ui/divider';

export default function ResearchReportListPageCom({
  list = [],
}: {
  list: { date: string; content_zh_cn: string; content_en: string }[];
}) {
  const { t, i18n } = useTranslation();
  const contentKey = i18n.language === 'zh-CN' ? 'content_zh_cn' : 'content_en';
  return (
    <VStack className="gap-6 flex-1 w-full">
      {list.map((item) => {
        return <>
        <ShowContentReportCard key={item.date} date={item.date} content={item[contentKey]} />
        <Divider className="w-full h-[1px] bg-[#E6E6E6]" key={item.date} />
        </>;
      })}
    </VStack>
  );
}
