'use client';
import { Divider } from '@quantum/components/ui/divider';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import { RiArrowRightLine } from 'react-icons/ri';
import Link from '@unitools/link';
import { useTranslation } from 'react-i18next';

import Markdown from 'react-native-markdown-display';
const mdStyle = { color: '#808080', fontSize: 14, fontWeight: '400', lineHeight: '20px', margin: 0 };
export function ShowContentReportCard({ date, content }: { date: string; content: string }) {
  const { t } = useTranslation();
  return (
    <VStack className="gap-3">
      <Text className="text-[#0A0A0A] text-[16px] font-[600] leading-[20px]">{date} {t('crypto_stat.research-report.research-report')}</Text>
      <Box className='max-h-[60px] overflow-hidden'>
        <Markdown
          style={{
            body: mdStyle,
            heading1: mdStyle,
            heading2: mdStyle,
            heading3: mdStyle,
            heading4: mdStyle,
            heading5: mdStyle,
            heading6: mdStyle,
            paragraph: {
              marginTop: 0,
              marginBottom: 0,
            },
          }}
        >
          {content}
        </Markdown>
      </Box>
      <Link href={`/research-report/${date}`}>
        <HStack className="items-center gap-1">
          <Text className="text-[#0A0A0A]  text-[14px] font-500 leading-[20px]">
            {t('crypto_stat.research-report.view-original')}
          </Text>
          <RiArrowRightLine size={16} color="#09121F" />
        </HStack>
      </Link>
    </VStack>
  );
}

function ReportCard({ date, content }: { date: string; content: string }) {
  const { t } = useTranslation();
  return (
    <VStack className="gap-4">
      <Link href={`/research-report/${date}`}>
        <Text className="self-stretch text-[#0A0A0A] text-[16px] font-600 leading-[20px]">{date} {t('crypto_stat.research-report.research-report')}</Text>
      </Link>
      <Divider className="w-full h-[1px] bg-[#E6E6E6]" />
    </VStack>
  );
}

export default function ResearchReportList({
  list = [],
}: {
  list: {
    date: string;
    content_zh_cn: string;
    content_en: string;
  }[];
}) {
  const { t, i18n } = useTranslation();
  const contentKey = i18n.language === 'zh-CN' ? 'content_zh_cn' : 'content_en';
  return (
    <VStack className="p-4 gap-4 rounded-[12px] border-[1px] border-solid border-[#E6E6E6] bg-gradient-to-b from-[rgba(5,198,151,0.01)] to-[rgba(5,198,151,0.03)]">
      <Text className="h-[32px] text-[#0A0A0A] text-[24px] font-[700] leading-[32px]">
        {
          t('crypto_stat.research-report.daily-report')
        }
        </Text>
      <Divider className="w-full h-[1px] bg-[#E6E6E6]" />
      {!!list[0] && (
        <>
          <ShowContentReportCard date={list[0].date} content={list[0][contentKey]} key="ShowContentReportCard" />
          <Divider className="w-full h-[1px] bg-[#E6E6E6]" key="ShowContentReportCardDivider" />
        </>
      )}

      {list.slice(1).map((item, index) => (
        <ReportCard key={index} date={item.date} content={item[contentKey]} />
      ))}
      <Link href="/research-report">
        <HStack className="item-center justify-center">
          <Text className="text-[#0A0A0A] text-[14px] font-500 leading-[20px]">
            {t('crypto_stat.research-report.view-history-report')}
          </Text>
        </HStack>
      </Link>
    </VStack>
  );
}
