'use client';
import { Box } from '@quantum/components/ui/box';
import { VStack } from '@quantum/components/ui/vstack';
import ResearchReportIndex from '.';
import { HStack } from '@quantum/components/ui/hstack';
import { Text } from '@quantum/components/ui/text';
import Markdown from 'react-native-markdown-display';
import { useTranslation } from 'react-i18next';

export default function ResearchReportDetail({
  detail,
}: {
  detail: { date: string; content_zh_cn: string; content_en: string };
}) {
  const { t, i18n } = useTranslation();
  const contentKey = i18n.language === 'zh-CN' ? 'content_zh_cn' : 'content_en';
  return (
    <VStack className="gap-6 flex-1">
      <h1 className="text-[24px] font-[700] leading-[32px] text-[#0A0A0A]">
        <Text>{detail.date}</Text>
      </h1>
      <VStack className="flex-1">
        <Markdown
          style={{
            // 基础文本和容器样式
            body: {
              fontSize: 16,
              lineHeight: 24,
              color: '#333333',
              fontFamily: 'System',
            },
            paragraph: {
              marginTop: 8,
              marginBottom: 8,
            },
            
            // 标题样式
            heading1: {
              fontSize: 28,
              fontWeight: 'bold',
              color: '#111111',
              marginTop: 16,
              marginBottom: 8,
              lineHeight: 36,
            },
            heading2: {
              fontSize: 24,
              fontWeight: 'bold',
              color: '#222222',
              marginTop: 14,
              marginBottom: 6,
              lineHeight: 32,
            },
            heading3: {
              fontSize: 20,
              fontWeight: 'bold',
              color: '#333333',
              marginTop: 12,
              marginBottom: 4,
              lineHeight: 28,
            },
            heading4: {
              fontSize: 18,
              fontWeight: 'bold',
              color: '#444444',
              marginTop: 10,
              marginBottom: 4,
              lineHeight: 24,
            },
            heading5: {
              fontSize: 16,
              fontWeight: 'bold',
              color: '#555555',
              marginTop: 8,
              marginBottom: 4,
            },
            heading6: {
              fontSize: 14,
              fontWeight: 'bold',
              color: '#666666',
              marginTop: 8,
              marginBottom: 4,
              fontStyle: 'italic',
            },
            
            // 强调样式
            strong: {
              fontWeight: 'bold',
              color: '#222222',
            },
            em: {
              fontStyle: 'italic',
              color: '#333333',
            },
            s: { // 删除线
              textDecorationLine: 'line-through',
              color: '#777777',
            },
            
            // 引用块
            blockquote: {
              backgroundColor: '#F5F5F5',
              borderLeftWidth: 4,
              borderLeftColor: '#DDDDDD',
              paddingHorizontal: 16,
              paddingVertical: 6,
              marginVertical: 10,
              fontStyle: 'italic',
            },
            
            // 代码样式
            code_inline: {
              backgroundColor: '#F0F0F0',
              color: '#C7254E',
              fontFamily: 'monospace',
              borderRadius: 3,
              paddingHorizontal: 4,
              paddingVertical: 2,
              fontSize: 14,
            },
            code_block: {
              backgroundColor: '#F8F8F8',
              borderWidth: 1,
              borderColor: '#EEEEEE',
              borderRadius: 4,
              padding: 12,
              marginVertical: 12,
              fontFamily: 'monospace',
              fontSize: 14,
            },
            fence: {
              backgroundColor: '#F8F8F8',
              borderWidth: 1,
              borderColor: '#EEEEEE',
              borderRadius: 4,
              padding: 12,
              marginVertical: 12,
              fontFamily: 'monospace',
              fontSize: 14,
            },
            
            // 水平线
            hr: {
              backgroundColor: '#EEEEEE',
              height: 1,
              marginVertical: 16,
            },
            
            // 列表样式
            bullet_list: {
              marginVertical: 8,
            },
            ordered_list: {
              marginVertical: 8,
            },
            list_item: {
              marginVertical: 3,
              flexDirection: 'row',
              alignItems: 'flex-start',
            },
            // 列表图标和内容
            bullet_list_icon: {
              fontSize: 16,
              color: '#666666',
              marginRight: 8,
              lineHeight: 24,
            },
            bullet_list_content: {
              flex: 1,
            },
            ordered_list_icon: {
              fontSize: 14,
              color: '#666666',
              marginRight: 8,
              lineHeight: 24,
              fontWeight: 'bold',
            },
            ordered_list_content: {
              flex: 1,
            },
            
            // 链接样式
            link: {
              color: '#0066CC',
              textDecorationLine: 'underline',
            },
            blocklink: {
              color: '#0066CC',
              textDecorationLine: 'underline',
            },
            
            // 图片样式
            image: {
              marginVertical: 12,
              borderRadius: 4,
              resizeMode: 'contain',
              maxWidth: 700,
            },
            
            // 表格样式
            table: {
              borderWidth: 1,
              borderColor: '#DDDDDD',
              borderRadius: 4,
              marginVertical: 12,
              overflow: 'hidden',
            },
            thead: {
              backgroundColor: '#F5F5F5',
            },
            tbody: {
              backgroundColor: '#FFFFFF',
            },
            th: {
              padding: 10,
              borderWidth: 1,
              borderColor: '#DDDDDD',
              fontWeight: 'bold',
              color: '#333333',
            },
            tr: {
              borderBottomWidth: 1,
              borderColor: '#EEEEEE',
            },
            td: {
              padding: 8,
              borderWidth: 1,
              borderColor: '#DDDDDD',
            },
            
            // 其他样式
            text: {
              color: '#333333',
              marginVertical: 0,
            },
            textgroup: {
              color: '#333333',
              marginVertical: 0,
            },
            hardbreak: {
              height: 16,
            },
            softbreak: {
              height: 8,
            },
          }}
        >
          {detail[contentKey]}
        </Markdown>
      </VStack>
    </VStack>
  );
}
