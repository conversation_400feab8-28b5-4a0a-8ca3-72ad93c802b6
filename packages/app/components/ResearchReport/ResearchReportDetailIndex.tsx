import { Box } from '@quantum/components/ui/box';
import { VStack } from '@quantum/components/ui/vstack';
import ResearchReportIndex from '.';
import { HStack } from '@quantum/components/ui/hstack';
import { Text } from '@quantum/components/ui/text';
import Markdown from 'react-native-markdown-display';
import { useTranslation } from 'react-i18next';
import ResearchReportDetail from './ResearchReportDetail';

export default function ResearchReportDetailIndex({ detail }: {
    detail: { date: string; content_zh_cn: string; content_en: string }
}) {
  return (
    <HStack className="gap-6 flex-1 w-full">
      <ResearchReportDetail detail={detail} />
      <Box className="w-[377px]">
        <ResearchReportIndex />
      </Box>
    </HStack>
  );
}
