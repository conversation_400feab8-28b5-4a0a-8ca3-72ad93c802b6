import axios from 'axios';
import ResearchReportList from './ResearchReportList';


export async function getReportList({
  date,
  pageSize = 10
}: {
  date?: string,
  pageSize?: number
}) {
  try {
    const isGray = process.env.SETUP_ENV === 'gray';

    const env = isGray ? 'gray' : process.env.APP_ENV;
    console.log('env', env, isGray, process.env.SETUP_ENV);
    const res = await axios.get('http://qqtoa.ulegofi.com/api/research_report:list', {
      headers: {
        Authorization: `Bearer ${process.env.OA_API_KEY}`,
      },
      params: {
        'sort[]': '-date',
        pageSize,
        page: 1,
        filter: {
          $and: [!!date && { date: { $eq: date } }, { enable_env: { $anyOf: [env] } }].filter(Boolean),
        },
      },
    });
    const list = res.data.data as {
      content_zh_cn: string;
      content_en: string;
      date: string;
    }[];
    return list;
  } catch (error) {
    console.error(error);
    return [];
  }
}
export default async function ResearchReportIndex() {
  const reportList = await getReportList({});
  return <ResearchReportList list={reportList} />;
}
