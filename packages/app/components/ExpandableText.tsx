import { Pressable } from '@quantum/components/ui/pressable';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';

const ExpandableText = ({ text }: { text: string }) => {
  const [expanded, setExpanded] = useState(false);
  const [isClamped, setIsClamped] = useState(false);
  const textRef = useRef(null);
  const { t } = useTranslation();

  useEffect(() => {
    const element = textRef.current;
    if (element) {
      const { scrollHeight, clientHeight } = element;

      // 判断是否超出默认限制行数（5行高度）
      const lineHeight = parseFloat(getComputedStyle(element).lineHeight);
      const maxHeight = lineHeight * 5;

      setIsClamped(scrollHeight > maxHeight);
    }
  }, [text]);

  return (
    <VStack className="gap-2">
      <div
        ref={textRef}
        className={`overflow-hidden transition-all duration-500 ease-in-out ${
          expanded ? 'max-h-[2000px]' : 'line-clamp-5'
        }`}
      >
        {text}
      </div>

      {isClamped && (
        <Pressable onPress={(e) => {
          e.preventDefault();
          setExpanded(!expanded);
        }} className="hover:underline focus:outline-none">
          <Text className="text-[#1F69FF] font-Inter text-[14px] font-not-italic font-[500] leading-[20px]">
            {expanded ? t('collapse') : t('expand')}
          </Text>
        </Pressable>
      )}
    </VStack>
  );
};

export default ExpandableText;