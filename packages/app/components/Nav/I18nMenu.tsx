import { HStack } from '@quantum/components/ui/hstack';
import { Menu, MenuItem, MenuItemLabel } from '@quantum/components/ui/menu';
import { Pressable } from '@quantum/components/ui/pressable';
import { RiArrowDownSFill, RiGlobalLine } from 'react-icons/ri';
import { Text } from '@quantum/components/ui/text';
import { useTranslation } from 'react-i18next';
import { useMemo } from 'react';

// 定义语言配置类型
export interface LanguageConfig {
  label: string;
  value: string;
}

export default function I18nMenu() {
  const { i18n } = useTranslation();
  const { t } = useTranslation();
  const languages = i18n.languages;
  
  // 语言配置列表
  const languageConfig: LanguageConfig[] = useMemo(() => {
    return [
      {
        label: t('menu.nav.zh_cn'),
        value: 'zh-CN',
      },
      {
        label: t('menu.nav.en'),
        value: 'en',
      },
    ];
  }, [t]);
  
  // 获取当前语言配置
  const currentLanguage = useMemo(() => {
    return languageConfig.find(config => config.value === i18n.language) || languageConfig[0];
  }, [languageConfig, i18n.language]);
  
  // 生成菜单项
  const menuItems = useMemo(() => {
    return languageConfig.filter(config => 
      true
    );
  }, [languages]);
  
  // 切换语言
  const handleLanguageChange = (language: string) => {
    // 更改i18n语言
    i18n.changeLanguage(language);
    
    // 在Web环境中设置Locale cookie
    if (typeof document !== 'undefined') {
      // 设置cookie，有效期为1年
      document.cookie = `Locale=${language}; path=/; max-age=${60 * 60 * 24 * 365}; SameSite=Lax`;
    }
  };

  return (
    <Menu
      placement="bottom right"
      offset={10}
      trigger={({ ...triggerProps }) => {
        return (
          <Pressable {...triggerProps}>
            <HStack className="gap-1 items-center min-h-[36px]">
              <RiGlobalLine className="text-[#0a0a0a]" size={20} />
              <Text className="text-[#0A0A0A] font-Inter text-[16px] font-not-italic font-[500] leading-[20px]">
                {currentLanguage.label}
              </Text>
              <RiArrowDownSFill className="text-[#0a0a0a]" size={12} />
            </HStack>
          </Pressable>
        );
      }}
      selectionMode="single"
    >
      {menuItems.map(item => (
        <MenuItem 
          key={item.value} 
          textValue={item.label}
          onPress={() => handleLanguageChange(item.value)}
        >
          <MenuItemLabel >{item.label}</MenuItemLabel>
        </MenuItem>
      ))}
    </Menu>
  );
}
