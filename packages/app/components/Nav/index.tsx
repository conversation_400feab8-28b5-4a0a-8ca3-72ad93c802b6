import { But<PERSON>, ButtonText } from '@quantum/components/ui/button';
import { Divider } from '@quantum/components/ui/divider';
import { HStack } from '@quantum/components/ui/hstack';
import { Text } from '@quantum/components/ui/text';
import logoText from '@quantum/shared/assets/logo-text.svg';
import Logo from '@quantum/shared/assets/logo.svg';
import {
  Accordion,
  AccordionItem,
  AccordionHeader,
  AccordionTrigger,
  AccordionContent,
  AccordionTitleText,
  AccordionContentText,
  AccordionIcon,
} from '@quantum/components/ui/accordion';
import Link from '@unitools/link';

import Image from '@unitools/image';

import { Menu, MenuItem, MenuItemLabel } from '@quantum/components/ui/menu';
import { Pressable } from '@quantum/components/ui/pressable';
import { observer } from 'mobx-react-lite';
import { usePathname } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { authStore } from '../../store/auth.store';
import { userStore } from '../../store/user.store';
import ModalLogin from '../auth/ModalLogin';
import TextOrImageLogo from '../TextOrImageLogo';
import { Search } from './Search';
import { Box } from '@quantum/components/ui/box';
import I18nMenu, { type LanguageConfig } from './I18nMenu';
import useRouter from '@unitools/router';
import {
  RiArrowDownSLine,
  RiArrowRightSLine,
  RiCheckLine,
  RiCloseFill,
  RiMenuFill,
  RiSearch2Line,
} from 'react-icons/ri';
import { useMemo, useState } from 'react';
import {
  Drawer,
  DrawerBackdrop,
  DrawerContent,
  DrawerHeader,
  DrawerBody,
  DrawerFooter,
} from '@quantum/components/ui/drawer';
import { VStack } from '@quantum/components/ui/vstack';
import SearchDrawer from './SearchDrawer';
import { usePathnameHook } from '../../hooks/usePathnameHook';
import SelectUI from '../SelectUI/SelectUI';
import { ChevronDownIcon, ChevronUpIcon } from '@quantum/components/ui/icon';

function MobileNavComponent({
  isMenuOpen,
  setIsMenuOpen,
  handleSearchOpen,
}: {
  isMenuOpen: boolean;
  setIsMenuOpen: (isMenuOpen: boolean) => void;
  handleSearchOpen: () => void;
}) {
  return (
    <>
      <HStack className="h-[48px] py-[10px] px-[16px] w-full justify-between">
        <HStack className="gap-7 items-center flex-1 overflow-hidden">
          <Link href="/">
            <Image source={Logo} alt="logo" width={28} height={28} />
          </Link>
        </HStack>

        <HStack className="items-center gap-4 ">
          <Pressable onPress={handleSearchOpen}>
            <RiSearch2Line size={20} className="text-[#808080]" />
          </Pressable>
          {authStore.isLoggedIn && (
            <TextOrImageLogo
              text={userStore.currentUserInfo?.username || userStore.currentUserInfo?.email || ''}
              size={28}
            />
          )}
          {isMenuOpen ? (
            <>
              <Pressable onPress={() => setIsMenuOpen(false)}>
                <RiCloseFill size={20} className="text-[#808080]" />
              </Pressable>
            </>
          ) : (
            <Pressable onPress={() => setIsMenuOpen(true)}>
              <RiMenuFill size={20} className="text-[#808080]" />
            </Pressable>
          )}
        </HStack>
      </HStack>
    </>
  );
}

const MobileNav = observer(MobileNavComponent);

function NavBar() {
  const { t, i18n } = useTranslation();

  const list = useMemo(
    () => [
      {
        name: t('menu.nav.market_price'),
        key: 'market',
        link: '/market',
      },
      {
        name: t('menu.nav.exchange_rank'),
        key: 'exchange_rank',
        link: '/exchange/rank',
      },
      {
        name: t('menu.nav.news'),
        key: 'community',
        link: '/signal',
      },
      {
        name: t('menu.nav.crypto_stat'),
        key: 'crypto_stat',
        link: '/crypto-stat/fear-greed-index',
      },
      {
        name: t('menu.nav.features'),
        key: 'features',
        link: '/features',
        children: [
          {
            name: t('menu.nav.features'),
            key: 'features',
            link: '/features',
          },
          {
            name: t('menu.nav.portfolio-tracker'),
            key: 'portfolio-tracker',
            link: '/features/portfolio-tracker',
          },
          {
            name: t('menu.nav.trader-signal'),
            key: 'trader-signal',
            link: '/features/trader-signal',
          },
          {
            name: t('menu.nav.watchlists'),
            key: 'watchlists',
            link: '/features/watchlists',
          },
        ],
      },
      {
        name: t('menu.nav.portfolio'),
        key: 'portfolio',
        link: '/portfolio',
      },
      // {
      //   name: 'AI 分析',
      //   key: 'ai',
      //   link: '/ai',
      // },
      // {
      //   name: '资产管理',
      //   key: 'asset',
      //   link: '/asset',
      // },
    ],
    [t],
  );
  const pathname = usePathnameHook();

  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  // const { i18n } = useTranslation();
  const languages = i18n.languages;

  // 语言配置列表
  const languageConfig: LanguageConfig[] = useMemo(() => {
    return [
      {
        label: t('menu.nav.zh_cn'),
        value: 'zh-CN',
      },
      {
        label: t('menu.nav.en'),
        value: 'en',
      },
      {
        label: t('menu.nav.en'),
        value: 'en',
      },
    ];
  }, [t]);

  // 获取当前语言配置
  const currentLanguage = useMemo(() => {
    return languageConfig.find((config) => config.value === i18n.language) || languageConfig[0];
  }, [i18n.language, languageConfig]);

  const menuItems = useMemo(() => {
    return languageConfig.filter((config) => true);
  }, [languages]);
  // 切换语言
  const handleLanguageChange = (language: string) => {
    // 更改i18n语言
    i18n.changeLanguage(language);

    // 在Web环境中设置Locale cookie
    if (typeof document !== 'undefined') {
      // 设置cookie，有效期为1年
      document.cookie = `Locale=${language}; path=/; max-age=${60 * 60 * 24 * 365}; SameSite=Lax`;
    }
    setIsLanguageMenuOpen(false);
  };
  const [isLanguageMenuOpen, setIsLanguageMenuOpen] = useState(false);
  const openLanguageMenu = () => {
    setIsLanguageMenuOpen(true);
  };
  const handleSearchOpen = () => {
    setIsSearchOpen(true);
  };
  const router = useRouter();
  return (
    <Box className="bg-[#FFF]  border-b-[1px] border-b-solid border-b-[#F5F5F5]">
      <HStack className="h-[64px] justify-between py-4 px-6 hidden md:flex " space="md">
        <HStack className="gap-7 items-center flex-1 overflow-hidden">
          <Link href="/">
            <Image source={logoText} alt="logo" width={100} height={100} />
          </Link>
          <Divider orientation="vertical" className="h-5" />
          <HStack className="flex-1 items-center gap-10 text-[#808080] text-[16px] font-[500] leading-[20px] ">
            {list.map((item) => {
              if (item.children) {
                let hasSelected = false;
                item.children.forEach((child: any) => {
                  if (pathname === child.link) {
                    hasSelected = true;
                  }
                });
                return (
                  <Menu
                    placement="bottom left"
                    offset={5}
                    trigger={({ ...triggerProps }) => {
                      return (
                        <Pressable
                          {...triggerProps}
                          variant="link"
                          className="p-0"
                          onHoverIn={() => {
                            triggerProps.onPress();
                          }}
                          onPress={() => {}}
                        >
                          <HStack className="items-center gap-1 items-center">
                            <Text className={`${hasSelected ? 'text-[#0A0A0A] font-[700]' : ''}`}>{item.name}</Text>
                            <RiArrowDownSLine
                              size={16}
                              className={`${hasSelected ? 'text-[#0A0A0A] font-[700]' : ''}`}
                            />
                          </HStack>
                        </Pressable>
                      );
                    }}
                    className="p-[10px] max-h-[300px] overflow-y-auto rounded-[6px] border-[1px] border-solid border-[#E6E6E6] bg-[#FFF] shadow-[0px_8px_20px_0px_rgba(0,0,0,0.10)] focus:outline-none"
                    selectionMode="single"
                  >
                    {item.children.map((item: any) => (
                      <MenuItem
                        onPress={() => {
                          router.push(item.link);
                        }}
                        key={item.link}
                        textValue={item.link}
                        className="px-[8px] py-[10px] items-center gap-[16px] self-stretch rounded-[6px] bg-[#FFF] data-[hover=true]:bg-[rgba(0,173,107,0.05)]"
                      >
                        <HStack className="items-center justify-between w-full">
                          <Text
                            className={`text-[#4D4D4D] font-Poppins text-[14px] font-not-italic font-[400] leading-[20px] ${
                              pathname === item.link ? 'font-[500]' : ''
                            }`}
                          >
                            {item.name}
                          </Text>
                          {pathname === item.link && <RiCheckLine size={16} className="text-[#40C382]" />}
                        </HStack>
                      </MenuItem>
                    ))}
                  </Menu>
                );
              }
              return (
                <Link href={item.link} key={item.key}>
                  <Text className={`${pathname === item.link ? 'text-[#0A0A0A] font-[700]' : ''}`}>{item.name}</Text>
                </Link>
              );
            })}

            <Box className="hidden xl:block">
              <Search />
            </Box>
          </HStack>
        </HStack>

        <HStack className="items-center space-x-4 gap-5">
          {authStore.isLoggedIn ? (
            <>
              <Box className="hidden">
                {userStore.currentUserInfo?.username ||
                  (userStore.currentUserInfo?.email && (
                    <Text className="hidden">
                      {userStore.currentUserInfo?.username || userStore.currentUserInfo?.email || ''}
                    </Text>
                  ))}
              </Box>

              <Menu
                placement="bottom right"
                offset={10}
                trigger={({ ...triggerProps }) => {
                  return (
                    <Pressable {...triggerProps}>
                      <TextOrImageLogo
                        text={userStore.currentUserInfo?.username || userStore.currentUserInfo?.email || ''}
                        size={36}
                      />
                    </Pressable>
                  );
                }}
                selectionMode="single"
              >
                <MenuItem
                  key="logout"
                  textValue="logout"
                  onPress={() => {
                    userStore.logout(authStore.activeUserId);
                  }}
                >
                  <MenuItemLabel>{t('menu.nav.logout')}</MenuItemLabel>
                </MenuItem>
              </Menu>
            </>
          ) : (
            <>
              <Button
                className="px-4 py-2 rounded-full bg-[#05C697] data-[hover=true]:bg-[#05C697]"
                onPress={() => authStore.setShowLoginModal(true)}
              >
                <Text className="text-[#F5F5F5] font-Inter text-[14px] font-not-italic font-[600] leading-[20px]">
                  {t('menu.nav.login')}
                </Text>
              </Button>
            </>
          )}

          <I18nMenu />
        </HStack>
      </HStack>

      <Box className="md:hidden">
        <MobileNav isMenuOpen={isMenuOpen} setIsMenuOpen={setIsMenuOpen} handleSearchOpen={handleSearchOpen} />
      </Box>

      <Drawer
        isOpen={isMenuOpen}
        onClose={() => {
          setIsMenuOpen(false);
        }}
        anchor="top"
        className="fixed top-0 left-0 right-0 bottom-0"
      >
        <DrawerBackdrop className="bg-[#000000B2]" />
        <DrawerContent className="p-0 h-auto">
          <DrawerHeader className="w-full">
            <MobileNav isMenuOpen={isMenuOpen} setIsMenuOpen={setIsMenuOpen} handleSearchOpen={handleSearchOpen} />
          </DrawerHeader>
          <DrawerBody className="mt-[6px] mb-0">
            <VStack>
              {list.map((item) => {
                if (item.children) {
                  return (
                    <Accordion
                      variant="filled"
                      type="multiple"
                      isCollapsible={true}
                      isDisabled={false}
                      className="p-0 m-0"
                    >
                      <AccordionItem value="a">
                        <AccordionHeader>
                          <AccordionTrigger className="p-0 m-0">
                            {({ isExpanded }) => {
                              return (
                                <>
                                  <HStack className="items-center justify-between h-[48px] px-4 w-full">
                                    <Text
                                      className={`${
                                        pathname === item.link
                                          ? 'text-[#0A0A0A] font-[600]'
                                          : 'text-[#808080] font-[500]'
                                      }`}
                                    >
                                      {item.name}
                                    </Text>
                                    {isExpanded ? (
                                      <AccordionIcon as={ChevronUpIcon} className="ml-3 text-[#808080]" />
                                    ) : (
                                      <AccordionIcon as={ChevronDownIcon} className="ml-3 text-[#808080]" />
                                    )}
                                  </HStack>
                                </>
                              );
                            }}
                          </AccordionTrigger>
                        </AccordionHeader>
                        <AccordionContent>
                          {item.children.map((child: any) => (
                            <Pressable key={child.link} onPress={() => setIsMenuOpen(false)}>
                              <Link href={child.link} key={child.key}>
                                <HStack className="items-center justify-between h-[48px] px-4">
                                  <Text
                                    className={`${
                                      pathname === child.link
                                        ? 'text-[#0A0A0A] font-[600]'
                                        : 'text-[#808080] font-[500]'
                                    }`}
                                  >
                                    {child.name}
                                  </Text>
                                  {pathname === child.link && <RiCheckLine size={18} className="text-[#05C697]" />}
                                </HStack>
                              </Link>
                            </Pressable>
                          ))}
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  );
                }
                return (
                  <Pressable key={item.key} onPress={() => setIsMenuOpen(false)}>
                    <Link href={item.link} key={item.key}>
                      <HStack className="items-center justify-between h-[48px] px-4">
                        <Text
                          className={`${
                            pathname === item.link ? 'text-[#0A0A0A] font-[600]' : 'text-[#808080] font-[500]'
                          }`}
                        >
                          {item.name}
                        </Text>
                        {pathname === item.link && <RiCheckLine size={18} className="text-[#05C697]" />}
                      </HStack>
                    </Link>
                  </Pressable>
                );
              })}
              <HStack className="items-center justify-between h-[48px] px-4">
                <Text className=" text-[#808080] font-[Inter] text-[16px] font-not-italic font-[500] leading-[20px]">
                  {t('menu.language')}
                </Text>

                <Pressable onPress={() => openLanguageMenu()}>
                  <HStack className="gap-1">
                    <Text className="text-[#808080] text-right font-[Inter] text-[16px] font-not-italic font-[500] leading-[20px]">
                      {currentLanguage.label}
                    </Text>
                    <RiArrowRightSLine size={18} className="text-[#8F8F8F]" />
                  </HStack>
                </Pressable>
              </HStack>

              {isLanguageMenuOpen &&
                menuItems.map((item) => (
                  <Pressable key={item.value} onPress={() => handleLanguageChange(item.value)}>
                    <HStack className="px-8 py-6 justify-between items-center h-[48px]">
                      <Text className=" text-[#808080] font-[Inter] text-[16px] font-not-italic font-[500] leading-[20px]">
                        {item.label}
                      </Text>
                      {currentLanguage.value === item.value && <RiCheckLine size={18} className="text-[#05C697]" />}
                    </HStack>
                  </Pressable>
                ))}
            </VStack>
          </DrawerBody>
          <DrawerFooter className="px-4 py-6">
            {authStore.isLoggedIn ? (
              <>
                <Button
                  className="px-4 py-2 w-full h-[36px] rounded-full bg-[#05C697] data-[hover=true]:bg-[#05C697]"
                  onPress={() => {
                    userStore.logout(authStore.activeUserId);
                    setIsMenuOpen(false);
                  }}
                >
                  <Text className="text-[#F5F5F5] font-[Inter] text-[14px] font-not-italic font-[600] leading-[20px]">
                    {t('menu.nav.logout')}
                  </Text>
                </Button>
              </>
            ) : (
              <>
                <Button
                  className="px-4 py-2 w-full h-[36px] rounded-full bg-[#05C697] data-[hover=true]:bg-[#05C697]"
                  onPress={() => {
                    authStore.setShowLoginModal(true);
                    setIsMenuOpen(false);
                  }}
                >
                  <Text className="text-[#F5F5F5] font-[Inter] text-[14px] font-not-italic font-[600] leading-[20px]">
                    {t('menu.nav.login')}
                  </Text>
                </Button>
              </>
            )}
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
      <ModalLogin
        isOpen={authStore.showLoginModal}
        onClose={() => {
          authStore.closeLoginModal();
        }}
      />
      <SearchDrawer isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} />
    </Box>
  );
}
export default observer(NavBar);
