import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>ooter,
} from '@quantum/components/ui/drawer';
import { VStack } from '@quantum/components/ui/vstack';
import { HStack } from '@quantum/components/ui/hstack';
import { Text } from '@quantum/components/ui/text';
import { Pressable } from '@quantum/components/ui/pressable';
import { Link } from '@quantum/components/ui/link';
import { RiArrowRightSLine, RiCheckLine, RiCloseLine } from 'react-icons/ri';
import { useTranslation } from 'react-i18next';
import { authStore } from '../../store/auth.store';
import { userStore } from '../../store/user.store';
import { Input, InputField, InputSlot } from '@quantum/components/ui/input';
import { RiSearch2Line } from 'react-icons/ri';
import { useCallback, useMemo, useRef, useState } from 'react';
import { reportSearchEvent, searchAssets } from '../../services/asset/asset.api';
import { useRequest } from 'ahooks';
import { useAsyncStorage } from '../../hooks/useAsyncStorage';
import { type SearchResultItem } from '../../services/asset/asset.types';
import useRouter from '@unitools/router';
import { Divider } from '@quantum/components/ui/divider';
import { SearchItem, SearchSkeleton } from './Search';
import Empty from '../Empty/Empty';
export default function SearchDrawer({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const { t } = useTranslation();
  const [keyword, setKeyword] = useState('');
  const inputRef = useRef<any>(null);

  const {
    runAsync,
    data: results = [],
    mutate,
    loading,
  } = useRequest(
    async (keyword: string) => {
      const res = await searchAssets(keyword);
      return res;
    },
    {
      manual: true,
      debounceWait: 500,
    },
  );

  // 防抖处理搜索请求
  const handleSearch = useCallback(async (value: string) => {
    setKeyword(value);

    // 如果输入为空，清空搜索结果并关闭弹窗
    if (!value.trim()) {
      mutate([]);
      return;
    }

    runAsync(value.trim());
  }, []);

  const { value: historyList = [], setValue: setHistoryList } = useAsyncStorage<SearchResultItem[]>('historyList');

  const handleClose = () => {
    onClose();
  };
  const reset = () => {
    setKeyword('');
    mutate([]);
    handleClose();
  };

  const router = useRouter();
  function goPage(item: SearchResultItem) {
    router.push(`/detail/${item.market}/${item.symbol}`);
  }


  const showEmpty = useMemo(() => {
    return results.length === 0 && historyList.length === 0 && !loading;
  }, [keyword, results, historyList, loading]);

  // 抽象出处理点击搜索项的通用方法
  const handleItemSelect = (item: SearchResultItem, searchText: string = '') => {
    // 报告搜索事件
    reportSearchEvent({
      market: item.market,
      symbol: item.symbol,
      search_text: searchText,
    });

    // 根据market和symbol去重，并将新点击的项目放在最前面
    const filteredHistory = historyList.filter((hist) => !(hist.market === item.market && hist.symbol === item.symbol));
    setHistoryList([item, ...filteredHistory]);

    // 导航到详情页
    goPage(item);
    reset();
  };

  const handleResultClick = (result: SearchResultItem) => {
    handleItemSelect(result, keyword);
  };

  const handleHistoryClick = (item: SearchResultItem) => {
    handleItemSelect(item);
  };

  const handleClearHistory = () => {
    setHistoryList([]);
  };
  return (
    <Drawer isOpen={isOpen} onClose={onClose} anchor="bottom" className="fixed top-0 left-0 right-0 bottom-0">
      <DrawerBackdrop className="bg-[#000000B2]" />
      <DrawerContent className="p-4 max-h-[80vh] h-[764px] rounded-t-[12px]">
        <DrawerBody className="mt-[6px] mb-0">
          <VStack className="gap-[10px]">
            <Input className="flex-1 w-full px-[13px] h-[36px] py-[9px] items-center gap-[7px] rounded-[6px] bg-[#F7F7F7] border-none outline-none shadow-none data-[focus=true]:shadow-[none]">
              <InputSlot>
                <RiSearch2Line size={16} className="text-[#808080]" />
              </InputSlot>
              <InputField
                ref={inputRef}
                placeholder={t('menu.nav.search')}
                value={keyword}
                onChangeText={handleSearch}
              />
            </Input>

            <VStack className="gap-[8px]">
              {showEmpty ? (
                <Empty className="py-4" content={keyword.trim() ? t('menu.nav.no_result') : t('menu.nav.no_keyword')} />
              ) : (
                <>
                  <VStack className="w-full h-full gap-2">
                    {loading ? (
                      <VStack className="w-full h-full gap-2">
                        {new Array(5).fill(0).map((_, index) => (
                          <SearchSkeleton key={index} />
                        ))}
                      </VStack>
                    ) : (
                      <>
                        {results.length > 0 && (
                          <VStack className="w-full h-full gap-2">
                            {results.map((item) => (
                              <SearchItem key={item.market + item.symbol} item={item} onPress={handleResultClick} />
                            ))}
                          </VStack>
                        )}
                      </>
                    )}

                    {(loading || results.length > 0) && historyList.length > 0 && <Divider className="bg-[#F5F5F5]" />}
                    {historyList.length > 0 && (
                      <>
                        <VStack className="w-fullgap-2">
                          <HStack className="w-full h-full p-2 justify-between items-center">
                            <Text className="text-[#808080] font-[Poppins] text-[12px] font-not-italic font-[400] leading-[1]">
                              {t('menu.nav.search_history')}
                            </Text>
                            <Pressable onPress={handleClearHistory}>
                              <Text className="flex-[1_0_0] text-[#05C697] text-right font-[Poppins] text-[12px] font-not-italic font-[400] leading-[1]">
                                {t('menu.nav.clear_history')}
                              </Text>
                            </Pressable>
                          </HStack>
                          <VStack className="w-full">
                            {historyList?.map((item, index) => (
                              <Pressable
                                onPress={() => handleHistoryClick(item)}
                                key={`history-${item.market}-${item.symbol}`}
                              >
                                <HStack
                                  key={`${item.market}-${item.symbol}-${index}`}
                                  className="items-center justify-between h-[40px] px-[8px] py-[10px] hover:bg-[rgba(5,198,151,0.05)]"
                                >
                                  <Text
                                    className="flex-[1_0_0] text-[#0A0A0A] font-Poppins text-[14px] font-not-italic font-[400] leading-[20px]"
                                    isTruncated
                                    numberOfLines={1}
                                  >
                                    {item.name}
                                  </Text>
                                  <Pressable
                                    onPress={(e) => {
                                      e.stopPropagation();
                                      const newHistoryList = [...historyList];
                                      newHistoryList.splice(index, 1);
                                      setHistoryList(newHistoryList);
                                    }}
                                  >
                                    <RiCloseLine size={16} className="text-[#b3b3b3]" />
                                  </Pressable>
                                </HStack>
                              </Pressable>
                            ))}
                          </VStack>
                        </VStack>
                      </>
                    )}
                  </VStack>
                </>
              )}
            </VStack>
          </VStack>
        </DrawerBody>
      </DrawerContent>
    </Drawer>
  );
}
