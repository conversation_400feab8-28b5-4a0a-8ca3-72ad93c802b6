import { Box } from '@quantum/components/ui/box';
import { Divider } from '@quantum/components/ui/divider';
import { HStack } from '@quantum/components/ui/hstack';
import { Input, InputField, InputSlot } from '@quantum/components/ui/input';
import { Popover, PopoverBackdrop, PopoverBody, PopoverContent } from '@quantum/components/ui/popover';
import { Pressable } from '@quantum/components/ui/pressable';
import { Skeleton } from '@quantum/components/ui/skeleton';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import useRouter from '@unitools/router';
import { useRequest } from 'ahooks';
import { useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RiCloseLine, RiSearch2Line } from 'react-icons/ri';
import { marketList } from '../../consts';
import { useAsyncStorage } from '../../hooks/useAsyncStorage';
import { reportSearchEvent, searchAssets } from '../../services/asset/asset.api';
import { type SearchResultItem } from '../../services/asset/asset.types';
import ChangeValue from '../ChangeValue';
import Empty from '../Empty/Empty';
import TextOrImageLogo from '../TextOrImageLogo';
import { averageNumber } from '../../uitls/number';
import { Tooltip, TooltipContent, TooltipText } from '@quantum/components/ui/tooltip';

export function SearchSkeleton() {
  return (
    <HStack className="py-[10px] px-[8px] gap-6 justify-between items-center rounded-[6px] hover:bg-[rgba(5,198,151,0.05)]">
      <HStack className="gap-6 items-center">
        <HStack className="gap-2">
          <Skeleton variant="circular" className="w-[20px] h-[20px]" />
          <Skeleton className="w-[90px] h-[20px]" />
        </HStack>
        <Skeleton className="w-[30px] h-[20px]" />
        <Skeleton className="w-[50px] h-[20px]" />
      </HStack>

      <Skeleton className="w-[40px] h-[20px] rounded-[20px]" />
    </HStack>
  );
}

export function SearchItem({ item, onPress }: { item: SearchResultItem; onPress: (item: SearchResultItem) => void }) {
  const router = useRouter();
  const marketLabel = useMemo(() => {
    return marketList.find((i) => i.value === item.market)?.label;
  }, [item.market]);
  return (
    <Pressable
      onPress={() => {
        onPress(item);
      }}
    >
      <HStack className="py-[10px] px-[8px] gap-6 justify-between items-center rounded-[6px] hover:bg-[rgba(5,198,151,0.05)]">
        <HStack className="gap-6 items-center">
          <HStack className="gap-2">
            <TextOrImageLogo text={String(item.symbol)} size={20} />
            <Tooltip
              placement="top"
              trigger={(triggerProps) => {
                return (
                  <Text
                    {...triggerProps}
                    className="text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[600] leading-[20px] w-[100px] truncate line-clamp-1"
                  >
                    {item.name}
                  </Text>
                );
              }}
            >
              <TooltipContent>
                <TooltipText>{item.name}</TooltipText>
              </TooltipContent>
            </Tooltip>
          </HStack>
          <Text className="text-[#0A0A0A] font-Inter text-[14px] font-not-italic font-[600] leading-[20px]  w-[70px] truncate line-clamp-1">
            {averageNumber(item.price, 2)}
          </Text>
          <ChangeValue
            change={`${item.change_rate}%`}
            className="font-Inter text-[14px] font-not-italic font-[600] leading-[16px]"
          />
        </HStack>

        <Box className="flex px-[8px] py-[2px] justify-center items-center gap-[4px] rounded-[12px] bg-[#E6E6E6]">
          <Text className="text-[#0A0A0A] font-Poppins text-[12px] font-not-italic font-[500] leading-[16px]">
            {marketLabel}
          </Text>
        </Box>
      </HStack>
    </Pressable>
  );
}

export const Search = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [keyword, setKeyword] = useState('');
  const inputRef = useRef<any>(null);

  const {
    runAsync,
    data: results = [],
    mutate,
    loading,
  } = useRequest(
    async (keyword: string) => {
      const res = await searchAssets(keyword);
      return res;
    },
    {
      manual: true,
      debounceWait: 500,
    },
  );

  // 防抖处理搜索请求
  const handleSearch = useCallback(async (value: string) => {
    setKeyword(value);

    // 如果输入为空，清空搜索结果并关闭弹窗
    if (!value.trim()) {
      mutate([]);
      return;
    }

    runAsync(value.trim());
  }, []);

  const handleOpen = () => {
    setIsOpen(true);
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  const reset = () => {
    setKeyword('');
    mutate([]);
    handleClose();
  };

  const router = useRouter();
  function goPage(item: SearchResultItem) {
    router.push(`/detail/${item.market}/${item.symbol}`);
  }

  const { value: historyList = [], setValue: setHistoryList } = useAsyncStorage<SearchResultItem[]>('historyList');
  const showEmpty = useMemo(() => {
    return results.length === 0 && historyList.length === 0 && !loading;
  }, [keyword, results, historyList, loading]);

  // 抽象出处理点击搜索项的通用方法
  const handleItemSelect = (item: SearchResultItem, searchText: string = '') => {
    // 报告搜索事件
    reportSearchEvent({
      market: item.market,
      symbol: item.symbol,
      search_text: searchText,
    });

    // 根据market和symbol去重，并将新点击的项目放在最前面
    const filteredHistory = historyList.filter((hist) => !(hist.market === item.market && hist.symbol === item.symbol));
    setHistoryList([item, ...filteredHistory]);

    // 导航到详情页
    goPage(item);
    reset();
  };

  const handleResultClick = (result: SearchResultItem) => {
    handleItemSelect(result, keyword);
  };

  const handleHistoryClick = (item: SearchResultItem) => {
    handleItemSelect(item);
  };

  const handleClearHistory = () => {
    setHistoryList([]);
  };
  const { t } = useTranslation();

  return (
    <Popover
      isOpen={isOpen}
      onOpen={handleOpen}
      onClose={handleClose}
      placement="bottom left"
      initialFocusRef={inputRef}
      trigger={(triggerProps) => {
        return (
          <Pressable {...triggerProps} className="inline-block">
            <Input className="flex-1 max-w-[295px] px-[12px] py-[6px] items-center gap-[6px] rounded-[6px] bg-[#F7F7F7] border-none outline-none shadow-none data-[focus=true]:shadow-[none]">
              <InputSlot>
                <RiSearch2Line size={16} className="text-[#808080]" />
              </InputSlot>
              <InputField
                ref={inputRef}
                placeholder={t('menu.nav.search')}
                value={keyword}
                onChangeText={handleSearch}
              />
            </Input>
          </Pressable>
        );
      }}
    >
      <PopoverBackdrop />
      <PopoverContent className="w-[450px] max-h-[500px] overflow-auto p-[10px] rounded-[6px] border-[1px] border-solid border-[#E6E6E6] bg-[#FFF] shadow-[0px_8px_20px_0px_rgba(0,0,0,0.10)]">
        <PopoverBody>
          {showEmpty ? (
            <Empty className="py-4" content={keyword.trim() ? t('menu.nav.no_result') : t('menu.nav.no_keyword')} />
          ) : (
            <>
              <VStack className="w-full h-full gap-2">
                {loading ? (
                  <VStack className="w-full h-full gap-2">
                    {new Array(5).fill(0).map((_, index) => (
                      <SearchSkeleton key={index} />
                    ))}
                  </VStack>
                ) : (
                  <>
                    {results.length > 0 && (
                      <VStack className="w-full h-full gap-2">
                        {results.map((item) => (
                          <SearchItem key={item.market + item.symbol} item={item} onPress={handleResultClick} />
                        ))}
                      </VStack>
                    )}
                  </>
                )}

                {(loading || results.length > 0) && historyList.length > 0 && <Divider className="bg-[#F5F5F5]" />}
                {historyList.length > 0 && (
                  <>
                    <VStack className="w-full h-full gap-2">
                      <HStack className="w-full h-full p-2 justify-between items-center">
                        <Text className="self-stretch text-[#808080] font-Poppins text-[12px] font-not-italic font-[400] leading-[16px]">
                          {t('menu.nav.search_history')}
                        </Text>
                        <Pressable onPress={handleClearHistory}>
                          <Text className="flex-[1_0_0] text-[#05C697] text-right font-Poppins text-[12px] font-not-italic font-[400] leading-[16px]">
                            {t('menu.nav.clear_history')}
                          </Text>
                        </Pressable>
                      </HStack>
                      <VStack className="w-full h-full">
                        {historyList?.map((item, index) => (
                          <Pressable
                            onPress={() => handleHistoryClick(item)}
                            key={`history-${item.market}-${item.symbol}`}
                          >
                            <HStack
                              key={`${item.market}-${item.symbol}-${index}`}
                              className="items-center justify-between px-[8px] py-[10px] hover:bg-[rgba(5,198,151,0.05)]"
                            >
                              <Text
                                className="flex-[1_0_0] text-[#0A0A0A] font-Poppins text-[14px] font-not-italic font-[400] leading-[20px]"
                                isTruncated
                                numberOfLines={1}
                              >
                                {item.name}
                              </Text>
                              <Pressable
                                onPress={(e) => {
                                  e.stopPropagation();
                                  const newHistoryList = [...historyList];
                                  newHistoryList.splice(index, 1);
                                  setHistoryList(newHistoryList);
                                }}
                              >
                                <RiCloseLine size={16} className="text-[#b3b3b3]" />
                              </Pressable>
                            </HStack>
                          </Pressable>
                        ))}
                      </VStack>
                    </VStack>
                  </>
                )}
              </VStack>
            </>
          )}
        </PopoverBody>
      </PopoverContent>
    </Popover>
  );
};
