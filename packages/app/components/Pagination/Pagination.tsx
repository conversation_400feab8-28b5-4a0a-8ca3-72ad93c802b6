import { HStack } from '@quantum/components/ui/hstack';
import { Button, ButtonText } from '@quantum/components/ui/button';
import { Pressable } from '@quantum/components/ui/pressable';
import { Text } from '@quantum/components/ui/text';
import { Box } from '@quantum/components/ui/box';
import { RiArrowLeftSLine, RiArrowRightSLine } from 'react-icons/ri';
export default function Pagination({
  total,
  pageSize,
  currentPage,
  onChange,
}: {
  total: number;
  pageSize: number;
  currentPage: number;
  onChange: (page: number) => void;
}) {
  const totalPages = Math.ceil(total / pageSize);

  // 生成需要显示的页码数组
  const getPageNumbers = () => {
    const maxDisplayPages = 6; // 最大显示6个页码

    // 如果总页数小于等于maxDisplayPages，显示全部页码
    if (totalPages <= maxDisplayPages) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    const result = [];
    // 始终显示第1页
    result.push(1);

    // 当前页靠近首页
    if (currentPage <= 3) {
      result.push(2, 3, 4, '...', totalPages);
    } 
    // 当前页靠近尾页
    else if (currentPage >= totalPages - 2) {
      result.push('...', totalPages - 3, totalPages - 2, totalPages - 1, totalPages);
    } 
    // 当前页在中间位置
    else {
      result.push('...', currentPage - 1, currentPage, currentPage + 1, '...', totalPages);
    }

    return result;
  };

  const pageNumbers = getPageNumbers();

  return (
    <HStack className="gap-4 items-center">
      <Button
        className={`w-[32px] h-[32px] p-0 rounded-full items-center justify-center bg-[none] data-[hover=true]:bg-[#05C697] data-[disabled=true]:bg-[none]`}
        onPress={() => onChange(currentPage - 1)}
        isDisabled={currentPage === 1}
      >
        <ButtonText className="text-[#CCCCCC]">
          <RiArrowLeftSLine size={16} />
        </ButtonText>
      </Button>
      
      {pageNumbers.map((pageNumber, index) => (
        pageNumber === '...' ? (
          <Text key={`ellipsis-${index}`} className="text-[14px] leading-[20px] font-500 text-[#0A0A0A]">...</Text>
        ) : (
          <Button
            key={index}
            className={`w-[32px] h-[32px] p-0 rounded-full items-center justify-center bg-[none] data-[hover=true]:bg-[#05C697] ${
              currentPage === pageNumber ? 'bg-[#05C697]' : 'bg-[#ffffff]'
            }`}
            onPress={() => onChange(pageNumber as number)}
          >
            <ButtonText className={`text-[14px] leading-[20px] font-500 ${currentPage === pageNumber ? 'text-[#ffffff]' : 'text-[#0A0A0A]'}`}>
              {pageNumber}
            </ButtonText>
          </Button>
        )
      ))}
      
      <Button
        className={`w-[32px] h-[32px] p-0 rounded-full items-center justify-center bg-[none] data-[hover=true]:bg-[#05C697] data-[disabled=true]:bg-[none]`}
        onPress={() => onChange(currentPage + 1)}
        isDisabled={currentPage === totalPages}
      >
        <ButtonText className="text-[#CCCCCC]">
          <RiArrowRightSLine size={16} />
        </ButtonText>
      </Button>
    </HStack>
  );
}
