import React, { createContext, useContext, useState, useCallback } from 'react';
import type { ReactNode } from 'react';
import {
  Modal,
  ModalBackdrop,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>eader,
  ModalFooter,
} from '@quantum/components/ui/modal';
import { Button, ButtonText } from '@quantum/components/ui/button';
import { Text } from '@quantum/components/ui/text';
import { Box } from '@quantum/components/ui/box';
import { 
  AlertDialog,
  AlertDialogBackdrop,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter
} from '@quantum/components/ui/alert-dialog';

// 定义对话框类型
export type DialogType = 'alert' | 'confirm' | 'custom';

// 定义对话框选项
export interface DialogOptions {
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  renderContent?: (close: () => void) => ReactNode;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'full';
}

// 创建一个Context，存储dialog实例
interface DialogContextType {
  showDialog: (type: DialogType, options: DialogOptions) => void;
  hideDialog: () => void;
}

// 创建一个上下文，初始值为null
const DialogContext = createContext<DialogContextType | null>(null);

// 创建Provider组件
export function DialogProvider({ children }: { children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);
  const [dialogType, setDialogType] = useState<DialogType>('alert');
  const [options, setOptions] = useState<DialogOptions>({});

  // 显示对话框
  const showDialog = useCallback((type: DialogType, options: DialogOptions) => {
    setDialogType(type);
    setOptions(options);
    setIsOpen(true);
  }, []);

  // 隐藏对话框
  const hideDialog = useCallback(() => {
    setIsOpen(false);
  }, []);

  // 处理取消操作
  const handleCancel = useCallback(() => {
    if (options.onCancel) {
      options.onCancel();
    }
    hideDialog();
  }, [options, hideDialog]);

  // 处理确认操作
  const handleConfirm = useCallback(() => {
    if (options.onConfirm) {
      options.onConfirm();
    }
    hideDialog();
  }, [options, hideDialog]);

  // 渲染Alert对话框
  const renderAlert = () => (
    <AlertDialog isOpen={isOpen} onClose={hideDialog}>
      <AlertDialogBackdrop />
      <AlertDialogContent size={options.size || 'md'}>
        <AlertDialogHeader>
          <Text size="lg" bold>
            {options.title || '提示'}
          </Text>
        </AlertDialogHeader>
        <AlertDialogBody>
          <Text>{options.message || ''}</Text>
        </AlertDialogBody>
        <AlertDialogFooter>
          <Button onPress={handleConfirm} action="primary">
            <ButtonText>{options.confirmText || '确定'}</ButtonText>
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );

  // 渲染Confirm对话框
  const renderConfirm = () => (
    <AlertDialog isOpen={isOpen} onClose={hideDialog}>
      <AlertDialogBackdrop />
      <AlertDialogContent size={options.size || 'md'}>
        <AlertDialogHeader>
          <Text size="lg" bold>
            {options.title || '确认'}
          </Text>
        </AlertDialogHeader>
        <AlertDialogBody>
          <Text>{options.message || ''}</Text>
        </AlertDialogBody>
        <AlertDialogFooter>
          <Button onPress={handleCancel} action="secondary" variant="outline" className="mr-3">
            <ButtonText>{options.cancelText || '取消'}</ButtonText>
          </Button>
          <Button onPress={handleConfirm} action="primary">
            <ButtonText>{options.confirmText || '确定'}</ButtonText>
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );

  // 渲染自定义对话框
  const renderCustom = () => (
    <Modal isOpen={isOpen} onClose={hideDialog}>
      <ModalBackdrop />
      <ModalContent size={options.size || 'md'}>
        {options.title && (
          <ModalHeader>
            <Text size="lg" bold>
              {options.title}
            </Text>
            <ModalCloseButton />
          </ModalHeader>
        )}
        <ModalBody>
          {options.renderContent ? options.renderContent(hideDialog) : null}
        </ModalBody>
      </ModalContent>
    </Modal>
  );

  // 根据对话框类型渲染不同的组件
  const renderDialog = () => {
    switch (dialogType) {
      case 'alert':
        return renderAlert();
      case 'confirm':
        return renderConfirm();
      case 'custom':
        return renderCustom();
      default:
        return null;
    }
  };

  return (
    <DialogContext.Provider value={{ showDialog, hideDialog }}>
      {children}
      {renderDialog()}
    </DialogContext.Provider>
  );
}

// 创建一个自定义hook，用于在组件中访问dialog
export function useDialog() {
  const context = useContext(DialogContext);
  
  // 判断是否在Provider内使用此hook
  if (!context) {
    throw new Error('useDialog必须在DialogProvider内部使用');
  }
  
  return context;
}

// 示例组件
export default function DialogExample() {
  const { showDialog } = useDialog();
  
  const handleShowAlert = () => {
    showDialog('alert', {
      title: '提示',
      message: '这是一个提示对话框',
      confirmText: '知道了'
    });
  };

  const handleShowConfirm = () => {
    showDialog('confirm', {
      title: '确认操作',
      message: '您确定要执行此操作吗？',
      confirmText: '确定',
      cancelText: '取消',
      onConfirm: () => console.log('用户确认了操作'),
      onCancel: () => console.log('用户取消了操作')
    });
  };

  const handleShowCustom = () => {
    showDialog('custom', {
      title: '自定义对话框',
      renderContent: (close) => (
        <Box>
          <Text className="mb-4">这是一个自定义内容的对话框</Text>
          <Button onPress={close} className="mt-4">
            <ButtonText>关闭</ButtonText>
          </Button>
        </Box>
      )
    });
  };

  return (
    <Box>
      <Button onPress={handleShowAlert} className="mb-2">
        <ButtonText>显示Alert</ButtonText>
      </Button>
      
      <Button onPress={handleShowConfirm} className="mb-2">
        <ButtonText>显示Confirm</ButtonText>
      </Button>
      
      <Button onPress={handleShowCustom}>
        <ButtonText>显示自定义对话框</ButtonText>
      </Button>
    </Box>
  );
} 