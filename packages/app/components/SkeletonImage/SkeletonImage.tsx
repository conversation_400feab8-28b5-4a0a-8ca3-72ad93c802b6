import React, { useState } from 'react';
import type { ComponentProps } from 'react';
import { Skeleton } from '@quantum/components/ui/skeleton';
import { Image } from '@quantum/components/ui/image';
import { View } from '@quantum/components/ui/view';

export type SkeletonImageProps = ComponentProps<typeof Image> & {
  skeletonProps?: ComponentProps<typeof Skeleton>;
};

export const SkeletonImage = (props: SkeletonImageProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const { style, skeletonProps, ...restProps } = props;
  
  return (
    <View style={{ position: 'relative' }}>
      {isLoading && (
        <Skeleton 
          className="absolute inset-0 w-full h-full"
          {...skeletonProps}
        />
      )}
      <Image
        {...restProps}
        style={[
          style,
          isLoading ? { opacity: 0 } : { opacity: 1 }
        ]}
        onLoad={() => setIsLoading(false)}
        onError={() => setIsLoading(false)}
      />
    </View>
  );
};
