# SkeletonImage 组件

`SkeletonImage` 是一个增强版的图片组件，在图片加载过程中显示骨架屏占位符，提供更好的用户体验。

## 特性

- 继承 Image 组件的所有属性
- 在图片加载过程中自动显示骨架屏
- 支持自定义骨架屏样式
- 同时支持 Web 和 Native 平台

## 使用方法

```jsx
import { SkeletonImage } from '@quantum/app/components/SkeletonImage';

// 基本用法
<SkeletonImage 
  source={{ uri: 'https://example.com/image.jpg' }} 
  alt="示例图片" 
/>

// 自定义骨架屏
<SkeletonImage 
  source={{ uri: 'https://example.com/image.jpg' }} 
  alt="示例图片"
  skeletonProps={{
    variant: "rectangular",
    className: "rounded-md"
  }}
/>
```

## 属性

- 所有 `Image` 组件支持的属性
- `skeletonProps`: 骨架屏的自定义属性（可选）

## 注意事项

- 骨架屏默认样式为圆形 (`variant="circular"`)
- 图片加载完成或加载失败后，骨架屏会自动隐藏 