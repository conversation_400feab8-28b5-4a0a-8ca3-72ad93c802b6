'use client';
import dayjs from 'dayjs';
import { SV<PERSON>enderer } from '@wuba/react-native-echarts';
import { LineChart } from 'echarts/charts';
import {
  DataZoomComponent,
  DataZoomInsideComponent,
  DataZoomSliderComponent,
  GridComponent,
  LegendComponent,
  TooltipComponent,
  MarkLineComponent,
  MarkPointComponent,
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { useEffect, useMemo, useRef, useState } from 'react';
import type { KLineDataItem } from '../../services/kline/kline.types';
import { Skeleton } from '@quantum/components/ui/skeleton';
import { useTranslation } from 'react-i18next';
import { averageNumber } from '../../uitls/number';
echarts.use([
  SVGRenderer,
  LineChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  DataZoomInsideComponent,
  DataZoomSliderComponent,
  Mark<PERSON>ineComponent,
  MarkPointComponent,
]);

interface LineGraphProps {
  isInteractive?: boolean;
  data?: KLineDataItem[];
  loading?: boolean;
}

// 创建通用的时间格式化函数
const formatTime = (timestamp: number | string) => {
  try {
    // 确保时间戳是数字类型
    const numTimestamp = typeof timestamp === 'string' ? Number(timestamp) : timestamp;
    // 使用标准格式
    return dayjs(numTimestamp).format('YYYY-MM-DD HH:mm');
  } catch (e) {
    console.error('时间格式化错误:', e);
    return '时间数据无效';
  }
};

export default function LineGraph({
  isInteractive = true,
  data,
  loading = false,
}: LineGraphProps) {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  useEffect(() => {
    setIsLoading(loading);
  }, [loading]);

  const chartContainRef = useRef<HTMLDivElement>(null);
  const myChartRef = useRef<any>(null);
  const roRef = useRef<any>(null);
  // 使用ref存储缩放范围，而不是每次数据变化都重新计算
  const zoomRangeRef = useRef<{ start: number; end: number }>({ start: 0, end: 100 });

  // 将K线数据格式转换为内部使用的格式
  const formatData = useMemo(() => {
    // 使用传入的数据或生成模拟数据
    if (data && data.length > 0) {
      // 将K线数据转换为内部格式
      return data.map((item) => ({
        ts: Number(item[0]), // 开盘时间戳
        price: Number(item[4]), // 使用收盘价作为价格
      }));
    }
    return [];
  }, [data]);

  const dateList = useMemo(() => {
    return formatData.filter((item) => item.ts).map((item: any) => item.ts * 1000);
  }, [formatData]);

  const priceList = useMemo(() => {
    return formatData.filter((item) => item.ts).map((item: any) => Number(item.price || 0));
  }, [formatData]);

  const isSetZoomRange = useRef(false);
  // 首次有数据时计算默认缩放范围
  useEffect(() => {
    // 只有在dateList首次有数据且尚未设置过zoomRange时执行计算
    if (dateList.length > 0 && zoomRangeRef.current.start === 0 && zoomRangeRef.current.end === 100) {
      const totalPoints = dateList.length;
      const pointsToShow = 50;

      if (totalPoints <= pointsToShow) {
        // 数据点少于或等于50个，全部显示
        zoomRangeRef.current = { start: 0, end: 100 };
      } else {
        if (isSetZoomRange.current) return;
        // 计算显示最后50个点对应的百分比
        const startPercent = Math.max(0, ((totalPoints - pointsToShow) / totalPoints) * 100);
        zoomRangeRef.current = { start: startPercent, end: 100 };
      }
      isSetZoomRange.current = true;
    }
  }, [dateList]);

  // 仅在组件首次加载时初始化图表
  useEffect(() => {
    // 初始化图表实例和ResizeObserver
    function initChart() {
      if (!chartContainRef.current) return;

      // 如果已经存在图表实例，先销毁它
      if (myChartRef.current) {
        myChartRef.current.dispose();
      }

      myChartRef.current = echarts.init(chartContainRef.current, 'light', {
        renderer: 'svg',
        useDirtyRect: true, // 增加渲染性能
      });

      // 如果已存在ResizeObserver，先断开连接
      if (roRef.current) {
        roRef.current.disconnect();
      }

      // 创建新的ResizeObserver
      roRef.current = new ResizeObserver((entries) => {
        if (myChartRef.current) {
          myChartRef.current.resize();
        }
      });

      // 开始观察
      roRef.current.observe(chartContainRef.current);
    }

    initChart();

    // 组件卸载时清理资源
    return () => {
      // 清理时断开ResizeObserver连接
      if (roRef.current) {
        roRef.current.disconnect();
      }
      // 清理图表事件监听和实例
      if (myChartRef.current) {
        myChartRef.current.off('dataZoom');
        myChartRef.current.dispose();
        myChartRef.current = null;
      }
    };
  }, []); // 空依赖数组，只在首次渲染时执行

  // 只在数据或交互设置发生变化时更新图表选项
  useEffect(() => {
    // 如果图表实例不存在或没有数据，不执行更新
    if (!myChartRef.current || !priceList?.length) return;

    const isDown = priceList.length > 1 ? priceList[priceList.length - 1] - priceList[0] < 0 : false;
    const lineColor = isDown ? '#CC3D3D' : '#00B268';

    const lastPrice = priceList.length > 0 ? priceList[priceList.length - 1] : 0;

    const option = {
      // 根据交互状态设置是否响应交互
      silent: !isInteractive,
      hoverLayerThreshold: isInteractive ? Infinity : 0,

      animation: isInteractive, // 根据交互状态启用/禁用动画效果

      legend: {
        show: isInteractive, // 只在交互模式下显示图例
        top: 'bottom',
        data: ['Intention'],
      },

      // 添加全局axisPointer配置
      axisPointer: {
        link: [{ xAxisIndex: 'all' }], // 链接所有x轴的指示器
        snap: true, // 全局启用吸附功能
        lineStyle: {
          color: lineColor,
          width: 1,
        },
        label: {
          backgroundColor: lineColor,
        },
      },

      tooltip: {
        show: isInteractive, // 根据交互状态显示/隐藏tooltip
        trigger: 'axis',
        confine: true, // 确保tooltip不会超出图表区域
        position: function (
          point: [number, number],
          params: any,
          dom: HTMLElement,
          rect: { x: number; y: number; width: number; height: number },
          size: { contentSize: [number, number]; viewSize: [number, number] },
        ) {
          // 计算tooltip的位置，使其跟随鼠标但不溢出边界
          // size.contentSize是tooltip的大小，size.viewSize是图表的大小
          const [x, y] = point;

          // 水平方向位置计算
          // 当鼠标靠近右侧时，将tooltip向左偏移以避免溢出
          let tooltipX = x + 10; // 默认位置在鼠标右侧10px
          if (tooltipX + size.contentSize[0] > size.viewSize[0]) {
            // 如果会溢出右边界，改为显示在鼠标左侧
            tooltipX = x - size.contentSize[0] - 10;
          }

          // 垂直方向位置计算
          // 确保tooltip不会超出顶部或底部边界
          let tooltipY = y + 10; // 默认位置在鼠标下方10px
          if (tooltipY + size.contentSize[1] > size.viewSize[1]) {
            // 如果会溢出底部边界，改为显示在鼠标上方
            tooltipY = y - size.contentSize[1] - 10;
          }

          return [tooltipX, tooltipY];
        },
        formatter(params: any) {
          // 尝试从不同属性获取时间值
          let timestamp;
          if (params[0].axisValue) {
            timestamp = params[0].axisValue;
          } else if (params[0].name) {
            timestamp = params[0].name;
          } else if (dateList && dateList[params[0].dataIndex]) {
            timestamp = dateList[params[0].dataIndex];
          } else {
            // 如果都获取不到，使用当前时间作为后备
            timestamp = Date.now();
          }

          // 使用dayjs格式化时间
          let formattedDate = '';
          try {
            // 统一使用固定格式
            formattedDate = formatTime(timestamp);
          } catch (e) {
            console.error('日期格式化错误:', e);
            formattedDate = '时间数据无效';
          }

          // 获取价格值，确保显示的是当前点的实际价格
          const price =
            typeof params[0].value !== 'undefined'
              ? params[0].value
              : typeof params[0].data !== 'undefined'
                ? params[0].data
                : 0;

          // 获取对应的原始K线数据（如果存在）
          let tooltipContent = `
          <div style='font-weight: 400; font-family: "PingFang SC Regular";color: #0A0A0A; font-size: 14px; line-height: 20px;'>
            <div>${t('chart.time')}: ${formattedDate}</div>
            <div>${t('chart.price')}: ${averageNumber(price)}</div>
          </div>
          `;

          // 如果能找到原始K线数据，显示更多信息
          const dataIndex = params[0].dataIndex;
          if (data && data[dataIndex]) {
            const klineData = data[dataIndex];
            tooltipContent = `
            <div style='font-weight: 400; font-family: "PingFang SC Regular";color: #0A0A0A; font-size: 14px; line-height: 20px;'>
              <div>${t('chart.time')}: ${formattedDate}</div>
              <div>${t('chart.open')}: ${averageNumber(klineData[1])}</div>
              <div>${t('chart.high')}: ${averageNumber(klineData[2])}</div>
              <div>${t('chart.low')}: ${averageNumber(klineData[3])}</div>
              <div>${t('chart.close')}: ${averageNumber(klineData[4])}</div>
              <div>${t('chart.volume')}: ${averageNumber(klineData[5])}</div>
            </div>
            `;
          }

          return tooltipContent;
        },
        axisPointer: {
          type: 'cross',
          lineStyle: {
            color: lineColor,
            width: 1,
            type: 'solid',
          },
        },
      },

      xAxis: {
        show: false,
        boundaryGap: false,
        data: dateList,
        axisPointer: {
          show: isInteractive, // 根据交互状态显示/隐藏axisPointer
          // snap已在全局配置中设置，但为确保对x轴生效，再次明确指定
          snap: true,
          value: dateList[0], // 默认选中第一个数据点
          status: isInteractive ? 'show' : 'hide', // 明确指定状态
          handle: {
            show: isInteractive,
          },
          label: {
            show: isInteractive,
            formatter: function (params: { value: number }) {
              // 确保时间戳格式正确，与tooltip使用同样的处理方式
              return formatTime(params.value);
            },
            backgroundColor: lineColor,
          },
          triggerTooltip: isInteractive,
        },
        silent: !isInteractive, // 根据交互状态禁用坐标轴交互
      },

      yAxis: {
        show: false,
        type: 'value',
        scale: true,
        min: 'dataMin',
        max: 'dataMax',
        minMargin: 0.05,
        maxMargin: 0.05,
        splitLine: {
          show: true,
          lineStyle: {
            color: ['#1F1F1F'],
            width: 1,
            type: 'solid',
          },
        },
        silent: !isInteractive,
        axisPointer: {
          show: isInteractive,
          label: {
            show: isInteractive,
            formatter: function (params: { value: number }) {
              return averageNumber(params.value);
            },
            backgroundColor: lineColor,
          },
        },
      },

      grid: {
        top: 0,
        left: 0, // 增加左侧空间以容纳标签
        right: 0,
        bottom: 30,
      },

      series: [
        {
          type: 'line',
          smooth: true,
          stack: 'a',
          symbol: 'circle',
          symbolSize: priceList?.length > 1 ? 1 : 10,
          sampling: 'average',
          itemStyle: {
            color: lineColor,
          },
          emphasis: {
            // 添加hover状态的强调效果，只在isInteractive为true时生效
            disabled: !isInteractive,
            scale: isInteractive,
            itemStyle: {
              shadowColor: lineColor,
              shadowBlur: 5,
              borderColor: lineColor,
              borderWidth: 1,
            },
            symbolSize: 1, // hover时增大点的大小
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: isDown ? 'rgba(204, 61, 61, 1)' : 'rgba(0, 178, 104, 1)', // 顶部颜色，100%不透明度
              },
              {
                offset: 1,
                color: isDown ? 'rgba(204, 61, 61, 0)' : 'rgba(0, 178, 104, 0)', // 底部颜色，完全透明
              },
            ]),
          },
          data: priceList,
          cursor: isInteractive ? 'pointer' : 'default', // 根据交互状态设置鼠标样式
          silent: !isInteractive, // 根据交互状态禁用系列交互
          hoverAnimation: isInteractive, // 根据交互状态设置悬停动画
          
          // 添加当前收盘价格的横向标线
          markLine: isInteractive ? {
            silent: true,
            symbol: ['none', 'none'],
            lineStyle: {
              color: lineColor,
              type: 'dashed',
              width: 1,
            },
            emphasis: {
              disabled: false // 禁用高亮效果
            },
            label: {
              show: true,
              position: 'insideStart', // 改为显示在线的上方
              formatter: `${t('last_price')}: ${averageNumber(lastPrice)}`,
              backgroundColor: lineColor,
              color: '#fff',
              padding: [3, 6],
              borderRadius: 3,
              fontSize: 12,
              distance: 8, // 调整为合适的上方距离
              align: 'left' // 居中对齐
            },
            precision: 18,
            data: [
              {
                name: t('last_price'),
                type: 'value',
                valueDim: 'y',
                yAxis: lastPrice
              }
            ]
          } : undefined
        }
      ],
      dataZoom: isInteractive
        ? [
            {
              // 保留 inside 型 dataZoom
              type: 'inside',
              start: zoomRangeRef.current.start,
              end: zoomRangeRef.current.end,

              zoomLock: false,
              throttle: 50, // 减少节流时间，使交互更流畅
              rangeMode: ['value', 'value'],
              filterMode: 'filter',
              disabled: false, // 确保不被禁用
              zoomOnMouseWheel: true, // 允许鼠标滚轮缩放
              moveOnMouseMove: true, // 允许鼠标移动平移
              preventDefaultMouseMove: false, // 不阻止默认鼠标行为
              orient: 'horizontal', // 水平方向缩放
              zoomInCursor: 'pointer', // 放大时的光标样式
              zoomOutCursor: 'pointer', // 缩小时的光标样式
            },
          ]
        : [],
    };
    console.log('zoomRangeRef.current', zoomRangeRef.current);
    // 使用setOption更新图表数据，而不是重新创建图表实例
    myChartRef.current.setOption(option);

    // 监听dataZoom事件，当用户手动缩放时，更新zoomRangeRef的值
    myChartRef.current.off('datazoom');
    myChartRef.current.on('datazoom', function (params: any) {
      console.log('params', params);
      if (params.batch[0].start !== undefined && params.batch[0].end !== undefined) {
        zoomRangeRef.current = {
          start: params.batch[0].start,
          end: params.batch[0].end,
        };
        console.log('zoomRangeRef.current', zoomRangeRef.current);
      }
    });
  }, [priceList, dateList, isInteractive, isLoading, data]);

  return <div className="w-full h-full" ref={chartContainRef}></div>;
}
