import { Box } from '@quantum/components/ui/box';
import { Pressable } from '@quantum/components/ui/pressable';
import { useDebounce, useDebounceFn } from 'ahooks';
import { useEffect, useState } from 'react';
import { RiStarSFill } from 'react-icons/ri';
import { authStore } from '../../store/auth.store';
import { Text } from '@quantum/components/ui/text';

export const ButtonStar = ({
  size = 16,
  defaultStar = false,
  className,
  onToggleStar,
  isButton = false,
}: {
  size?: number;
  defaultStar?: boolean;
  className?: string;
  onToggleStar?: (value: boolean) => void;
  isButton?: boolean;
}) => {
  const { run: callOnToggleStar } = useDebounceFn(
    (isStar) => {
      onToggleStar?.(isStar);
    },
    {
      wait: 500,
    },
  );
  const [isStar, setIsStar] = useState(defaultStar);
  const handleToggleStar = (e: any) => {
    e.stopPropagation();
    if (!authStore.isLoggedIn) {
      authStore.setShowLoginModal(true);
      return;
    }

    const res = !isStar;
    setIsStar(res);
    callOnToggleStar(res);
  };
  useEffect(() => {
    setIsStar(defaultStar);
  }, [defaultStar]);

  return (
    <>
      <Pressable onPress={handleToggleStar}>
        {isButton ? (
          <Box
            className={`w-full h-full flex px-[12px] py-[6px] justify-center items-center gap-[4px] self-stretch rounded-[60px] bg-[rgba(5,198,151,0.10)] ${className}`}
          >
            <Text className="text-[#05C697] font-Poppins text-[12px] font-not-italic font-[500] leading-[16px]">
              {isStar ? 'Unwatch' : 'Watch'}
            </Text>
          </Box>
        ) : (
          <Box className={`${className}`}>
            <RiStarSFill size={size} className={`${isStar ? 'text-[#f5b800]' : 'text-[#cccccc]'}`} />
          </Box>
        )}
      </Pressable>
    </>
  );
};
