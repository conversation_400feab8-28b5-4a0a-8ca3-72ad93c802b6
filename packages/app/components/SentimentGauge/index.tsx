import React, { useState, useEffect } from 'react';

interface SegmentInfo {
  label: string;
  color: string;
}

interface SentimentGaugeProps {
  // 初始值
  value?: number;
  // 显示值
  showValue?: number;
  // 自定义标签
  labels?: string[];
  // 是否启用自动变化
  enableAutoChange?: boolean;
}

const defaultLabels = ['极度恐慌', '恐慌', '正常', '害怕', '乐观'];
const defaultColors = ['#ea8c00', '#ea8c00', '#f3d42f', '#16c784', '#16c784'];

const SentimentGauge = ({ value = 35, labels = defaultLabels, showValue = 35 }: SentimentGaugeProps) => {
  const [label, setLabel] = useState('');
  // 构建 segments 配置
  const segments = labels.map((label, index) => ({
    value: (index + 1) * 20,
    label,
    color: defaultColors[index],
  }));

  // 获取区间信息
  const getSegmentInfo = (val: number): SegmentInfo => {
    for (const segment of segments) {
      if (val < segment.value) {
        return { label: segment.label, color: segment.color };
      }
    }
    return segments[segments.length - 1];
  };

  // 获取高亮段落
  const getHighlightedSegment = (val: number): number => {
    for (let i = 0; i < segments.length; i++) {
      if (val < segments[i].value) return i;
    }
    return segments.length - 1;
  };

  // 初始化和更新标签
  useEffect(() => {
    const { label } = getSegmentInfo(value);
    setLabel(label);
  }, [value, labels]);

  // 段的路径数据，直接从你的SVG复制
  const segmentsData = [
    'M2 40.9997C2 35.9179 3.03463 30.8858 5.04482 26.1907C5.89381 24.2078 6.83562 22.7115 7.59918 21.709', // 左下
    'M26.5651 5.27148C21.7121 7.21622 17.4299 10.043 13.7156 13.6364C12.0238 15.2731 12.2322 15.1181 10.8208 16.9457', // 左上
    'M32.0491 3.30975C35.0596 2.36003 37.8447 2 41.922 2C45.9992 2 49.0327 2.68517 52.2292 3.30975', // 顶部
    'M57.7537 5.39111C62.6067 7.33585 66.5698 10.0433 70.2841 13.6367C71.9759 15.2734 72.6867 16.1144 73.7315 17.2984', // 右上
    'M82.0001 41.0002C82.0001 35.9184 80.9655 30.8863 78.9553 26.1913C78.0456 24.0666 77.7899 23.3331 76.9243 22.0625', // 右下
  ];

  const highlightedSegment = getHighlightedSegment(value);
  const { color } = getSegmentInfo(value);

  return (
    <div className="relative w-full h-full">
      <svg viewBox="0 0 84 43" className="w-full h-full" preserveAspectRatio="xMidYMid meet">
        {segmentsData.map((path, index) => (
          <path
            key={index}
            d={path}
            stroke={index === highlightedSegment ? color : '#CCCCCC'}
            strokeWidth="4"
            strokeLinecap="round"
            fill="none"
          />
        ))}
      </svg>

      <div className="absolute inset-0 flex flex-col items-center justify-center mt-4">
        <span className="text-[#0A0A0A] text-center font-Inter text-[14px] font-not-italic font-[700] leading-[20px]">
          {showValue}
        </span>
        <span className="self-stretch text-[#808080] text-center font-Inter text-[10px] font-not-italic font-[400] leading-normal">
          {label}
        </span>
      </div>
    </div>
  );
};

export default SentimentGauge;
