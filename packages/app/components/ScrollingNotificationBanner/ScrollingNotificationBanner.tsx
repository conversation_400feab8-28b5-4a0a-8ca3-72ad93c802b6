'use client';
import { useState, useEffect, useRef, useCallback } from 'react';
import type { ReactNode } from 'react';

export default function ScrollingNotificationBanner({ data }: { data: { content: ReactNode }[] }) {
  // 修改消息定义为JSX内容
  const messages = data;

  const [activeIndex, setActiveIndex] = useState(0);
  const [nextIndex, setNextIndex] = useState(1);
  const [isAnimating, setIsAnimating] = useState(false);
  const [containerHeight, setContainerHeight] = useState<number>(24); // 默认高度为24px
  const containerRef = useRef<HTMLDivElement>(null);

  // 正确定义messageRefs的类型
  const messageRefs = useRef<Array<HTMLDivElement | null>>([]);

  const rotateMessages = () => {
    setIsAnimating(true);

    setTimeout(() => {
      const newActiveIndex = (activeIndex + 1) % messages.length;
      const newNextIndex = (newActiveIndex + 1) % messages.length;

      setActiveIndex(newActiveIndex);
      setNextIndex(newNextIndex);
      setIsAnimating(false);
    }, 600);
  };

  // 计算最高消息高度的函数
  const calculateMaxHeight = useCallback(() => {
    if (messageRefs.current.length === messages.length) {
      let maxHeight = 24; // 最小默认高度
      messageRefs.current.forEach((ref) => {
        if (ref) {
          const height = ref.scrollHeight;
          maxHeight = Math.max(maxHeight, height);
        }
      });
      setContainerHeight(maxHeight);
    }
  }, [messages]);

  // 计算最高消息的高度
  useEffect(() => {
    calculateMaxHeight();
  }, [messages, calculateMaxHeight]);

  // 监听窗口大小变化，重新计算高度
  useEffect(() => {
    const handleResize = () => {
      calculateMaxHeight();
    };

    // 添加窗口大小变化事件监听器
    window.addEventListener('resize', handleResize);

    // 组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [calculateMaxHeight]);

  useEffect(() => {
    const interval = setInterval(() => {
      rotateMessages();
    }, 3000);

    return () => clearInterval(interval);
  }, [activeIndex]);

  return (
    <div className="w-full">
      <div ref={containerRef} className="relative overflow-hidden" style={{ height: `${containerHeight}px` }}>
        {messages.map((message, index) => {
          // Determine position state for each message
          let position = 'hidden';

          if (index === activeIndex) {
            position = isAnimating ? 'leaving' : 'active';
          } else if (index === nextIndex) {
            position = isAnimating ? 'entering' : 'next';
          }

          // Set styles based on position state
          const classNames = {
            hidden: 'absolute w-full opacity-0',
            active: 'absolute w-full opacity-100 translate-y-0',
            next: 'absolute w-full opacity-0 translate-y-full',
            leaving: 'absolute w-full opacity-0 -translate-y-full transition-all duration-500 ease-in-out',
            entering: 'absolute w-full opacity-100 translate-y-0 transition-all duration-500 ease-in-out',
          };

          return (
            <div
              key={index}
              ref={(el) => (messageRefs.current[index] = el)}
              className={`${classNames[position as keyof typeof classNames]}`}
            >
              {message.content}
            </div>
          );
        })}
      </div>
    </div>
  );
}
