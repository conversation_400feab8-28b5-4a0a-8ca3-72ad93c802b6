'use client';
import { Menu, MenuItem } from '@quantum/components/ui/menu';
import { Button } from '@quantum/components/ui/button';
import { Text } from '@quantum/components/ui/text';
import { HStack } from '@quantum/components/ui/hstack';
import { RiCheckLine, RiArrowDownSLine, RiSearch2Line } from 'react-icons/ri';
import { useEffect, useState } from 'react';
import { Input, InputField, InputSlot } from '@quantum/components/ui/input';
import { View } from '@quantum/components/ui/view';
import { Popover, PopoverBackdrop, PopoverContent } from '@quantum/components/ui/popover';
import { Pressable } from '@quantum/components/ui/pressable';
import { useDebounceFn } from 'ahooks';
import { trim } from 'lodash';
import type { NativeSyntheticEvent, TextInputChangeEventData } from 'react-native';

export default function SelectUI({
  className,
  valueClassName,
  label,
  list,
  value,
  onChange,
  enableSearch,
}: {
  className?: string;
  valueClassName?: string;
  label?: string | JSX.Element;
  list: { value: any; label: string; icon?: string }[];
  value: (typeof list)[number]['value'];
  onChange: (value: (typeof list)[number]['value']) => void;
  enableSearch?: boolean;
}) {
  const [search, setSearch] = useState('');
  const [selected, setSelected] = useState(value);
  useEffect(() => {
    setSelected(value);
  }, [value]);

  const [isOpen, setIsOpen] = useState(false);
  const handleOpen = () => {
    setIsOpen(true);
  };
  const handleClose = () => {
    setIsOpen(false);
  };
  const filteredList = list
    .map((item) => {
      return {
        ...item,
        label: item.label.trim(),
      };
    })
    // 根据label去重
    .reduce(
      (unique, item) => {
        const exists = unique.some((uniqueItem) => uniqueItem.label.toLowerCase() === item.label.toLowerCase());
        if (!exists) {
          unique.push(item);
        }
        return unique;
      },
      [] as typeof list,
    )
    .filter((item) => item?.label?.toLowerCase().includes((search).toLowerCase()));
  
  const handleSearch = (e: NativeSyntheticEvent<TextInputChangeEventData>) => {
    setSearch(trim(e.nativeEvent.text));
  };
  return (
    <Popover
      isOpen={isOpen}
      onClose={handleClose}
      onOpen={handleOpen}
      placement="bottom right"
      offset={5}
      trigger={({ ...triggerProps }) => {
        return (
          <Button
            variant="link"
            {...triggerProps}
            className={`h-4 min-w-[120px] border-[1px] border-solid border-[#CCCCCC] bg-[#FFF] rounded-[6px] py-4 px-[10px] ${className}`}
          >
            {label && (
              <Text className="text-[#808080] font-[Poppins] text-[14px] font-not-italic font-400 leading-[20px] flex-1">
                {label}
              </Text>
            )}
            <HStack className={`${!label ? 'flex-1' : ''} items-center justify-between gap-1`}>
              <Text
                className={`text-[#0A0A0A] font-Inter text-[12px] font-not-italic font-[500] leading-[16px] flex-1 ${valueClassName}`}
              >
                {list.find((item: any) => item.value === selected)?.label || ''}
              </Text>
              <RiArrowDownSLine size={16} className="text-[#808080]" />
            </HStack>
          </Button>
        );
      }}
    >
      <PopoverBackdrop />
      <PopoverContent className="p-[10px] min-w-[220px] max-h-[300px] overflow-y-auto rounded-[6px] border-[1px] border-solid border-[#E6E6E6] bg-[#FFF] shadow-[0px_8px_20px_0px_rgba(0,0,0,0.10)]">
        {enableSearch && (
          <Input className="mb-3 flex px-[12px] py-[6px] items-center gap-[6px] self-stretch rounded-[6px] bg-[#F7F7F7] border-none">
            <InputSlot>
              <RiSearch2Line size={20} className="text-[#808080]" />
            </InputSlot>
            <InputField
              placeholder="Search"
              className="pl-0 text-[14px] font-not-italic font-400 leading-[20px]"
              onChange={handleSearch}
              defaultValue={search}
            />
          </Input>
        )}
        {filteredList.map((item: any) => (
          <Pressable
            onPress={() => {
              setSelected(item.value);
              onChange(item.value);
            }}
            key={item.value}
            className="px-[8px] py-[10px] items-center gap-[16px] self-stretch rounded-[6px] bg-[#FFF] data-[hover=true]:bg-[rgba(0,173,107,0.05)]"
          >
            <HStack className="items-center justify-between w-full">
              <Text
                className={`text-[#4D4D4D] font-Poppins text-[14px] font-not-italic font-[400] leading-[20px] ${
                  selected == item.value ? 'font-[500]' : ''
                }`}
              >
                {item.label}
              </Text>

              {selected == item.value && <RiCheckLine size={16} className="text-[#40C382]" />}
            </HStack>
          </Pressable>
        ))}
      </PopoverContent>
    </Popover>
  );
}
