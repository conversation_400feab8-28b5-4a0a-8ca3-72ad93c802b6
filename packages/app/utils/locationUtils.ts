/**
 * 为 URL 添加查询参数
 * @param {Object|string} params - 要添加的参数对象或参数名
 * @param {string} [value] - 当 params 为字符串时，表示要添加的参数值
 * @param {string} [customUrl] - 自定义URL，如果不传则使用当前页面URL
 * @param {boolean} [onlyMainDomain=false] - 是否只返回主域名（不含路径）
 * @returns {string} 返回新的URL
 */
export function addQueryToUrl(params: any, value: any, customUrl: any, onlyMainDomain = false) {
  // 处理参数
  let urlToModify = customUrl;
  let keepMainDomain = onlyMainDomain;

  // 如果第一个参数是字符串，调整参数顺序
  if (typeof params === 'string') {
    const queryObj = { [params]: value };
    if (typeof value === 'string' && value.startsWith('http')) {
      urlToModify = value;
      keepMainDomain = customUrl || false;
    }
    params = queryObj;
  }

  // 使用自定义URL或当前页面URL
  const baseUrl = urlToModify || window.location.href;

  // 创建 URL 对象
  let url;
  try {
    url = new URL(baseUrl);
  } catch (e) {
    // 降级处理
    const anchor = document.createElement('a');
    anchor.href = baseUrl;
    url = {
      search: anchor.search,
      pathname: anchor.pathname,
      protocol: anchor.protocol,
      host: anchor.host
    };
  }

  // 创建 URLSearchParams 对象
  let searchParams;
  try {
    searchParams = new URLSearchParams(url.search);
  } catch (e) {
    // 降级处理
    searchParams = {
      set: function(key, value) {
        const regex = new RegExp(`([?&])${key}=.*?(&|$)`, 'i');
        const separator = url.search.indexOf('?') !== -1 ? '&' : '?';
        if (url.search.match(regex)) {
          url.search = url.search.replace(regex, `$1${key}=${value}$2`);
        } else {
          url.search = url.search + separator + `${key}=${value}`;
        }
      },
      toString: function() {
        return url.search;
      }
    };
  }

  // 添加所有参数
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.set(key, value.toString());
    }
  });

  // 构建新的 URL
  let newUrl;
  if (url instanceof URL) {
    if (keepMainDomain) {
      url.pathname = '';
      url.search = searchParams.toString();
      newUrl = url.toString();
    } else {
      url.search = searchParams.toString();
      newUrl = url.toString();
    }
  } else {
    if (keepMainDomain) {
      newUrl = `${url.protocol}//${url.host}${searchParams.toString()}`;
    } else {
      newUrl = `${url.protocol}//${url.host}${url.pathname}${url.search}`;
    }
  }

  return newUrl;
}


export function getMainDomain() {
  // 优先使用 origin，降级使用拼接方式
  return window.location.origin || (window.location.protocol + '//' + window.location.host);
}