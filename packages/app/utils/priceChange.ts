import type { KLineDataItem } from '../services/kline/kline.types';

/**
 * 计算百分比变化
 * @param currentPrice 当前价格
 * @param previousPrice 前一个价格
 * @returns 百分比变化值
 */
const calculatePercentageChange = (currentPrice: number, previousPrice: number): number => {
  if (previousPrice === 0) return 0;
  return ((currentPrice - previousPrice) / previousPrice) * 100;
};

/**
 * 格式化百分比为字符串
 * @param percentage 百分比数值
 * @returns 格式化的百分比字符串，包含正负号
 */
const formatPercentage = (percentage: number): string => {
  const sign = percentage >= 0 ? '+' : '';
  return `${sign}${percentage.toFixed(2)}%`;
};

/**
 * 从K线数据中获取收盘价
 * @param data K线数据项
 * @returns 收盘价（数字类型）
 */
const getClosePrice = (data: KLineDataItem): number => {
  // K线数据格式：[开盘时间戳, 开盘价, 最高价, 最低价, 收盘价, 成交数量, 成交金额]
  // 收盘价是第5项（索引为4）
  return parseFloat(data[4]);
};

/**
 * 从时间戳获取日期字符串（格式：YYYY-MM-DD）
 * @param timestamp 时间戳（字符串格式）
 * @returns 日期字符串
 */
const getDateFromTimestamp = (timestamp: string): string => {
  // 转换为毫秒时间戳（如果是秒级时间戳则需要乘以1000）
  const timestampNum = parseInt(timestamp, 10);
  // 根据时间戳长度判断是秒级还是毫秒级
  const date = new Date(timestampNum * (timestampNum.toString().length <= 10 ? 1000 : 1));
  return date.toISOString().split('T')[0]; // 返回YYYY-MM-DD格式
};

/**
 * 根据时间戳获取日期对象
 * @param timestamp 时间戳（字符串格式）
 * @returns Date对象
 */
const getDateObjectFromTimestamp = (timestamp: string): Date => {
  const timestampNum = parseInt(timestamp, 10);
  return new Date(timestampNum * (timestampNum.toString().length <= 10 ? 1000 : 1));
};

/**
 * 查找交易日索引
 * 该函数有两种使用方式：
 * 1. 查找指定日期之前的最近交易日(提供targetDate参数)
 * 2. 查找指定索引对应日期的前一个交易日(提供currentIndex参数，不提供targetDate)
 * 
 * @param klineData K线数据数组
 * @param currentIndex 当前数据索引(可选，如果与targetDate同时提供，则优先使用targetDate)
 * @param targetDate 目标日期(可选，查找该日期之前的最近交易日)
 * @returns 查找到的交易日索引，如果没找到则返回-1或0
 */
const findTradingDayIndex = (
  klineData: KLineDataItem[], 
  currentIndex?: number, 
  targetDate?: Date
): number => {
  if (!klineData?.length) return -1;
  
  // 场景1: 查找指定日期之前的最近交易日
  if (targetDate) {
    const targetDateStr = targetDate.toISOString().split('T')[0];
    
    // 从数据末尾向前遍历，找到第一个日期小于等于目标日期的记录
    for (let i = klineData.length - 1; i >= 0; i--) {
      const currentDate = getDateFromTimestamp(klineData[i][0]);
      if (currentDate <= targetDateStr) {
        return i;
      }
    }
    
    return 0; // 如果没有找到，返回第一个交易日
  }
  
  // 场景2: 查找当前索引对应日期的前一个交易日
  if (currentIndex !== undefined && currentIndex > 0) {
    const currentDate = getDateFromTimestamp(klineData[currentIndex][0]);
    
    // 从当前索引向前查找，直到找到不同日期的数据
    for (let i = currentIndex - 1; i >= 0; i--) {
      const prevDate = getDateFromTimestamp(klineData[i][0]);
      if (prevDate !== currentDate) {
        return i;
      }
    }
  }
  
  return -1; // 没有找到前一个交易日
};

/**
 * 计算不同时间周期的价格变化
 * @param yearData 全年K线数据（用于计算所有周期的涨跌幅）
 * @returns 包含各个周期涨跌幅的对象
 */
export const calculatePriceChanges = (yearData: KLineDataItem[]) => {
  // 默认返回值（无数据情况）
  const defaultChanges = {
    oneDayChange: '0.00%',
    oneWeekChange: '0.00%',
    oneMonthChange: '0.00%',
    threeMonthChange: '0.00%',
    oneYearChange: '0.00%',
  }

  // 检查数据有效性
  if (!yearData?.length || yearData.length < 2) {
    return defaultChanges;
  }

  // 获取最新收盘价 (最后一个元素是最新的)
  const lastIndex = yearData.length - 1;
  const latestPrice = getClosePrice(yearData[lastIndex]);
  const latestDate = getDateObjectFromTimestamp(yearData[lastIndex][0]);
  
  // 计算1日涨跌幅 (找到真正的前一个交易日进行比较)
  const previousDayIndex = findTradingDayIndex(yearData, lastIndex);
  const oneDayChange = previousDayIndex >= 0 
    ? calculatePercentageChange(latestPrice, getClosePrice(yearData[previousDayIndex])) 
    : 0;

    

  // 计算1周涨跌幅 - 找到一周前的最近交易日
  const oneWeekDate = new Date(latestDate);
  oneWeekDate.setDate(latestDate.getDate() - 7);
  const oneWeekIndex = findTradingDayIndex(yearData, undefined, oneWeekDate);
  const oneWeekChange = oneWeekIndex >= 0 
    ? calculatePercentageChange(latestPrice, getClosePrice(yearData[oneWeekIndex])) 
    : 0;

  // 计算1个月涨跌幅
  const oneMonthDate = new Date(latestDate);
  oneMonthDate.setMonth(latestDate.getMonth() - 1);
  const oneMonthIndex = findTradingDayIndex(yearData, undefined, oneMonthDate);
  const oneMonthChange = oneMonthIndex >= 0 
    ? calculatePercentageChange(latestPrice, getClosePrice(yearData[oneMonthIndex])) 
    : 0;
  
  // 计算3个月涨跌幅
  const threeMonthDate = new Date(latestDate);
  threeMonthDate.setMonth(latestDate.getMonth() - 3);
  const threeMonthIndex = findTradingDayIndex(yearData, undefined, threeMonthDate);
  const threeMonthChange = threeMonthIndex >= 0 
    ? calculatePercentageChange(latestPrice, getClosePrice(yearData[threeMonthIndex])) 
    : 0;

  // 计算1年涨跌幅
  const oneYearDate = new Date(latestDate);
  oneYearDate.setFullYear(latestDate.getFullYear() - 1);
  const oneYearIndex = findTradingDayIndex(yearData, undefined, oneYearDate);
  const oneYearChange = oneYearIndex >= 0 
    ? calculatePercentageChange(latestPrice, getClosePrice(yearData[oneYearIndex])) 
    : 0;
  
  return {
    oneDayChange: formatPercentage(oneDayChange),
    oneWeekChange: formatPercentage(oneWeekChange),
    oneMonthChange: formatPercentage(oneMonthChange),
    threeMonthChange: formatPercentage(threeMonthChange),
    oneYearChange: formatPercentage(oneYearChange),
  }
}; 