import i18n from "i18next";
import { useCustomToast } from '../components/Toast/ToastProvider';



export function getErrorMessage(e: any, defaultMsg = 'unknown error!(web)') {
  // console.log(JSON.parse(JSON.stringify(e)));
  let msg = e.msg || e?.cause?.reason || e.shortMessage || e.message || e.reason
  const errorCode = e.code || e.name
  if (errorCode && i18n.exists(`${errorCode}`, { ns: 'error_code' })) {
    msg = i18n.t(`${errorCode}`, { ns: 'error_code' })
  }
  return msg || defaultMsg
}

export function useHandleErrorMessage() {

  const { showToast } = useCustomToast();

  function handleErrorMessage(e: any, defaultMsg = undefined) {
    if (e.response?.status === 401) {
      return
    }
    const msg = getErrorMessage(e, defaultMsg)
    showToast({
      title: 'Error',
      description: msg,
      status: 'error',
    });
  }
  return {
    handleErrorMessage
  }
}


