/**
 * 小数简化格式化工具
 * 将连续的0简化为下标表示，例如 0.000001 -> 0.0₆1
 */

/**
 * 格式化选项接口
 */
export interface FormatDecimalOptions {
  /** 是否返回HTML格式（带有sub标签） */
  useHTML?: boolean;
  /** 是否仅处理尾随零 */
  onlyTrailingZeros?: boolean;
  /** 最小连续零的数量，达到这个数量才会简化 */
  minZeros?: number;
  /** 是否保留原始值 */
  keepOriginal?: boolean;
}

/**
 * 格式化结果接口
 */
export interface FormattedDecimalResult {
  /** 格式化后的值 */
  formatted: string;
  /** 原始值 */
  original: string;
  /** 整数部分 */
  integerPart: string;
  /** 小数部分 */
  decimalPart: string;
  /** 简化的部分（如果有的话） */
  simplifiedParts: Array<{
    /** 简化前的字符 */
    character: string;
    /** 连续出现的次数 */
    count: number;
    /** 在原始字符串中的起始位置 */
    startIndex: number;
    /** 在原始字符串中的结束位置 */
    endIndex: number;
  }>;
  /** 是否含有简化部分 */
  hasSimplification: boolean;
  /** 最大连续出现的次数 */
  maxConsecutiveCount: number;
}

/**
 * 用于格式化小数的工具函数
 * 将连续的0简化为下标表示，例如 0.000001 -> 0.0₆1
 *
 * @param value - 要格式化的数值
 * @param options - 配置选项
 * @returns 格式化后的字符串
 */
export function formatDecimal(value: number | string, options: FormatDecimalOptions = {}): string {
  const { useHTML = true, onlyTrailingZeros = false, minZeros = 3 } = options;

  // 将输入转换为字符串
  const strValue = String(value);
  const isNegative = strValue.startsWith('-');
  const absStrValue = isNegative ? strValue.slice(1) : strValue;

  // 如果不包含小数点，直接返回
  if (!absStrValue.includes('.')) {
    return isNegative ? `-${absStrValue}` : absStrValue;
  }

  // 分离整数部分和小数部分
  const [integerPart, decimalPart] = absStrValue.split('.');

  // 处理小数部分
  let result = (isNegative ? '-' : '') + integerPart + '.';
  let zeroCount = 0;
  let formattedDecimal = '';
  let i = 0;

  // 遍历小数部分
  while (i < decimalPart.length) {
    // 当前字符
    const char = decimalPart[i];

    // 如果是0，计数
    if (char === '0') {
      zeroCount++;
      i++;
      continue;
    }

    // 如果不是0，检查前面是否有需要简化的连续0
    if (zeroCount >= minZeros) {
      // 添加简化表示
      if (useHTML) {
        formattedDecimal += `0<sub>${zeroCount}</sub>`;
      } else {
        // 使用Unicode下标字符
        formattedDecimal += `0${toSubscript(zeroCount)}`;
      }
      zeroCount = 0;
    } else {
      // 零的数量不够，直接添加
      formattedDecimal += '0'.repeat(zeroCount);
      zeroCount = 0;
    }

    // 添加非零字符
    formattedDecimal += char;
    i++;
  }

  // 处理尾部剩余的0
  if (zeroCount >= minZeros) {
    if (useHTML) {
      formattedDecimal += `0<sub>${zeroCount}</sub>`;
    } else {
      formattedDecimal += `0${toSubscript(zeroCount)}`;
    }
  } else {
    formattedDecimal += '0'.repeat(zeroCount);
  }

  return result + formattedDecimal;
}

/**
 * 将数字转换为Unicode下标字符
 *
 * @param num - 要转换的数字
 * @returns 下标字符串
 */
export function toSubscript(num: number | string): string {
  const subscriptDigits: Record<string, string> = {
    '0': '₀',
    '1': '₁',
    '2': '₂',
    '3': '₃',
    '4': '₄',
    '5': '₅',
    '6': '₆',
    '7': '₇',
    '8': '₈',
    '9': '₉',
  };

  return String(num)
    .split('')
    .map((digit) => subscriptDigits[digit] || digit)
    .join('');
}

/**
 * 将带有下标表示的小数转换回普通小数
 *
 * @param value - 格式化后的字符串 (例如: "0.0₆123")
 * @returns 普通小数表示 (例如: "0.000000123")
 */
export function parseFormattedDecimal(value: string): string {
  // 处理带HTML标签的格式
  if (value.includes('<sub>')) {
    return value.replace(/(\d+)<sub>(\d+)<\/sub>/g, (match, digit, count) => {
      return digit.repeat(parseInt(count, 10) + 1);
    });
  }

  // 处理Unicode下标字符格式
  const subscriptMap: Record<string, number> = {
    '₀': 0,
    '₁': 1,
    '₂': 2,
    '₃': 3,
    '₄': 4,
    '₅': 5,
    '₆': 6,
    '₇': 7,
    '₈': 8,
    '₉': 9,
  };

  let result = '';
  let i = 0;

  while (i < value.length) {
    const char = value[i];
    const nextChar = value[i + 1];

    // 检查当前字符后是否有下标
    if (char && nextChar && subscriptMap[nextChar] !== undefined) {
      // 获取下标数值
      let subscriptNum = '';
      let j = i + 1;

      while (j < value.length && subscriptMap[value[j]] !== undefined) {
        subscriptNum += subscriptMap[value[j]];
        j++;
      }

      // 添加重复的字符
      result += char.repeat(parseInt(subscriptNum, 10) + 1);
      i = j;
    } else {
      // 普通字符
      result += char;
      i++;
    }
  }

  return result;
}

/**
 * 增强版格式化函数，返回详细的格式化结果对象
 *
 * @param value - 要格式化的数值
 * @param options - 配置选项
 * @returns 格式化结果对象
 */
export function formatDecimalEnhanced(
  value: number | string,
  options: FormatDecimalOptions = {},
): FormattedDecimalResult {
  const { useHTML = true, onlyTrailingZeros = false, minZeros = 3, keepOriginal = true } = options;

  // 将输入转换为字符串
  const strValue = String(value);
  const original = strValue;
  const isNegative = strValue.startsWith('-');
  const absStrValue = isNegative ? strValue.slice(1) : strValue;

  // 初始化结果对象
  const result: FormattedDecimalResult = {
    formatted: '',
    original: original,
    integerPart: '',
    decimalPart: '',
    simplifiedParts: [],
    hasSimplification: false,
    maxConsecutiveCount: 0,
  };

  // 如果不包含小数点，直接返回
  if (!absStrValue.includes('.')) {
    result.formatted = isNegative ? `-${absStrValue}` : absStrValue;
    result.integerPart = absStrValue;
    result.decimalPart = '';
    return result;
  }

  // 分离整数部分和小数部分
  const [integerPart, decimalPart] = absStrValue.split('.');
  result.integerPart = integerPart;
  result.decimalPart = decimalPart;

  // 处理小数部分
  let formattedResult = (isNegative ? '-' : '') + integerPart + '.';
  let zeroCount = 0;
  let formattedDecimal = '';
  let i = 0;

  // 遍历小数部分
  while (i < decimalPart.length) {
    // 当前字符
    const char = decimalPart[i];

    // 如果是0，计数
    if (char === '0') {
      zeroCount++;
      i++;
      continue;
    }

    // 如果不是0，检查前面是否有需要简化的连续0
    if (zeroCount >= minZeros) {
      // 记录简化部分
      result.simplifiedParts.push({
        character: '0',
        count: zeroCount,
        startIndex: i - zeroCount,
        endIndex: i - 1,
      });

      // 更新最大连续计数
      if (zeroCount > result.maxConsecutiveCount) {
        result.maxConsecutiveCount = zeroCount;
      }

      result.hasSimplification = true;

      // 添加简化表示
      if (useHTML) {
        formattedDecimal += `0<sub>${zeroCount}</sub>`;
      } else {
        // 使用Unicode下标字符
        formattedDecimal += `0${toSubscript(zeroCount)}`;
      }
      zeroCount = 0;
    } else {
      // 零的数量不够，直接添加
      formattedDecimal += '0'.repeat(zeroCount);
      zeroCount = 0;
    }

    // 添加非零字符
    formattedDecimal += char;
    i++;
  }

  // 处理尾部剩余的0
  if (zeroCount >= minZeros) {
    // 记录简化部分
    result.simplifiedParts.push({
      character: '0',
      count: zeroCount,
      startIndex: decimalPart.length - zeroCount,
      endIndex: decimalPart.length - 1,
    });

    // 更新最大连续计数
    if (zeroCount > result.maxConsecutiveCount) {
      result.maxConsecutiveCount = zeroCount;
    }

    result.hasSimplification = true;

    if (useHTML) {
      formattedDecimal += `0<sub>${zeroCount}</sub>`;
    } else {
      formattedDecimal += `0${toSubscript(zeroCount)}`;
    }
  } else {
    formattedDecimal += '0'.repeat(zeroCount);
  }

  result.formatted = formattedResult + formattedDecimal;

  return result;
}

/**
 * 批量处理多个数字
 *
 * @param values - 要处理的数值数组
 * @param options - 配置选项
 * @returns 处理结果数组
 */
export function batchFormatDecimals(
  values: (number | string)[],
  options: FormatDecimalOptions = {},
): FormattedDecimalResult[] {
  return values.map((value) => formatDecimalEnhanced(value, options));
}

/**
 * 默认导出所有工具函数
 */
export default {
  formatDecimal,
  formatDecimalEnhanced,
  parseFormattedDecimal,
  toSubscript,
  batchFormatDecimals,
};
