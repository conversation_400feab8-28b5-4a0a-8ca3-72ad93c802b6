import numbro from 'numbro';
import { formatDecimal, formatDecimalEnhanced } from './decimal-formatter-utils';

export function averageNumber(value: number | string, mantissa = 6, optionalMantissa = true, averageNum = 999999) {
  return String(
    mantissaNumber(value, Number(value) > averageNum ? 2 : mantissa, optionalM<PERSON>ssa, Math.abs(Number(value)) > averageNum),
  ).toUpperCase();
}

export function mantissaNumber(value: number | string, mantissa = 6, optionalMantissa = true, average = false) {
  if (!value) return '0';
  try {
    if (Number(value) < 1 && Number(value) > 0 && mantissa >= 6) {
      const res = formatDecimalEnhanced(Number(value), {
        useHTML: false,
      });
      if (res.hasSimplification) {
        return res.formatted;
      }
    }
    return numbro(value).format({
      thousandSeparated: true,
      average,
      totalLength: average ? 4 : 0,
      optionalMantissa,
      mantissa,
      trimMantissa: true,
    });
  } catch (error) {
    return '0';
  }
}
