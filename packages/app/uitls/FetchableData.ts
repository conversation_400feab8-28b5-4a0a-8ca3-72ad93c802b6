import { makeAutoObservable, action } from "mobx";

class FetchableData<T> {
  data: T | null = null;
  loading: boolean = false;
  lastApiParams: any = {};

  constructor(private fetchFunction: (apiParams: any) => Promise<T>) {
    makeAutoObservable(this);
  }

  // 添加 action 方法来设置数据
  setData(data: T | null) {
    this.data = data;
  }

  // 添加 action 方法来设置加载状态
  setLoading(loading: boolean) {
    this.loading = loading;
  }

  // 添加 action 方法来设置最后的API参数
  setLastApiParams(params: any) {
    this.lastApiParams = params;
  }

  async fetch(apiParams: any = {}, useLoading: boolean = true) {
    // 使用 action 方法设置状态
    this.setLastApiParams(apiParams);
    if (useLoading) {
      this.setLoading(true);
    }

    try {
      const data = await this.fetchFunction(apiParams);
      // 使用 action 方法设置数据
      this.setData(data);
      return data
    } catch (error) {
      console.error("Failed to fetch data:", error);
      return Promise.reject(error);
    } finally {
      if (useLoading) {
        // 使用 action 方法设置加载状态
        this.setLoading(false);
      }
    }
  }

  async reload(useLoading: boolean = true) {
    await this.fetch(this.lastApiParams, useLoading);
  }
}

export default FetchableData;