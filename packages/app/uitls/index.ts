export function sleep(ms: number = 1000) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}


// 检测运行环境
export const canUsePersistence = () => {
  // 检测是否在 React Native 环境
  const isReactNative = typeof global.navigator?.product === 'string' && 
                      global.navigator?.product === 'ReactNative';
                      
  // 检测是否在服务器端渲染环境
  const isServer = typeof window === 'undefined';
  
  // 只有在客户端浏览器或 React Native 环境中才使用持久化
  return !isServer || isReactNative;
};
