import { useCallback, useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const useAsyncStorage = <T>(key: string, initialValue?: T) => {
  const [storedValue, setStoredValue] = useState<T | undefined>(initialValue);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  // 初始化时读取存储的值
  useEffect(() => {
    const loadStoredValue = async () => {
      try {
        setLoading(true);
        const jsonValue = await AsyncStorage.getItem(key);
        
        if (jsonValue !== null) {
          const value = JSON.parse(jsonValue) as T;
          setStoredValue(value);
        } else if (initialValue !== undefined) {
          // 如果存储中没有值且提供了初始值，则设置初始值
          setStoredValue(initialValue);
          await AsyncStorage.setItem(key, JSON.stringify(initialValue));
        }
      } catch (e) {
        setError(e instanceof Error ? e : new Error(String(e)));
      } finally {
        setLoading(false);
      }
    };
    
    loadStoredValue();
  }, [key, initialValue]);
  
  // 更新存储的值
  const setValue = useCallback(async (value: T | ((val: T | undefined) => T)) => {
    try {
      // 如果值是函数，则传入当前值并执行
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      
      // 保存到 state
      setStoredValue(valueToStore);
      
      // 保存到 AsyncStorage
      await AsyncStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (e) {
      setError(e instanceof Error ? e : new Error(String(e)));
    }
  }, [key, storedValue]);
  
  // 移除存储的值
  const removeValue = useCallback(async () => {
    try {
      await AsyncStorage.removeItem(key);
      setStoredValue(undefined);
    } catch (e) {
      setError(e instanceof Error ? e : new Error(String(e)));
    }
  }, [key]);
  
  return {
    value: storedValue,
    setValue,
    removeValue,
    loading,
    error,
  };
};
    