import { useRequest } from "ahooks";
import { addWatchlist, removeWatchlist } from "../services/watchlist/watchlist.api";
import type { Market, AssetBaseParam } from "../services/watchlist/watchlist.types";

export function useToggleStar(market: AssetBaseParam['market'], symbol: AssetBaseParam['symbol']) {
    const {runAsync, loading} = useRequest(
       async(isStar: boolean) => {
        if (isStar) {
            await addWatchlist({market, symbol});
        } else {
            await removeWatchlist({market, symbol});
        }
       },
       {
        manual: true,
       }
    )
  return {
    runAsync,
    loading
  }
}