import api from '../api';
import type {
  PortfolioTxAddParam,
  CommonResponse,
  PositionsResp,
  GroupOverviewResp,
  PortfolioPosParam,
  TransactionsResp,
  PerformanceChartParam,
  PortfolioChartResp,
  AllowedContentTypes,
  UploadImageLinkResp,
  UserGuideStatusResp
} from './portfolio.types';

/**
 * 添加 Portfolio 交易
 * POST /api/v1/private/portfolio/transaction/add
 * 接口ID：添加_Portfolio_交易_api_v1_private_portfolio_transaction_add_post
 */
export const addPortfolioTransaction = async (params: PortfolioTxAddParam) => api.post<CommonResponse>(
  '/api/v1/private/portfolio/transaction/add',
  params
);

/**
 * Portfolio 持仓列表
 * POST /api/v1/private/portfolio/position/list
 * 接口ID：Portfolio_持仓列表_api_v1_private_portfolio_position_list_post
 */
export const getPortfolioPositionList = async () => api.post<PositionsResp>(
  '/api/v1/private/portfolio/position/list'
);

/**
 * Portfolio 持仓概览
 * POST /api/v1/private/portfolio/group/overview
 * 接口ID：Portfolio_持仓概览_api_v1_private_portfolio_group_overview_post
 */
export const getPortfolioGroupOverview = async () => api.post<GroupOverviewResp>(
  '/api/v1/private/portfolio/group/overview'
);

/**
 * Portfolio 持仓下的交易明细
 * POST /api/v1/private/portfolio/transaction/list
 * 接口ID：Portfolio_持仓下的交易明细_api_v1_private_portfolio_transaction_list_post
 */
export const getPortfolioTransactionList = async (params: PortfolioPosParam) => api.post<TransactionsResp>(
  '/api/v1/private/portfolio/transaction/list',
  params
);

/**
 * Portfolio 持仓收益曲线
 * POST /api/v1/private/portfolio/group/performance-chart
 * 接口ID：Portfolio_持仓收益曲线_api_v1_private_portfolio_group_performance_chart_post
 */
export const getPortfolioPerformanceChart = async (params: PerformanceChartParam) => api.post<PortfolioChartResp>(
  '/api/v1/private/portfolio/group/performance-chart',
  params
); 

/**
 * 查询用户是否完成引导
 * GET /api/v1/private/portfolio/user/guide
 * 接口ID：查询用户是否完成引导_api_v1_private_portfolio_user_guide_get
 */
export const getPortfolioUserGuide = async () => api.get<UserGuideStatusResp>(
  '/api/v1/private/portfolio/user/guide'
);

/**
 * 标记用户已经完成引导
 * POST /api/v1/private/portfolio/user/guide
 * 接口ID：标记用户已经完成引导_api_v1_private_portfolio_user_guide_post
 */
export const markPortfolioUserGuide = async () => api.post<CommonResponse>(
  '/api/v1/private/portfolio/user/guide'
);

/**
 * Portfolio 持仓收益分享
 * GET /api/v1/private/portfolio/group/share-link
 * 接口ID：Portfolio_持仓收益分享_api_v1_private_portfolio_group_share_link_get
 */
export const getPortfolioShareLink = async (contentType?: AllowedContentTypes) => api.get<UploadImageLinkResp>(
  '/api/v1/private/portfolio/group/share-link',
  { content_type: contentType }
);