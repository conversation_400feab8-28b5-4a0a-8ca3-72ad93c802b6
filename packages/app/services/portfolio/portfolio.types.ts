/**
 * PortfolioSide
 */
export enum PortfolioSide {
  BUY = 'BUY',
  SELL = 'SELL',
}

/**
 * Market
 */
export enum Market {
  US = 'US',
  HK = 'HK',
  CRYPTO = 'CRYPTO',
}

/**
 * PortfolioTxAddParam
 */
export interface PortfolioTxAddParam {
  /**
   * 市场
   */
  market: Market;
  /**
   * 代码
   */
  symbol: string;
  /**
   * 方向
   */
  side: PortfolioSide;
  /**
   * 数量
   */
  amount: number;
  /**
   * 价格
   */
  price: number;
  /**
   * 总花费
   */
  cost: number;
  /**
   * 说明
   */
  intro: string;
  /**
   * 时间戳
   */
  ts: number;
}

/**
 * PortfolioPosParam
 */
export interface PortfolioPosParam {
  /**
   * 仓位 id
   */
  pos_id: string;
}

/**
 * PerformanceChartParam
 */
export interface PerformanceChartParam {
  /**
   * 展示时间区间
   */
  days: 1 | 7 | 30 | 90 | 365;
}

/**
 * PortfolioChart
 */
export interface PortfolioChart {
  /**
   * 时间戳
   */
  t: number;
  /**
   * 持仓价值
   */
  v: string;
}

/**
 * PortfolioChartResp
 */
export interface PortfolioChartResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: PortfolioChart[] | null;
}

/**
 * GroupOverview
 */
export interface GroupOverview {
  /**
   * 总余额
   */
  total_value: string;
  /**
   * 24h余额变动
   */
  value_change_24h: string;
  /**
   * 24h余额变动比例
   */
  value_change_rate_24h: string;
  /**
   * 总利润
   */
  pnl_value: string;
  /**
   * 总利润率
   */
  pnl_rate: string;
  /**
   * BTC 24h 最高增益
   */
  btc_change_high_24h: string;
}

/**
 * GroupOverviewResp
 */
export interface GroupOverviewResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: GroupOverview | null;
}

/**
 * PositionItem
 */
export interface PositionItem {
  /**
   * 市场
   */
  market: Market;
  /**
   * 代码
   */
  symbol: string;
  /**
   * 名称
   */
  name: string;
  /**
   * 仓位 id
   */
  pos_id: string;
  /**
   * 总市值
   */
  market_cap: string;
  /**
   * 价格
   */
  price: string;
  /**
   * 24h价格涨跌幅
   */
  price_change_24h: string;
  /**
   * 7天价格涨跌幅
   */
  price_change_7d: string;
  /**
   * 初次持仓时间戳
   */
  holding_start_time: number;
  /**
   * 持仓量
   */
  pos: string;
  /**
   * 利润
   */
  pnl_value: string;
  /**
   * 利润率
   */
  pnl_rate: string;
}

/**
 * PositionsResp
 */
export interface PositionsResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: PositionItem[];
}

/**
 * TransactionItem
 */
export interface TransactionItem {
  /**
   * 市场
   */
  market: Market;
  /**
   * 代码
   */
  symbol: string;
  /**
   * 名称
   */
  name: string;
  /**
   * 时间戳毫秒
   */
  ts: number;
  /**
   * 方向
   */
  side: PortfolioSide;
  /**
   * 数量
   */
  amount: number;
  /**
   * 价格
   */
  price: number;
  /**
   * 总花费
   */
  cost: number;
  /**
   * 说明
   */
  intro: string;
}

/**
 * TransactionsResp
 */
export interface TransactionsResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: TransactionItem[] | null;
}

/**
 * ValidationError
 */
export interface ValidationError {
  /**
   * Location
   */
  loc: (string | number)[];
  /**
   * Message
   */
  msg: string;
  /**
   * Error Type
   */
  type: string;
}

/**
 * HTTPValidationError
 */
export interface HTTPValidationError {
  /**
   * Detail
   */
  detail: ValidationError[];
}

/**
 * CommonResponse
 */
export interface CommonResponse {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: any;
}

/**
 * AllowedContentTypes
 */
export type AllowedContentTypes = 'image/png' | 'image/jpeg';

/**
 * UploadImageLinkModel
 */
export interface UploadImageLinkModel {
  /**
   * Image Link
   * temp upload image url
   */
  image_link: string;
}

/**
 * UploadImageLinkResp
 */
export interface UploadImageLinkResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: UploadImageLinkModel;
}

/**
 * BooleanResult
 */
export interface BooleanResult {
  /**
   * Result
   */
  result: boolean;
}

/**
 * UserGuideStatusResp
 */
export interface UserGuideStatusResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: BooleanResult;
}