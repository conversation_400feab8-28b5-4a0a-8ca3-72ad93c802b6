/**
 * CommonResponse
 */
export interface CommonResponse {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: any;
}

/**
 * SupportExchange
 */
export interface SupportExchange {
  /**
   * Exchanges
   * 支持的交易所列表
   */
  exchanges: string[];
}

/**
 * SupportExchangeResp
 */
export interface SupportExchangeResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: SupportExchange;
}

/**
 * FearGreedIndex
 */
export interface FearGreedIndex {
  /**
   * Time List
   * 时间戳/毫秒
   */
  time_list: number[];
  /**
   * Btc Price List
   * btc 价格列表
   */
  btc_price_list: string[];
  /**
   * Index List
   * 恐惧贪婪指数
   */
  index_list: number[];
}

/**
 * FearGreedIndexResp
 */
export interface FearGreedIndexResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: FearGreedIndex | null;
}

/**
 * CoinsLiqHistoryParam
 */
export interface CoinsLiqHistoryParam {
  /**
   * Exchange List
   * 查询的交易所列表
   */
  exchange_list: string[];
  /**
   * Symbol
   * 指定币种
   */
  symbol: string;
  /**
   * Interval
   * 数据时间间隔，4h、6h、8h、12h、1d、1w
   */
  interval?: string | null;
  /**
   * Limit
   * 返回数据条数
   */
  limit?: number;
}

/**
 * CoinsLiquidationHistory
 */
export interface CoinsLiquidationHistory {
  /**
   * Long Liq Usd
   * 多单清算
   */
  long_liq_usd: string;
  /**
   * Short Liq Usd
   * 空单清算
   */
  short_liq_usd: string;
  /**
   * Time
   * 时间戳/秒
   */
  time: number;
}

/**
 * CoinsLiquidationHistoryResp
 */
export interface CoinsLiquidationHistoryResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: CoinsLiquidationHistory[];
}

/**
 * AHR999
 */
export interface AHR999 {
  /**
   * Date String
   * 日期字符串（格式：YYYY/MM/DD）
   */
  date_string: string;
  /**
   * Average Price
   * 当日的平均价格
   */
  average_price: string;
  /**
   * Ahr999 Value
   * AHR999 指数值
   */
  ahr999_value: string;
  /**
   * Current Value
   * 当日的当前值
   */
  current_value: string;
}

/**
 * AHR999Resp
 */
export interface AHR999Resp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: AHR999[];
}

/**
 * GrayscaleHolding
 */
export interface GrayscaleHolding {
  /**
   * Symbol
   * 币种代码，例如 ETH 代表以太坊
   */
  symbol: string;
  /**
   * Primary Market Price
   * 一级市场价格
   */
  primary_market_price: string;
  /**
   * Secondary Market Price
   * 二级市场价格
   */
  secondary_market_price: string;
  /**
   * Premium Rate
   * 溢价率（单位：%）
   */
  premium_rate: string;
  /**
   * Holdings Amount
   * 当前持仓数量（单位：币）
   */
  holdings_amount: string;
  /**
   * Holdings Usd
   * 当前持仓总市值（单位：USD）
   */
  holdings_usd: string;
  /**
   * Holdings Amount Change 30D
   * 最近30天持仓数量变化（单位：币）
   */
  holdings_amount_change_30d: string;
  /**
   * Holdings Amount Change 7D
   * 最近7天持仓数量变化（单位：币）
   */
  holdings_amount_change_7d: string;
  /**
   * Holdings Amount Change1D
   * 最近1天持仓数量变化（单位：币）
   */
  holdings_amount_change1d: string;
  /**
   * Close Time
   * 收盘时间（时间戳，单位：毫秒）
   */
  close_time: number;
  /**
   * Update Time
   * 更新时间（时间戳，单位：毫秒）
   */
  update_time: number;
}

/**
 * GrayscaleHoldingResp
 */
export interface GrayscaleHoldingResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: GrayscaleHolding[];
}

/**
 * GrayscalePremiumCoins
 */
export interface GrayscalePremiumCoins {
  /**
   * Symbol
   * 指定币种
   */
  symbol: string;
}

/**
 * MicroStrategyCost
 */
export interface MicroStrategyCost {
  /**
   * Date
   * 日期
   */
  date: string;
  /**
   * Btc Amount
   * 比特币总量
   */
  btc_amount: string;
  /**
   * Usd Value
   * 比特币美金市值
   */
  usd_value: string;
  /**
   * Cost Indicator
   * 微策略成本指标
   */
  cost_indicator: string;
}

/**
 * MicroStrategyCostResp
 */
export interface MicroStrategyCostResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: MicroStrategyCost[];
}

/**
 * LongTermHolderSupply
 */
export interface LongTermHolderSupply {
  /**
   * Date
   * 日期
   */
  date: string;
  /**
   * Btc Price
   * 比特币价格
   */
  btc_price: string;
  /**
   * Holders Hold Count
   * 长期持有者持有数量
   */
  holders_hold_count: string;
}

/**
 * LongTermHolderSupplyResp
 */
export interface LongTermHolderSupplyResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: LongTermHolderSupply[];
}

/**
 * GrayscaleHistoryCoins
 */
export interface GrayscaleHistoryCoins {
  /**
   * Symbol
   * 指定币种：BTC、ETH、ETC、BCH、LTC、ZEC、XLM、ZEN、BAT、LINK、MANA、FIL、LPT
   */
  symbol: string;
}

/**
 * GrayscalePosition
 */
export interface GrayscalePosition {
  /**
   * Date
   * 日期
   */
  date: string;
  /**
   * Coin Price
   * 比特币价格
   */
  coin_price: string;
  /**
   * Hold Count
   * 长期持有者持有数量
   */
  hold_count: string;
}

/**
 * GrayscalePositionResp
 */
export interface GrayscalePositionResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: GrayscalePosition[];
}

/**
 * BTCLinnerLiqMapParam
 */
export interface BTCLinnerLiqMapParam {
  /**
   * Symbol
   * 交易所标的
   */
  symbol: 'Binance_BTC_USDT' | 'Gate_BTC_USDT' | 'Bybit_BTC_USDT' | 'Hyperliquid_BTC_USD' | 'OKX_BTC_USDT' | 'Bitget_BTC_USDT' | 'Binance_BTC_USD' | 'Bitget_BTC_USD' | 'Bybit_BTC_USD' | 'OKX_BTC_USD' | 'Coinbase_BTC_USDC';
  /**
   * Interval
   * 指定周期
   */
  interval: 1 | 7 | 30;
}

/**
 * CoinsLiqMapParam
 */
export interface CoinsLiqMapParam {
  /**
   * Symbol
   * 标的
   */
  symbol: 'BTC' | 'ETH' | 'SOL' | 'XRP' | 'DOGE' | 'SUI' | 'HYPE' | 'LTC' | 'TRUMP';
  /**
   * Interval
   * 指定周期
   */
  interval: 1 | 7 | 30;
}

/**
 * HyperLiqMapParam
 */
export interface HyperLiqMapParam {
  /**
   * Symbol
   * 标的
   */
  symbol: 'BTC' | 'ETH' | 'LINK' | 'HYPE' | 'XRP' | 'SOL' | 'ADA';
}

/**
 * BooleanResult
 */
export interface BooleanResult {
  /**
   * Result
   */
  result: boolean;
}

/**
 * ValidationError
 */
export interface ValidationError {
  /**
   * Location
   */
  loc: (string | number)[];
  /**
   * Message
   */
  msg: string;
  /**
   * Error Type
   */
  type: string;
}

/**
 * HTTPValidationError
 */
export interface HTTPValidationError {
  /**
   * Detail
   */
  detail: ValidationError[];
} 