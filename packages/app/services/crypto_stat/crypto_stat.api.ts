import api from '../api';
import type {
  SupportExchangeResp,
  FearGreedIndexResp,
  CoinsLiqHistoryParam,
  CoinsLiquidationHistoryResp,
  AHR999Resp,
  GrayscaleHoldingResp,
  GrayscalePremiumCoins,
  CommonResponse,
  MicroStrategyCostResp,
  LongTermHolderSupplyResp,
  GrayscaleHistoryCoins,
  GrayscalePositionResp,
  BTCLinnerLiqMapParam,
  CoinsLiqMapParam,
  HyperLiqMapParam
} from './crypto_stat.types';

/**
 * 支持的交易所
 * POST /api/v1/public/stat/crypto/supported-exchange
 * 接口ID：支持的交易所_api_v1_public_stat_crypto_supported_exchange_post
 */
export const getSupportedExchanges = async () => api.post<SupportExchangeResp>(
  '/api/v1/public/stat/crypto/supported-exchange'
);

/**
 * 恐惧&贪婪指数
 * POST /api/v1/public/stat/crypto/fear-greed-index
 * 接口ID：恐惧_贪婪指数_api_v1_public_stat_crypto_fear_greed_index_post
 */
export const getFearGreedIndex = async () => api.post<FearGreedIndexResp>(
  '/api/v1/public/stat/crypto/fear-greed-index'
);

/**
 * 币种爆仓历史
 * POST /api/v1/public/stat/crypto/coins-liquidation-history
 * 接口ID：币种爆仓历史_api_v1_public_stat_crypto_coins_liquidation_history_post
 */
export const getCoinsLiquidationHistory = async (params: CoinsLiqHistoryParam) => api.post<CoinsLiquidationHistoryResp>(
  '/api/v1/public/stat/crypto/coins-liquidation-history',
  params
);

/**
 * 比特币 Ahr999 指标
 * POST /api/v1/public/stat/crypto/index-ahr999
 * 接口ID：比特币_ahr999_指标_api_v1_public_stat_crypto_index_ahr999_post
 */
export const getAhr999Index = async () => api.post<AHR999Resp>(
  '/api/v1/public/stat/crypto/index-ahr999'
);

/**
 * 牛市逃顶指数
 * POST /api/v1/public/stat/crypto/bull-market-peak-indicator
 * 接口ID：牛市逃顶指数_api_v1_public_stat_crypto_bull_market_peak_indicator_post
 */
export const getBullMarketPeakIndicator = async () => api.post<CommonResponse>(
  '/api/v1/public/stat/crypto/bull-market-peak-indicator'
);

/**
 * 灰度持仓列表
 * POST /api/v1/public/stat/crypto/grayscale-holdings-list
 * 接口ID：灰度持仓列表_api_v1_public_stat_crypto_grayscale_holdings_list_post
 */
export const getGrayscaleHoldingsList = async () => api.post<GrayscaleHoldingResp>(
  '/api/v1/public/stat/crypto/grayscale-holdings-list'
);

/**
 * 灰度溢价历史
 * POST /api/v1/public/stat/crypto/grayscale-premium-history
 * 接口ID：灰度溢价历史_api_v1_public_stat_crypto_grayscale_premium_history_post
 */
export const getGrayscalePremiumHistory = async (params: GrayscalePremiumCoins) => api.post<CommonResponse>(
  '/api/v1/public/stat/crypto/grayscale-premium-history',
  params
);

/**
 * Etf溢价/折扣历史
 * POST /api/v1/public/stat/crypto/etf-bitcoin-premium-discount-history
 * 接口ID：ETF溢价_折扣历史_api_v1_public_stat_crypto_etf_bitcoin_premium_discount_history_post
 */
export const getEtfBitcoinPremiumDiscountHistory = async () => api.post<CommonResponse>(
  '/api/v1/public/stat/crypto/etf-bitcoin-premium-discount-history'
);

/**
 * 微策略成本
 * POST /api/v1/public/stat/crypto/micro-strategy-cost
 * 接口ID：微策略成本_api_v1_public_stat_crypto_micro_strategy_cost_post
 */
export const getMicroStrategyCost = async () => api.post<MicroStrategyCostResp>(
  '/api/v1/public/stat/crypto/micro-strategy-cost'
);

/**
 * 长期持有者持有比特币数量
 * POST /api/v1/public/stat/crypto/long-term-holder-supply
 * 接口ID：长期持有者持有比特币数量_api_v1_public_stat_crypto_long_term_holder_supply_post
 */
export const getLongTermHolderSupply = async () => api.post<LongTermHolderSupplyResp>(
  '/api/v1/public/stat/crypto/long-term-holder-supply'
);

/**
 * 灰度基金持仓币种
 * POST /api/v1/public/stat/crypto/grayscale-positions-symbols
 * 接口ID：灰度基金持仓币种_api_v1_public_stat_crypto_grayscale_positions_symbols_post
 */
export const getGrayscalePositionsSymbols = async () => api.post<CommonResponse>(
  '/api/v1/public/stat/crypto/grayscale-positions-symbols'
);

/**
 * 灰度基金持仓历史
 * POST /api/v1/public/stat/crypto/grayscale-positions-history
 * 接口ID：灰度基金持仓历史_api_v1_public_stat_crypto_grayscale_positions_history_post
 */
export const getGrayscalePositionsHistory = async (params: GrayscaleHistoryCoins) => api.post<GrayscalePositionResp>(
  '/api/v1/public/stat/crypto/grayscale-positions-history',
  params
);

/**
 * 交易所 Btc 永续合约清算地图支持的品种
 * POST /api/v1/public/stat/crypto/exchange-liquidation-symbols
 * 接口ID：交易所_BTC_永续合约清算地图支持的品种_api_v1_public_stat_crypto_exchange_liquidation_symbols_post
 */
export const getExchangeLiquidationSymbols = async () => api.post<CommonResponse>(
  '/api/v1/public/stat/crypto/exchange-liquidation-symbols'
);

/**
 * 交易所 Btc 永续合约清算地图
 * POST /api/v1/public/stat/crypto/btc-linner-liquidation-map
 * 接口ID：交易所_BTC_永续合约清算地图_api_v1_public_stat_crypto_btc_linner_liquidation_map_post
 */
export const getBtcLinnerLiquidationMap = async (params: BTCLinnerLiqMapParam) => api.post<CommonResponse>(
  '/api/v1/public/stat/crypto/btc-linner-liquidation-map',
  params
);

/**
 * 币种清算地图支持的币种
 * POST /api/v1/public/stat/crypto/coins-liquidation-symbols
 * 接口ID：币种清算地图支持的币种_api_v1_public_stat_crypto_coins_liquidation_symbols_post
 */
export const getCoinsLiquidationSymbols = async () => api.post<CommonResponse>(
  '/api/v1/public/stat/crypto/coins-liquidation-symbols'
);

/**
 * 币种清算地图
 * POST /api/v1/public/stat/crypto/coins-liquidation-map
 * 接口ID：币种清算地图_api_v1_public_stat_crypto_coins_liquidation_map_post
 */
export const getCoinsLiquidationMap = async (params: CoinsLiqMapParam) => api.post<CommonResponse>(
  '/api/v1/public/stat/crypto/coins-liquidation-map',
  params
);

/**
 * Hyperliquid 清算地图支持的币种
 * POST /api/v1/public/stat/crypto/hyper-liquidation-symbols
 * 接口ID：Hyperliquid_清算地图支持的币种_api_v1_public_stat_crypto_hyper_liquidation_symbols_post
 */
export const getHyperLiquidationSymbols = async () => api.post<CommonResponse>(
  '/api/v1/public/stat/crypto/hyper-liquidation-symbols'
);

/**
 * Hyperliquid 清算地图
 * POST /api/v1/public/stat/crypto/hyper-liquidation-map
 * 接口ID：Hyperliquid_清算地图_api_v1_public_stat_crypto_hyper_liquidation_map_post
 */
export const getHyperLiquidationMap = async (params: HyperLiqMapParam) => api.post<CommonResponse>(
  '/api/v1/public/stat/crypto/hyper-liquidation-map',
  params
); 