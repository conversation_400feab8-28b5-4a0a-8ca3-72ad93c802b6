import api from '../api';
import type { AuthEmailSendParam, AuthEmailConfirmParam, CommonResponse, AuthSignInResp } from './auth.types';

/**
 * Email Authorize Send
 * POST /auth/email/send
 * 接口ID：email_authorize_send_auth_email_send_post
 */
export const sendEmailAuth = async (params: AuthEmailSendParam, realIp?: string) => {
  const headers = realIp ? { 'X-Real-Ip': realIp } : undefined;
  return api.post<CommonResponse>(
    '/auth/email/send',
    params,
    { headers }
  );
};

/**
 * Email Authorize Signup/Signin
 * POST /auth/email/signup
 * 接口ID：email_authorize_signup_signin_auth_email_signup_post
 */
export const confirmEmailAuth = async (params: AuthEmailConfirmParam, realIp?: string) => {
  const headers = realIp ? { 'X-Real-Ip': realIp } : undefined;
  return api.post<AuthSignInResp>(
    '/auth/email/signup',
    params,
    { headers }
  );
}; 