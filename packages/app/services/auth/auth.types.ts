/**
 * AuthEmailSendParam
 */
export interface AuthEmailSendParam {
    /**
     * Inviter
     * 邀请码
     */
    inviter?: string | null;
    /**
     * Email
     * 用户邮箱
     */
    email: string;
}

/**
 * AuthEmailConfirmParam
 */
export interface AuthEmailConfirmParam {
    /**
     * Code
     * 验证码
     */
    code: string;
}

/**
 * AuthSignInfo
 * 登陆
 */
export interface AuthSignInfo {
    /**
     * Access Token
     * access_token
     */
    access_token: string;
    /**
     * Email
     * email
     */
    email: string;
}

/**
 * AuthSignInResp
 */
export interface AuthSignInResp {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: AuthSignInfo;
}

/**
 * CommonResponse
 */
export interface CommonResponse {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: any;
}

/**
 * ValidationError
 */
export interface ValidationError {
    /**
     * Location
     */
    loc: (string | number)[];
    /**
     * Message
     */
    msg: string;
    /**
     * Error Type
     */
    type: string;
}

/**
 * HTTPValidationError
 */
export interface HTTPValidationError {
    /**
     * Detail
     */
    detail: ValidationError[];
} 