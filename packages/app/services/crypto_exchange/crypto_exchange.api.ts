import api from '../api';
import type { 
  ExchangeListResp, 
  ExchangeDetailResp, 
  ExchangeMarketsResp, 
  ExchangeVolumeChartResp, 
  ExchangeAssetsResp,
  ExchangeParam,
  ExchangeMarketsParam
} from './crypto_exchange.types';

/**
 * 交易所列表
 * POST /api/v1/public/crypto/exchange-list
 * 接口ID：交易所列表_api_v1_public_crypto_exchange_list_post
 */
export const getExchangeList = async () => api.post<ExchangeListResp>(
  '/api/v1/public/crypto/exchange-list'
);

/**
 * 交易所详情
 * POST /api/v1/public/crypto/exchange-detail
 * 接口ID：交易所详情_api_v1_public_crypto_exchange_detail_post
 * @param exchangeName 交易所名称
 */
export const getExchangeDetail = async (exchangeName: string) => api.post<ExchangeDetailResp>(
  '/api/v1/public/crypto/exchange-detail',
  { exchange_name: exchangeName }
);

/**
 * 现货交易对列表
 * POST /api/v1/public/crypto/exchange-spot-markets
 * 接口ID：现货交易对列表_api_v1_public_crypto_exchange_spot_markets_post
 * @param exchangeName 交易所名称
 * @param page 页码，0开始
 */
export const getExchangeSpotMarkets = async (exchangeName: string, page = 0) => api.post<ExchangeMarketsResp>(
  '/api/v1/public/crypto/exchange-spot-markets',
  { exchange_name: exchangeName, page }
);

/**
 * 交易所成交量图表
 * POST /api/v1/public/crypto/exchange-volume-chart
 * 接口ID：交易所成交量图表_api_v1_public_crypto_exchange_volume_chart_post
 * @param exchangeName 交易所名称
 */
export const getExchangeVolumeChart = async (exchangeName: string, days: number) => api.post<ExchangeVolumeChartResp>(
  '/api/v1/public/crypto/exchange-volume-chart',
  { exchange_name: exchangeName, days }
);

/**
 * 交易所资产
 * POST /api/v1/public/crypto/exchange-assets
 * 接口ID：交易所资产_api_v1_public_crypto_exchange_assets_post
 * @param exchangeName 交易所名称
 */
export const getExchangeAssets = async (exchangeName: string) => api.post<ExchangeAssetsResp>(
  '/api/v1/public/crypto/exchange-assets',
  { exchange_name: exchangeName }
); 