/**
 * ExchangeOverview
 */
export interface ExchangeOverview {
  /**
   * Name
   * 名称
   */
  name: string;
  /**
   * Logo
   * logo
   */
  logo: string;
  /**
   * Maker Fee
   * 挂单费率
   */
  maker_fee: number;
  /**
   * Taker Fee
   * 吃单费率
   */
  taker_fee: number;
  /**
   * Fiats
   * 支持的法币
   */
  fiats?: string[] | null;
  /**
   * Weekly Visits
   * 周访问量
   */
  weekly_visits: number;
  /**
   * Spot Volume Usd
   * 现货 24h 成交额
   */
  spot_volume_usd: string;
  /**
   * Btc Balance
   * BTC 钱包余额
   */
  btc_balance: string;
  /**
   * Symbol Amount
   * 交易对数量
   */
  symbol_amount?: number | null;
  /**
   * Trust Score
   * 信用评分
   */
  trust_score?: number | null;
}

/**
 * ExchangeParam
 * 交易所请求参数
 */
export interface ExchangeParam {
  /**
   * Exchange Name
   * 交易所名称
   */
  exchange_name: string;
}

/**
 * ExchangeListResp
 */
export interface ExchangeListResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: ExchangeOverview[];
}

/**
 * TradeInfo
 */
export interface TradeInfo {
  /**
   * Spot Volume 24h Usd
   * 24h现货成交量
   */
  spot_volume_24h_usd: string | null;
  /**
   * Future Volume 24h Usd
   * 24h合约成交量
   */
  future_volume_24h_usd: string | null;
  /**
   * Future Position Usd
   * 合约持仓量
   */
  future_position_usd: string | null;
}

/**
 * LiqInfo
 */
export interface LiqInfo {
  /**
   * Long Liq 24H Usd
   * 24h多单爆仓
   */
  long_liq_24h_usd: string | null;
  /**
   * Short Liq 24H Usd
   * 24h空单爆仓
   */
  short_liq_24h_usd: string | null;
}

/**
 * ExchangeInfo
 */
export interface ExchangeInfo {
  /**
   * Name
   * 名称
   */
  name: string;
  /**
   * Logo
   * logo
   */
  logo: string;
  /**
   * Spot Volume 24H Usd
   * 24h现货成交量
   */
  spot_volume_24h_usd: string | null;
  /**
   * Total Asset Usd
   * 总资产
   */
  total_asset_usd: string | null;
}

/**
 * MarketInfo
 */
export interface MarketInfo {
  /**
   * Spot Volume 24H Usd
   * 24h现货成交量
   */
  spot_volume_24h_usd: string | null;
  /**
   * Trust Score
   * 信用评级
   */
  trust_score: string | null;
  /**
   * Currency Amount
   * 币种数量
   */
  currency_amount: number;
  /**
   * Pair Amount
   * 交易对数量
   */
  pair_amount: number;
}

/**
 * ExchangeDetail
 */
export interface ExchangeDetail {
  /**
   * Trade Info
   */
  trade_info: TradeInfo;
  /**
   * Liq Info
   */
  liq_info: LiqInfo;
  /**
   * Exchange Info
   */
  exchange_info: ExchangeInfo;
  /**
   * Market Info
   */
  market_info: MarketInfo;
  /**
   * Name
   */
  name: string;
  /**
   * Logo
   */
  logo: string;
}

/**
 * ExchangeDetailResp
 */
export interface ExchangeDetailResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: ExchangeDetail;
}

/**
 * ExchangeMarketsParam
 */
export interface ExchangeMarketsParam {
  /**
   * Exchange Name
   * 交易所名称
   */
  exchange_name: string;
  /**
   * Page
   * 页码，0 开始
   */
  page: number;
}

/**
 * MarketSymbol
 */
export interface MarketSymbol {
  /**
   * Base
   * base
   */
  base: string;
  /**
   * Quota
   * quotq
   */
  quota: string;
  /**
   * Price
   * 价格
   */
  price: string;
  /**
   * Volume 24H Usd
   * 24 成交量
   */
  volume_24h_usd: string;
  /**
   * Bid Ask Spread Percentage
   * 价差
   */
  bid_ask_spread_percentage: string;
  /**
   * Cost To Move Up Usd
   * 向上2%深度
   */
  cost_to_move_up_usd: string;
  /**
   * Cost To Move Down Usd
   * 向下2%深度
   */
  cost_to_move_down_usd: string;
  /**
   * Trust Score
   * 信任分（颜色）
   */
  trust_score: string;
  /**
   * Last Sync
   * 最后更新时间
   */
  last_sync: string;
}

/**
 * ExchangeMarketsResp
 */
export interface ExchangeMarketsResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: MarketSymbol[];
}

/**
 * ExchangeVolume
 */
export interface ExchangeVolume {
  /**
   * T
   * 时间戳/毫秒
   */
  t: number;
  /**
   * V
   * 成交量（单位 BTC）
   */
  v: string;
}

/**
 * ExchangeVolumeChartResp
 */
export interface ExchangeVolumeChartResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: ExchangeVolume[];
}

/**
 * ExchangeAsset
 */
export interface ExchangeAsset {
  /**
   * Wallet Address
   * 地址
   */
  wallet_address: string;
  /**
   * Balance
   * 余额
   */
  balance: string;
  /**
   * Balance Usd
   * 余额价值
   */
  balance_usd: string;
  /**
   * Symbol
   * 币种符号
   */
  symbol: string;
  /**
   * Assets Name
   * 币种全称
   */
  assets_name: string;
  /**
   * Price
   * 价格
   */
  price: string;
}

/**
 * ExchangeAssetsResp
 */
export interface ExchangeAssetsResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: ExchangeAsset[];
}

/**
 * CommonResponse
 */
export interface CommonResponse {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: any;
} 