import api from '../api';
import type { 
  AssetBaseParam, 
  WatchListOrderParam, 
  WatchListItemResp, 
  WatchListStatusResp, 
  CommonResponse,
  WatchListBatchAddParam
} from './watchlist.types';

/**
 * 添加 Watchlist
 * POST /api/v1/private/watchlist/add
 * 接口ID：添加_watchlist_api_v1_private_watchlist_add_post
 */
export const addWatchlist = async (params: AssetBaseParam) => api.post<CommonResponse>(
  '/api/v1/private/watchlist/add',
  params
);

/**
 * 批量添加 Watchlist
 * POST /api/v1/private/watchlist/batch-add
 * 接口ID：批量添加_watchlist_api_v1_private_watchlist_batch_add_post
 */
export const batchAddWatchlist = async (params: WatchListBatchAddParam) => api.post<CommonResponse>(
  '/api/v1/private/watchlist/batch-add',
  params
);

/**
 * 取消 Watchlist
 * POST /api/v1/private/watchlist/remove
 * 接口ID：取消_watchlist_api_v1_private_watchlist_remove_post
 */
export const removeWatchlist = async (params: AssetBaseParam) => api.post<CommonResponse>(
  '/api/v1/private/watchlist/remove',
  params
);

/**
 * Watchlist 列表
 * POST /api/v1/private/watchlist/list
 * 接口ID：watchlist_列表_api_v1_private_watchlist_list_post
 */
export const getWatchlist = async () => api.post<WatchListItemResp>(
  '/api/v1/private/watchlist/list'
);

/**
 * 指定资产是否在 Watchlist 中
 * POST /api/v1/private/watchlist/status
 * 接口ID：指定资产是否在_watchlist_中_api_v1_private_watchlist_status_post
 */
export const getWatchlistStatus = async (params: AssetBaseParam) => api.post<WatchListStatusResp>(
  '/api/v1/private/watchlist/status',
  params
);

/**
 * 更新 Watchlist 顺序
 * POST /api/v1/private/watchlist/order
 * 接口ID：更新_watchlist_顺序_api_v1_private_watchlist_order_post
 */
export const updateWatchlistOrder = async (params: WatchListOrderParam) => api.post<CommonResponse>(
  '/api/v1/private/watchlist/order',
  params
); 