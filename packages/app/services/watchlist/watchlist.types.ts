/**
 * Market
 */
export enum Market {
    US = 'US',
    HK = 'HK',
    CRYPTO = 'CRYPTO'
}

/**
 * AssetBase
 */
export interface AssetBase {
    /**
     * Market
     */
    market: Market;
    /**
     * Symbol
     * symbol
     */
    symbol: string;
}

/**
 * AssetBaseParam
 */
export interface AssetBaseParam {
    /**
     * Market
     */
    market: Market;
    /**
     * Symbol
     * symbol
     */
    symbol: string;
}

/**
 * WatchListBatchAddParam
 */
export interface WatchListBatchAddParam {

    /**
     * Assets
     * 批量添加的资产列表
     */
    assets: AssetBaseParam[];
}

/**
 * WatchListOrderParam
 */
export interface WatchListOrderParam {
    /**
     * Market
     */
    market: Market;
    /**
     * Symbol
     * symbol
     */
    symbol: string;
    /**
     * New Order
     * 顺序
     */
    new_order: number;
}

/**
 * WatchListItem
 */
export interface WatchListItem {
    /**
     * Market
     */
    market: Market;
    /**
     * Symbol
     * symbol
     */
    symbol: string;
    /**
     * Name
     * 名称
     */
    name: string;
    /**
     * Price
     * 价格
     */
    price: string;
    /**
     * Change Rate
     * 价格涨跌幅
     */
    change_rate: string;
    /**
     * Sort Order
     * 排序
     */
    sort_order: number;
    /**
     * Market Cap
     * 市值
     */
    market_cap: string;
}

/**
 * WatchListItemResp
 */
export interface WatchListItemResp {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: WatchListItem[];
}

/**
 * BooleanResult
 */
export interface BooleanResult {
    /**
     * Result
     */
    result: boolean;
}

/**
 * WatchListStatusResp
 */
export interface WatchListStatusResp {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: BooleanResult;
}

/**
 * CommonResponse
 */
export interface CommonResponse {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: any;
}

/**
 * ValidationError
 */
export interface ValidationError {
    /**
     * Location
     */
    loc: (string | number)[];
    /**
     * Message
     */
    msg: string;
    /**
     * Error Type
     */
    type: string;
}

/**
 * HTTPValidationError
 */
export interface HTTPValidationError {
    /**
     * Detail
     */
    detail: ValidationError[];
} 