import api from '../api';
import type { 
  CommonResponse, 
  ReferralItemResp, 
  WaitListStatusResp, 
  WaitListInfoResp 
} from './waitlist.types';

/**
 * 加入 Waitlist
 * POST /api/v1/private/waitlist/join
 * 接口ID：加入_waitlist_api_v1_private_waitlist_join_post
 */
export const joinWaitlist = async () => api.post<CommonResponse>(
  '/api/v1/private/waitlist/join'
);

/**
 * Waitlist 邀请列表
 * POST /api/v1/private/waitlist/list
 * 接口ID：waitlist_邀请列表_api_v1_private_waitlist_list_post
 */
export const getWaitlistInviteList = async () => api.post<ReferralItemResp>(
  '/api/v1/private/waitlist/list'
);

/**
 * 查询本人是否已经加入 Waitlist
 * POST /api/v1/private/waitlist/status
 * 接口ID：查询本人是否已经加入_waitlist_api_v1_private_waitlist_status_post
 */
export const getWaitlistStatus = async () => api.post<WaitListStatusResp>(
  '/api/v1/private/waitlist/status'
);

/**
 * Waitlist 活动信息
 * POST /api/v1/public/waitlist/info
 * 接口ID：waitlist_活动信息_api_v1_public_waitlist_info_post
 */
export const getWaitlistInfo = async () => api.post<WaitListInfoResp>(
  '/api/v1/public/waitlist/info'
); 