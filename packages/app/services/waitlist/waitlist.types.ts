/**
 * CommonResponse
 */
export interface CommonResponse {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: any;
}

/**
 * BooleanResult
 */
export interface BooleanResult {
    /**
     * Result
     */
    result: boolean;
}

/**
 * WaitListStatusResp
 */
export interface WaitListStatusResp {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: BooleanResult;
}

/**
 * ReferralItem
 */
export interface ReferralItem {
    /**
     * 被邀请用户邮箱
     */
    email: string;
    /**
     * 被邀请用户加入 waitlist 时间
     */
    join_ts: string;
}

/**
 * ReferralItemResp
 */
export interface ReferralItemResp {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: ReferralItem[];
}

/**
 * WaitListInfo
 */
export interface WaitListInfo {
    /**
     * 总数量
     */
    total: number;
}

/**
 * WaitListInfoResp
 */
export interface WaitListInfoResp {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: WaitListInfo;
}

/**
 * ValidationError
 */
export interface ValidationError {
    /**
     * Location
     */
    loc: (string | number)[];
    /**
     * Message
     */
    msg: string;
    /**
     * Error Type
     */
    type: string;
}

/**
 * HTTPValidationError
 */
export interface HTTPValidationError {
    /**
     * Detail
     */
    detail: ValidationError[];
} 