import api from '../api';
import type { GoogleOAuthSignInParam, AuthSignInResp } from './oauth.types';

/**
 * Google Oauth
 * POST /oauth/google
 * 接口ID：google_oauth_oauth_google_post
 */
export const googleOAuth = async (params: GoogleOAuthSignInParam) => api.post<AuthSignInResp>(
  '/oauth/google/credential',
  params
);
export const googleOAuthToken = async (params: {
  auth_token: string,
  inviter?: string | null;
}) => api.post<AuthSignInResp>(
  '/oauth/google/token',
  params
);