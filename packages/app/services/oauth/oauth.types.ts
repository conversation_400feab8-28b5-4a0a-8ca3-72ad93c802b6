/**
 * GoogleOAuthSignInParam
 */
export interface GoogleOAuthSignInParam {
    /**
     * Inviter
     * 邀请码
     */
    inviter?: string | null;
    /**
     * Google Credential
     * google credential
     */
    google_credential: string;
}

/**
 * AuthSignInfo
 * 登陆
 */
export interface AuthSignInfo {
    /**
     * Access Token
     * access_token
     */
    access_token: string;
    /**
     * Email
     * email
     */
    email: string;
}

/**
 * AuthSignInResp
 */
export interface AuthSignInResp {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: AuthSignInfo;
}

/**
 * ValidationError
 */
export interface ValidationError {
    /**
     * Location
     */
    loc: (string | number)[];
    /**
     * Message
     */
    msg: string;
    /**
     * Error Type
     */
    type: string;
}

/**
 * HTTPValidationError
 */
export interface HTTPValidationError {
    /**
     * Detail
     */
    detail: ValidationError[];
} 