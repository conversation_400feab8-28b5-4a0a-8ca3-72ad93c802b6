import api from '../api';
import { AliKlineInterval, Market } from './kline.types';
import type { KlineResp, KLineDataItem, MarketStatusResp } from './kline.types';

/**
 * Kline
 * GET /api/v1/public/kline
 * 接口ID：kline_api_v1_public_kline_get
 */
export const getKline = async (
  market: Market,
  symbol: number | string,
  interval: AliKlineInterval,
  limit: number
) => api.get<KlineResp>(
  '/api/v1/public/kline',
  { market, symbol, interval, limit }
); 

/**
 * Market Status
 * GET /api/v1/public/market-status
 * 接口ID：kline_api_v1_public_market_status_get
 */
export const getMarketStatus = async () => api.get<MarketStatusResp>(
  '/api/v1/public/market-status'
);

/**
 * 生成随机价格，在基准价格的基础上上下浮动
 * @param basePrice 基准价格
 * @param volatility 波动范围百分比
 */
const generateRandomPrice = (basePrice: number, volatility = 0.05): string => {
  const change = basePrice * volatility * (Math.random() * 2 - 1);
  return (basePrice + change).toFixed(5);
};

/**
 * Kline Mock 方法
 * 生成模拟的 K 线数据以便前端开发和测试
 */
export const getKlineMock = async (
  market: Market,
  symbol: number,
  interval: AliKlineInterval,
  limit: number
): Promise<KLineDataItem[]> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // 根据市场和品种生成基准价格（模拟数据）
  let basePrice: number;
  
  if (market === Market.CRYPTO) {
    basePrice = 30000 + Math.random() * 5000; // 加密货币价格范围
  } else if (market === Market.US) {
    basePrice = 200 + Math.random() * 300; // 美股价格范围
  } else {
    basePrice = 50 + Math.random() * 150; // 港股价格范围
  }
  
  // 生成 K 线数据
  const now = Date.now();
  const oneDayMs = 24 * 60 * 60 * 1000;
  const klineData: KLineDataItem[] = [];
  
  for (let i = 0; i < limit; i++) {
    // 计算当前时间戳（向前推 i 天）
    const timestamp = Math.floor((now - i * oneDayMs) / 1000).toString();
    
    // 生成价格数据（开盘价、最高价、最低价、收盘价）
    const openPrice = generateRandomPrice(basePrice);
    const closePrice = generateRandomPrice(basePrice);
    
    // 确保最高价和最低价合理
    const highPrice = generateRandomPrice(Math.max(Number(openPrice), Number(closePrice)) * 1.01);
    const lowPrice = generateRandomPrice(Math.min(Number(openPrice), Number(closePrice)) * 0.99);
    
    // 成交量和成交金额
    const volume = Math.floor(10000 + Math.random() * 1000000).toString();
    const amount = Math.floor(Number(volume) * Number(closePrice)).toString();
    
    // 添加当天的 K 线数据
    klineData.push([timestamp, openPrice, highPrice, lowPrice, closePrice, volume, amount]);
    
    // 根据前一天收盘价微调基准价格，模拟价格趋势
    basePrice = Number(closePrice) * (1 + (Math.random() * 0.02 - 0.01));
  }
  
  // 返回模拟的 K 线数据（按时间排序，最近的日期在前）
  return klineData.reverse();
}; 