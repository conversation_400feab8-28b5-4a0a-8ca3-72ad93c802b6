/**
 * AliKlineInterval
 */
export enum AliKlineInterval {
  MIN_5 = "5m",
  MIN_30 = "30m",
  HOUR_1 = "1h",
  HOUR_2 = "2h",
  DAY_1 = "1d",
  WEEK_1 = "1w",
  MONTH_1 = "1M"
}

/**
 * Market
 */
export enum Market {
  US = "US",
  HK = "HK",
  CRYPTO = "CRYPTO"
}

/**
 * K线数据格式
 * [开盘时间戳, 开盘价, 最高价, 最低价, 收盘价, 成交数量, 成交金额]
 * 例如: ["1744387200","334.60000","335.20000","333.60000","333.80000","1194940","399359985"]
 */
export type KLineDataItem = [string, string, string, string, string, string, string];


/**
 * KlineResp
 */
export interface KlineResp {
  /**
   * Status
   */
  status: number;

  /**
   * Msg
   */
  msg: string;

  /**
   * Data
   * 开盘时间戳, 开盘价, 最高价, 最低价, 收盘价, 成交数量, 成交金额
   * 例如: [["1744387200","334.60000","335.20000","333.60000","333.80000","1194940","399359985"]]
   */
  data?: KLineDataItem[];
}

/**
 * MarketStatus
 */
export interface MarketStatus {
  /**
   * Market
   */
  market: Market;
  /**
   * Status
   */
  status: boolean;
}

/**
 * MarketStatusResp
 */
export interface MarketStatusResp {
  /**
   * Status
   */
  status: number;
  /**
   * Msg
   */
  msg: string;
  /**
   * Data
   */
  data: MarketStatus[];
}

/**
 * ValidationError
 */
export interface ValidationError {
  /**
   * Location
   */
  loc: (string | number)[];

  /**
   * Message
   */
  msg: string;

  /**
   * Error Type
   */
  type: string;
}

/**
 * HTTPValidationError
 */
export interface HTTPValidationError {
  /**
   * Detail
   */
  detail: ValidationError[];
} 