import api from '../api';
import { SocialPlatform } from './media.types';
import type { 
  Market, 
  SocialMediaSort, 
  SocialMediaResp, 
  FollowKolParam,
  CommonResponse,
  FollowingKolResp,
  SocialMediaItem,
  SocialMediaModel
} from './media.types';

/**
 * 社交媒体消息
 * GET /api/v1/public/media/social-medias
 * 接口ID：社交媒体消息_api_v1_public_media_social_medias_get
 */
export const getSocialMedias = async (
  sort: SocialMediaSort, 
  limit?: number, 
  cursor?: string | null,
  market?: Market, 
  symbol?: string
) => {
  const response = await api.get<SocialMediaResp>(
    '/api/v1/public/media/social-medias',
    { sort, limit, cursor, market, symbol }
  );
  
  // 处理响应，确保每个item都有author_profile_platform字段
  return response;
};

/**
 * 关注的 Kol 的媒体消息
 * GET /api/v1/private/media/following-medias
 * 接口ID：关注的_kol_的媒体消息_api_v1_private_media_following_medias_get
 */
export const getFollowingMedias = async (
  sort: SocialMediaSort,
  limit?: number,
  cursor?: string | null,
  market?: Market,
  symbol?: string
) => {
  const response = await api.get<SocialMediaResp>(
    '/api/v1/private/media/following-medias',
    { sort, limit, cursor, market, symbol }
  );
  
  // 处理响应，确保每个item都有author_profile_platform字段
  return response;
};

/**
 * 关注 Kol
 * POST /api/v1/private/media/follow-kol
 * 接口ID：关注_kol_api_v1_private_media_follow_kol_post
 */
export const followKol = async (params: FollowKolParam) => api.post<CommonResponse>(
  '/api/v1/private/media/follow-kol',
  params
);

/**
 * 取消关注 Kol
 * POST /api/v1/private/media/unfollow-kol
 * 接口ID：取消关注_kol_api_v1_private_media_unfollow_kol_post
 */
export const unfollowKol = async (params: FollowKolParam) => api.post<CommonResponse>(
  '/api/v1/private/media/unfollow-kol',
  params
);

/**
 * 关注中的 Kol 列表
 * POST /api/v1/private/media/following-kol
 * 接口ID：关注中的_kol_列表_api_v1_private_media_following_kol_post
 */
export const getFollowingKol = async () => api.post<FollowingKolResp>(
  '/api/v1/private/media/following-kol'
); 