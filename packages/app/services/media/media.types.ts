/**
 * Market
 */
export enum Market {
    US = 'US',
    HK = 'HK',
    CRYPTO = 'CRYPTO'
}

/**
 * SocialMediaSort
 */
export enum SocialMediaSort {
    NEWEST = 'NEWEST',
    HOTTEST = 'HOTTEST'
}

/**
 * SocialPlatform
 */
export enum SocialPlatform {
    Twitter = 'Twitter',
    TradingView = 'TradingView'
}

/**
 * SocialMediaItem
 */
export interface SocialMediaItem {
    /**
     * Assets
     * 关联的资产
     */
    assets: string[];
    /**
     * Title
     * 价格
     */
    title?: string | null;
    /**
     * Content
     * 成交量
     */
    content: string;
    /**
     * Image
     * 图片
     */
    image?: string | null;
    /**
     * Src
     * 源连接
     */
    src: string;
    /**
     * Create Ts
     * 创作时间
     */
    create_ts: number;
    /**
     * Creator Name
     * 创作者名称
     */
    creator_name: string;
    /**
     * Creator Picture
     * 创作者头像
     */
    creator_picture: string;
    /**
     * Comments Count
     * 评论数量
     */
    comments_count: number;
    /**
     * Likes Count
     * 点赞数量
     */
    likes_count: number;
    /**
     * Author Profile Url
     * 创作者主页链接
     */
    author_profile_url: string;
    /**
     * Author Profile Platform
     * 创作者主页平台
     * 注意：这个字段在远程API中不存在，但在我们的应用中需要使用
     */
    author_platform: SocialPlatform;
}

/**
 * SocialMediaModel
 */
export interface SocialMediaModel {
    /**
     * Items
     */
    items: SocialMediaItem[];
    /**
     * Next Cursor
     * 翻页游标
     */
    next_cursor?: string | null;
}

/**
 * SocialMediaResp
 */
export interface SocialMediaResp {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: SocialMediaModel;
}

/**
 * FollowKolParam
 */
export interface FollowKolParam {
    /**
     * Platform
     * kol 所属平台
     */
    platform: SocialPlatform;
    /**
     * Link
     * kol 链接
     */
    link: string;
}

/**
 * KolItem
 */
export interface KolItem {
    /**
     * Platform
     * 平台
     */
    platform: SocialPlatform;
    /**
     * Profile Url
     * kol主页链接
     */
    profile_url: string;
    creator_name: string;
}

/**
 * FollowingKolResp
 */
export interface FollowingKolResp {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: KolItem[];
}

/**
 * CommonResponse
 */
export interface CommonResponse {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: any;
}

/**
 * ValidationError
 */
export interface ValidationError {
    /**
     * Location
     */
    loc: (string | number)[];
    /**
     * Message
     */
    msg: string;
    /**
     * Error Type
     */
    type: string;
}

/**
 * HTTPValidationError
 */
export interface HTTPValidationError {
    /**
     * Detail
     */
    detail: ValidationError[];
} 