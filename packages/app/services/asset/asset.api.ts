import api from '../api';
import type { 
  AssetBase, 
  AssetBaseParam,
  SearchEventParam,
  TopBarAssetListResp, 
  SearchResultListResp, 
  MarketsOverviewResp, 
  MarketTrendingSort, 
  Market, 
  TrendingResp, 
  AssetDetailResp, 
  CommonResponse,
  MarketsSentimentResp
} from './asset.types';

/**
 * Topbar Asset List
 * GET /api/v1/public/asset/topbar
 * 接口ID：topbar_asset_list_api_v1_public_asset_topbar_get
 */
export const getTopBarAssets = async () => api.get<TopBarAssetListResp>(
  '/api/v1/public/asset/topbar'
);

/**
 * Search
 * GET /api/v1/public/asset/search
 * 接口ID：search_api_v1_public_asset_search_get
 */
export const searchAssets = async (queryText: string) => api.get<SearchResultListResp>(
  '/api/v1/public/asset/search',
  { query_text: queryText }
);

/**
 * 市场情绪指标
 * GET /api/v1/public/asset/market-sentiment
 * 接口ID：市场情绪指标_api_v1_public_asset_market_sentiment_get
 */
export const getMarketSentiment = async () => api.get<MarketsSentimentResp>(
  '/api/v1/public/asset/market-sentiment'
);

/**
 * 市场概览
 * GET /api/v1/public/asset/market-overview
 * 接口ID：市场概览_api_v1_public_asset_market_overview_get
 */
export const getMarketOverview = async () => api.get<MarketsOverviewResp>(
  '/api/v1/public/asset/market-overview'
);

/**
 * 热门市场排行
 * GET /api/v1/public/asset/market-trending
 * 接口ID：热门市场排行_api_v1_public_asset_market_trending_get
 */
export const getMarketTrending = async (market: Market, sort: MarketTrendingSort) => api.get<TrendingResp>(
  '/api/v1/public/asset/market-trending',
  { market, sort }
);

/**
 * 热搜榜
 * GET /api/v1/public/asset/hot-searches
 * 接口ID：热搜榜_api_v1_public_asset_hot_searches_get
 */
export const getHotSearches = async () => api.get<TrendingResp>(
  '/api/v1/public/asset/hot-searches'
);

/**
 * 搜索命中记录上报
 * POST /api/v1/public/asset/search-events
 * 接口ID：搜索命中记录上报_api_v1_public_asset_search_events_post
 */
export const reportSearchEvent = async (params: SearchEventParam) => api.post<CommonResponse>(
  '/api/v1/public/asset/search-events',
  params
);

/**
 * 资产详情
 * POST /api/v1/public/asset/asset-detail
 * 接口ID：资产详情_api_v1_public_asset_asset_detail_post
 */
export const getAssetDetail = async (params: AssetBase) => api.post<AssetDetailResp>(
  '/api/v1/public/asset/asset-detail',
  params
); 