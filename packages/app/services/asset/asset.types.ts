/**
 * Market
 */
export enum Market {
    US = 'US',
    HK = 'HK',
    CRYPTO = 'CRYPTO'
}

/**
 * MarketTrendingSort
 */
export enum MarketTrendingSort {
    VALUE_DESC = 'VALUE_DESC',
    CHANGE_RATE_ASC = 'CHANGE_RATE_ASC',
    CHANGE_RATE_DESC = 'CHANGE_RATE_DESC'
}

/**
 * Sentiment
 */
export enum Sentiment {
    VERY_BEARISH = 0,
    BEARISH = 1,
    NEUTRAL = 2,
    BULLISH = 3,
    VERY_BULLISH = 4
}

/**
 * Technical
 */
export enum Technical {
    STRONG_SELL = 0,
    SELL = 1,
    NEUTRAL = 2,
    BUY = 3,
    STRONG_BUY = 4
}

/**
 * AssetBase
 */
export interface AssetBase {
    /**
     * Market
     */
    market: Market;
    /**
     * Symbol
     * symbol
     */
    symbol: string;
}

/**
 * AssetBaseParam
 */
export interface AssetBaseParam {
    /**
     * Market
     */
    market: Market;
    /**
     * Symbol
     * symbol
     */
    symbol: string;
}

/**
 * SearchEventParam
 */
export interface SearchEventParam {
    /**
     * Market
     */
    market: Market;
    /**
     * Symbol
     * symbol
     */
    symbol: string;
    /**
     * Search Text
     * 搜索关键字
     */
    search_text: string;
}

/**
 * TopBarAssetItem
 */
export interface TopBarAssetItem {
    /**
     * Market
     */
    market: Market;
    /**
     * Symbol
     * symbol
     */
    symbol: string;
    /**
     * Name
     * 名称
     */
    name: string;
    /**
     * Price
     * 价格
     */
    price: string;
    /**
     * Change Rate
     * 价格涨跌幅
     */
    change_rate: string;
}

/**
 * TopBar
 */
export interface TopBar {
    /**
     * Fixed
     */
    fixed: TopBarAssetItem[];
    /**
     * Floating
     */
    floating: TopBarAssetItem[];
}

/**
 * TopBarAssetListResp
 */
export interface TopBarAssetListResp {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: TopBar;
}

/**
 * SearchResultItem
 */
export interface SearchResultItem {
    /**
     * Market
     */
    market: Market;
    /**
     * Symbol
     * symbol
     */
    symbol: string;
    /**
     * Name
     * 名称
     */
    name: string;
    /**
     * Price
     * 价格
     */
    price: string;
    /**
     * Change Rate
     * 价格涨跌幅
     */
    change_rate: string;
}

/**
 * SearchResultListResp
 */
export interface SearchResultListResp {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: SearchResultItem[];
}

/**
 * MarketsSentimentItem
 */
export interface MarketsSentimentItem {
    /**
     * Market
     */
    market: Market;
    /**
     * Symbol
     */
    symbol: string;
    /**
     * Name
     * 名称
     */
    name: string;
    /**
     * Price
     * 价格
     */
    price: string;
    /**
     * Change Rate
     * 价格涨跌幅
     */
    change_rate: string;
    /**
     * Long Short Ratio
     * Binance多空比例
     */
    long_short_ratio?: string | null;
    /**
     * Fear Greed Index
     * 恐惧贪婪指数
     */
    fear_greed_index?: number | null;
    /**
     * Alt Coin Season
     * 山寨指数
     */
    alt_coin_season?: string | null;
    /**
     * Vix Index
     * VIX 恐慌指数
     */
    vix_index?: string | null;
    /**
     * Pc Ratio
     * 看涨看跌比例 Put/Call Ratio
     */
    pc_ratio?: string | null;
    /**
     * Market Sentiment
     * 市场情绪（美股）
     */
    market_sentiment?: Sentiment | null;
    /**
     * Technical Rating
     * 买入卖出评级（港股）
     */
    technical_rating?: Technical | null;
}

/**
 * MarketsSentimentResp
 */
export interface MarketsSentimentResp {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: MarketsSentimentItem[];
}

/**
 * MarketsOverview
 */
export interface MarketsOverview {
    /**
     * Market Cap
     * 总市值
     */
    market_cap: string;
    /**
     * Change Rate
     * 比前一天变动幅度
     */
    change_rate: string;
}

/**
 * MarketsOverviewResp
 */
export interface MarketsOverviewResp {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: MarketsOverview;
}

/**
 * TrendingItem
 */
export interface TrendingItem {
    /**
     * Market
     */
    market: Market;
    /**
     * Symbol
     * symbol
     */
    symbol: string;
    /**
     * Name
     * 名称
     */
    name: string;
    /**
     * Price
     * 价格
     */
    price: string;
    /**
     * Value
     * 成交额
     */
    value: string;
    /**
     * Market Cap
     * 市值
     */
    market_cap: string;
    /**
     * Change Rate
     * 价格涨跌幅
     */
    change_rate: string;
}

/**
 * TrendingResp
 */
export interface TrendingResp {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: TrendingItem[];
}

/**
 * AssetDetail
 */
export interface AssetDetail {
    /**
     * Market
     */
    market: Market;
    /**
     * Symbol
     * symbol
     */
    symbol: string;
    /**
     * Name
     * 名称
     */
    name: string;
    /**
     * Price
     * 价格
     */
    price: string;
    /**
     * Change Rate
     * 价格涨跌幅
     */
    change_rate: string;
    /**
     * Value
     * 成交额
     */
    value: string;
    /**
     * Market Cap
     * 市值
     */
    market_cap: string;
    /**
     * Fdv
     * 完全稀释市值(加密)
     */
    fdv?: string | null;
    /**
     * Pe
     * 市盈率(股票)
     */
    pe?: string | null;
    /**
     * Total Supply
     * 最大供给量(加密)
     */
    total_supply?: number | null;
    /**
     * Total Share
     * 总股本(股票)
     */
    total_share?: number | null;
}

/**
 * AssetDetailResp
 */
export interface AssetDetailResp {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: AssetDetail;
}

/**
 * CommonResponse
 */
export interface CommonResponse {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: any;
}

/**
 * BooleanResult
 */
export interface BooleanResult {
    /**
     * Result
     */
    result: boolean;
}

/**
 * ValidationError
 */
export interface ValidationError {
    /**
     * Location
     */
    loc: (string | number)[];
    /**
     * Message
     */
    msg: string;
    /**
     * Error Type
     */
    type: string;
}

/**
 * HTTPValidationError
 */
export interface HTTPValidationError {
    /**
     * Detail
     */
    detail: ValidationError[];
} 