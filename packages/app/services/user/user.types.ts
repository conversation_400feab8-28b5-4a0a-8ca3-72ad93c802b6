/**
 * UserMetadata
 */
export interface UserMetadata {
    /**
     * Email
     * 用户邮箱
     */
    email: string;
    /**
     * Username
     * 用户名
     */
    username: string | null;
    /**
     * Introduction
     * 个人简介
     */
    introduction: string | null;
    /**
     * Avatar Link
     * 头像链接
     */
    avatar_link: string | null;
    /**
     * Invite Code
     * 邀请码
     */
    invite_code: string | null;
}

/**
 * UserMetadataResp
 */
export interface UserMetadataResp {
    /**
     * Status
     */
    status: number;
    /**
     * Msg
     */
    msg: string;
    /**
     * Data
     */
    data: UserMetadata;
}

/**
 * ValidationError
 */
export interface ValidationError {
    /**
     * Location
     */
    loc: (string | number)[];
    /**
     * Message
     */
    msg: string;
    /**
     * Error Type
     */
    type: string;
}

/**
 * HTTPValidationError
 */
export interface HTTPValidationError {
    /**
     * Detail
     */
    detail: ValidationError[];
}
