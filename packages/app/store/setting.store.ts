// store/weatherStore.ts
import AsyncStorage from '@react-native-async-storage/async-storage';
import { makeAutoObservable, reaction, autorun } from 'mobx';
import { makePersistable } from 'mobx-persist-store';
import { getUserInfo } from '../services/user/user.api';
import type { UserMetadata } from '../services/user/user.types';
import { canUsePersistence } from '../uitls';
import { authStore } from './auth.store';
import FetchableData from '../uitls/FetchableData';
import { getWatchlist } from '../services/watchlist/watchlist.api';
import type { WatchListItem } from '../services/watchlist/watchlist.types';

class SettingStore {
  constructor() {
    makeAutoObservable(this);
     // 只在客户端浏览器或 React Native 环境中执行持久化
     if (canUsePersistence()) {
      try {
        makePersistable(this, {
          name: 'SettingStore',
          properties: ['assetPortfolioTable'],
          storage: AsyncStorage,
        });
      } catch (error) {
        console.error('持久化存储初始化失败:', error);
      }
    }
  }
  assetPortfolioTable: {
    price_change_24h: boolean;
    price_change_7d: boolean;
    market_cap: boolean;
    page_size: number;
  } = {
    price_change_24h: true,
    price_change_7d: true,
    market_cap: true,
    page_size: 10,
  };
  setAssetPortfolioTable<K extends keyof typeof this.assetPortfolioTable>(
    key: K, 
    value: typeof this.assetPortfolioTable[K]
  ) {
    const res = {
      ...this.assetPortfolioTable,  
      [key]: value,
    };
    this.assetPortfolioTable = res;
  }
}

export const settingStore = new SettingStore();
