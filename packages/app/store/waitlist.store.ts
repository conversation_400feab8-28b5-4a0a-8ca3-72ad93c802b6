// store/weatherStore.ts
import AsyncStorage from '@react-native-async-storage/async-storage';
import { makeAutoObservable, reaction, autorun } from 'mobx';
import { makePersistable } from 'mobx-persist-store';
import { getUserInfo } from '../services/user/user.api';
import type { UserMetadata } from '../services/user/user.types';
import { canUsePersistence } from '../uitls';
import { authStore } from './auth.store';
import FetchableData from '../uitls/FetchableData';
import { getWatchlist } from '../services/watchlist/watchlist.api';
import type { WatchListItem } from '../services/watchlist/watchlist.types';
import { getWaitlistStatus } from '../services/waitlist/waitlist.api';
import type { BooleanResult } from '../services/waitlist/waitlist.types';

class WaitlistStore {
  constructor() {
    makeAutoObservable(this);
  }

  waitlistStatus = new FetchableData<BooleanResult>(getWaitlistStatus);
}

export const waitlistStore = new WaitlistStore();
