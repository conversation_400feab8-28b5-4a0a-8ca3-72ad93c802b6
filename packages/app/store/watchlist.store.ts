// store/weatherStore.ts
import AsyncStorage from '@react-native-async-storage/async-storage';
import { makeAutoObservable, reaction, autorun } from "mobx";
import { makePersistable } from "mobx-persist-store";
import { getUserInfo } from "../services/user/user.api";
import type { UserMetadata } from "../services/user/user.types";
import { canUsePersistence } from "../uitls";
import { authStore } from "./auth.store";
import FetchableData from '../uitls/FetchableData';
import { getWatchlist } from '../services/watchlist/watchlist.api';
import type { WatchListItem } from '../services/watchlist/watchlist.types';
import { getFollowingKol } from '../services/media/media.api';
import type { KolItem } from '../services/media/media.types';


class WatchlistStore {
    

  constructor() {
    makeAutoObservable(this);
    
    // autorun(
    //   () => { 
    //     if (!authStore.activeUserId) {
    //       this.followingKol.setData(null);
    //       return;
    //     }
    //     this.followingKol.fetch();
    //   },
    // );
  }

  watchlist = new FetchableData<WatchListItem[]>(getWatchlist);
  followingKol = new FetchableData<KolItem[]>(getFollowingKol);


 
}

export const watchlistStore = new WatchlistStore();