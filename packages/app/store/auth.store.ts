import { autorun, makeAutoObservable, reaction } from 'mobx';
import { makePersistable } from 'mobx-persist-store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { canUsePersistence } from '../uitls';
import { userStore } from './user.store';
import { watchlistStore } from './watchlist.store';
import { waitlistStore } from './waitlist.store';

class AuthStore {
  // 修改为普通对象，而不是 Map，以确保持久化正常工作
  tokens: Record<string, string> = {};
  // 记录当前活跃用户的ID
  activeUserId: string | null = null;

  showLoginModal = false;

  inviteCode: string | null = null;

  setShowLoginModal(show: boolean) {
    this.showLoginModal = show;
  }
  closeLoginModal() {
    this.showLoginModal = false;
  }

  constructor() {
    makeAutoObservable(this);

    // 只在客户端浏览器或 React Native 环境中执行持久化
    if (canUsePersistence()) {
      try {
        makePersistable(this, {
          name: 'AuthStore',
          properties: ['tokens', 'activeUserId'],
          storage: AsyncStorage,
        });
      } catch (error) {
        console.error('持久化存储初始化失败:', error);
      }
    }

    this.setupAuthStoreListener();
  }

  // 设置 authStore 监听器
  private setupAuthStoreListener() {
    // 保留原有的 reaction 监听作为备份
    reaction(
      // 监听的数据
      () => ({
        token: this.currentToken,
        userId: this.activeUserId,
      }),
      // 当数据变化时执行的操作
      (data, prev) => {
        const { token, userId } = data;
        if (token && userId && (token !== prev?.token || userId !== prev?.userId)) {
          userStore
            .fetchUserInfo()
            .then((info) => {
              if (info) {
                userStore.setUserInfo(userId, info);
              }
            })
            .catch((err) => console.error('reaction: 自动拉取用户信息失败:', err));
          // watchlistStore.watchlist.fetch();
          // waitlistStore.waitlistStatus.fetch();
        } else if (!this.isLoggedIn) {
          // waitlistStore.waitlistStatus.setData(null);
        }
      },
      // 配置选项
      {
        fireImmediately: true, // 立即执行一次
        delay: 100, // 防抖延迟
      },
    );
  }

  // // 使用userLogin API进行用户登录
  // async login(account: string, password: string) {
  //   try {
  //     const { id, token } = await userLogin(account, password);
  //     if (id && token) {
  //       const userId = id.toString();
  //       this.setToken(userId, token);
  //       this.setActiveUser(userId);
  //       return { success: true, userId };
  //     }
  //     return { success: false, error: '登录失败：无效的响应数据' };
  //   } catch (error) {
  //     console.error('登录失败:', error);
  //     return { success: false, error: '登录失败：' + (error instanceof Error ? error.message : String(error)) };
  //   }
  // }

  // 检查用户是否已登录
  get isLoggedIn(): boolean {
    return !!this.currentToken;
  }

  // 获取当前活跃用户ID
  getActiveUserId(): string | null {
    return this.activeUserId;
  }

  setToken(userId: string, token: string) {
    this.tokens[userId] = token;
    if (!this.activeUserId) {
      this.activeUserId = userId;
    }
  }

  getToken(userId: string): string {
    return this.tokens[userId] || '';
  }

  get currentToken() {
    if (!this.activeUserId) {
      return '';
    }
    const token = this.getToken(this.activeUserId);
    return token;
  }

  removeToken(userId: string) {
    delete this.tokens[userId];
    this.activeUserId = null;
  }

  // 清除所有认证信息
  clearAll() {
    this.tokens = {};
    this.activeUserId = null;
  }

  // 设置当前活跃用户
  setActiveUser(userId: string) {
    this.activeUserId = userId;
    console.log(`设置活跃用户: userId=${userId}, token=${this.getToken(userId).substring(0, 10) || '未设置'}...`);
  }

  setInviteCode(inviteCode: string | null) {
    this.inviteCode = inviteCode;
  }
}

export const authStore = new AuthStore();
