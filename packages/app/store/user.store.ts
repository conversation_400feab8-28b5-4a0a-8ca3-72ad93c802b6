// store/weatherStore.ts
import AsyncStorage from '@react-native-async-storage/async-storage';
import { makeAutoObservable, reaction, autorun, runInAction } from 'mobx';
import { makePersistable } from 'mobx-persist-store';
import { getUserInfo } from '../services/user/user.api';
import type { UserMetadata } from '../services/user/user.types';
import { canUsePersistence } from '../uitls';
import { authStore } from './auth.store';

class UserStore {
  userInfos: Record<string, UserMetadata> = {};

  constructor() {
    makeAutoObservable(this);
    if (canUsePersistence()) {
      makePersistable(this, {
        name: 'UserStore',
        properties: ['userInfos'],
        storage: AsyncStorage,
      });
    }
  }

  // 初始化时加载活跃用户信息
  async initializeActiveUser() {
    const activeUserId = authStore.activeUserId;
    if (activeUserId && !this.getUserInfo(activeUserId)) {
      await this.fetchUserInfo();
    }
    return activeUserId;
  }

  // 获取用户信息
  async fetchUserInfo() {
    try {
      const userId = authStore.activeUserId;

      if (!userId) {
        return;
      }
      const response = await getUserInfo();
      return response;
    } catch (error) {
      console.error('Failed to fetch user info:', error);
      throw error;
    }
  }

  // 设置用户信息
  setUserInfo(userId: string, info: UserMetadata) {
    const userInfos = { ...this.userInfos };
    userInfos[userId] = info;
    this.userInfos = userInfos;
  }

  // 获取用户信息
  getUserInfo(userId: string): UserMetadata | undefined {
    return this.userInfos[userId];
  }

  get currentUserInfo(): UserMetadata | undefined {
    if (!authStore.activeUserId) {
      return undefined;
    }
    return this.userInfos[authStore.activeUserId];
  }

  // 获取当前活跃用户信息
  get currentUser(): UserMetadata | undefined {
    return authStore.activeUserId ? this.getUserInfo(authStore.activeUserId) : undefined;
  }

  // 切换活跃用户
  async switchUser(userId: string) {
    if (!this.getUserInfo(userId)) {
      await this.fetchUserInfo();
    }
    authStore.setActiveUser(userId);
  }

  // 获取当前用户的 token
  get currentUserToken() {
    return authStore.activeUserId ? authStore.getToken(authStore.activeUserId) : undefined;
  }

  // 登出用户
  logout(userId: string | null) {
    if (!userId) {
      return;
    }
    authStore.removeToken(userId);
    delete this.userInfos[userId];
  }

  // 清除所有用户信息
  clearAllUsers() {
    this.userInfos = {};
    authStore.clearAll();
  }
}

export const userStore = new UserStore();
