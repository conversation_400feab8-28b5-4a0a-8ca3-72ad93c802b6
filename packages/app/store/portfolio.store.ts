// store/weatherStore.ts
import { makeAutoObservable } from "mobx";
import type { Market } from '../services/asset/asset.types';
import { getPortfolioGroupOverview, getPortfolioPositionList } from '../services/portfolio/portfolio.api';
import type { GroupOverview, PositionItem } from '../services/portfolio/portfolio.types';
import FetchableData from '../uitls/FetchableData';


class PortfolioStore {
    

  constructor() {
    makeAutoObservable(this);
    
  }

  portfolioPositionList = new FetchableData<PositionItem[]>(getPortfolioPositionList);
  portfolioGroupOverview = new FetchableData<GroupOverview | null>(getPortfolioGroupOverview);


  addAssetsList: {
    symbol: string;
    name: string;
    market: Market;
    price: string;
    change_rate: string;
  }[] = [];
  
  addAssets(value: {
    symbol: string;
    name: string;
    market: Market;
    price: string;
    change_rate: string;
  }) {
    if (this.addAssetsList.find((item) => item.symbol === value.symbol && item.market === value.market)) {
      this.addAssetsList = this.addAssetsList.filter((item) => item.symbol !== value.symbol || item.market !== value.market);
    } else {
      const list = [...this.addAssetsList];
      list.push(value);
      this.addAssetsList = list;
    }
  }
  clearAssets() {
    this.addAssetsList = [];
  }

  showAddAssetsModal = false;
  setShowAddAssetsModal(show: boolean) {
    this.showAddAssetsModal = show;
  }

  paramsAddTransactionModal: {
    market: Market;
    symbol: string;
    name: string;
  } | null = null;
  showAddTransactionModal = false;
  setShowAddTransactionModal(show: boolean) {
    this.showAddTransactionModal = show;
  }

  setParamsAddTransactionModal(params: {
    market: Market;
    symbol: string;
    name: string;
  } | null) {
    this.paramsAddTransactionModal = params;
  }


  showTransactionDetailModal = false;
  setShowTransactionDetailModal(show: boolean) {
    this.showTransactionDetailModal = show;
  }

  paramsTransactionDetailModal: {
    pos_id: string;
  } | null = null;
  setParamsTransactionDetailModal(params: {
    pos_id: string;
  } | null) {
    this.paramsTransactionDetailModal = params;
  }
  isStartGuide = false;
  startGuide() {
    this.isStartGuide = true;
  }
  closeGuide() {
    this.isStartGuide = false;
  }

  showGuideStep1 = false
  showGuideStep2 = false
  setGuideStep1(show: boolean) {
    this.showGuideStep1 = show;
  }
  setGuideStep2(show: boolean) {
    this.showGuideStep2 = show;
  }

  showSuggestionModal = false
  setShowSuggestionModal(show: boolean) {
    this.showSuggestionModal = show;
  }

}

export const portfolioStore = new PortfolioStore();