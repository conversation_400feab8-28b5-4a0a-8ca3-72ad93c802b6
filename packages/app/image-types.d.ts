/**
 * 图片类型定义文件
 * 用于支持在VSCode中点击PNG图片时跳转到类型定义
 */

// PNG图片类型定义
declare module '*.png' {
  import { ImageSourcePropType } from 'react-native';
  const src: ImageSource;
  export default src;
}

// JPG图片类型定义
declare module '*.jpg' {
  import { ImageSourcePropType } from 'react-native';
  const src: ImageSourcePropType;
  export default src;
}

// JPEG图片类型定义
declare module '*.jpeg' {
  import { ImageSourcePropType } from 'react-native';
  const src: ImageSourcePropType;
  export default src;
}

// GIF图片类型定义
declare module '*.gif' {
  import { ImageSourcePropType } from 'react-native';
  const src: ImageSourcePropType;
  export default src;
}

// WebP图片类型定义
declare module '*.webp' {
  import { ImageSourcePropType } from 'react-native';
  const src: ImageSourcePropType;
  export default src;
} 
declare module '*.svg' {
  const src: any;
  export default src;
} 