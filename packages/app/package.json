{"name": "@quantum/app", "version": "0.0.0", "main": "index.ts", "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/react": "^18.2.46", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-native": "^0.73.0", "typescript": "^5.3.3"}, "dependencies": {"@arco-design/web-react": "^2.66.1", "@hookform/resolvers": "^5.0.1", "@quantum/components": "workspace:*", "@quantum/shared": "workspace:*", "@shopify/react-native-skia": "^1.11.18", "@unitools/image": "^0.0.4", "@unitools/image-expo": "^0.0.5", "@unitools/link": "^0.0.3", "@unitools/link-expo": "^0.0.1", "@unitools/navigation": "^0.0.1-alpha.0", "@unitools/router": "^0.0.4", "@unitools/router-expo": "^0.0.1", "@wuba/react-native-echarts": "^2.0.2", "ahooks": "^3.8.4", "dayjs": "^1.11.13", "echarts": "^5.6.0", "i18next": "^24.2.3", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react-native": "^0.378.0", "mobx": "^6.13.7", "mobx-persist-store": "^1.1.8", "mobx-react-lite": "^4.1.0", "next-i18next": "^15.4.2", "numbro": "^2.5.0", "react-copy-to-clipboard": "^5.1.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-native-gesture-handler": "^2.24.0", "react-native-reanimated": "~3.16.1", "react-native-svg": "^15.11.2", "react-scroll": "^1.9.3", "react-share": "^5.2.2", "zod": "^3.24.2"}, "peerDependencies": {"react": "^18.3.1", "react-native": "^0.76.7"}, "files": ["button", "index.js", "index.d.ts"]}