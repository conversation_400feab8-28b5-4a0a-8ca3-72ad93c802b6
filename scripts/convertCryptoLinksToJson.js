const fs = require('fs');
const path = require('path');

// 解析命令行参数
const args = process.argv.slice(2);
let inputPath = '';
let outputPath = '';

// 获取命令行参数
if (args.length >= 2) {
  inputPath = args[0];
  outputPath = args[1];
} else {
  console.error('使用方法: node convertCryptoLinksToJson.js <输入文件路径> <输出文件路径>');
  console.error('例如: node convertCryptoLinksToJson.js ../apps/next-app/common/sitemapData/crypto_links ../apps/next-app/common/sitemapData/crypto_links.json');
  process.exit(1);
}

// 解析为绝对路径
const sourceFilePath = path.resolve(process.cwd(), inputPath);
const targetFilePath = path.resolve(process.cwd(), outputPath);

// 读取文件
try {
  // 读取源文件
  const fileContent = fs.readFileSync(sourceFilePath, 'utf8');
  
  // 将内容按行分割并过滤空行
  const links = fileContent.split('\n').filter(line => line.trim() !== '');
  
  // 提取URL中的市场和股票代号
  const stockItems = links.map(link => {
    // 从URL中提取market和symbol，确保能够处理包含特殊字符的符号
    // 新的正则表达式使用URL的结构来提取market和symbol部分
    const urlPattern = /https:\/\/stockbits\.ai\/detail\/([^\/]+)\/([^\/\n]+)$/;
    const match = link.match(urlPattern);
    
    if (match) {
      return {
        market: match[1],
        symbol: match[2],
        url: link
      };
    }
    
    console.warn(`无法解析链接: ${link}`);
    return null;
  }).filter(Boolean);
  
  // 将数组转换为JSON字符串
  const jsonContent = JSON.stringify(stockItems, null, 2);
  
  // 写入目标文件
  fs.writeFileSync(targetFilePath, jsonContent);
  
  console.log(`成功将 ${links.length} 个链接转换为JSON格式并保存到 ${targetFilePath}`);
  console.log(`解析成功: ${stockItems.length} 项`);
  
  if (stockItems.length < links.length) {
    console.warn(`警告: ${links.length - stockItems.length} 个链接无法解析`);
  }
} catch (error) {
  console.error('转换过程中发生错误:', error);
  console.error(error.stack);
} 