const { resolve, join } = require('path');
const { kebabCase } = require('lodash');
const pm2 = require('pm2');
const branch = process.env.branch;
const branchName = kebabCase(branch.replace(/^origin\//, ''));
const fs = require('fs');

const { program } = require('commander');

program.option('--app, --app <char>').option('--deploy-cwd, --deploy-cwd <char>');

program.parse();
const options = program.opts();
const app = options.app;
const deployCwd = options.deployCwd;

const utils = require('./utils');
const { pick } = require('lodash');

async function main() {
  const productionENVFile = join(__dirname, '..', '..', 'apps', app, '.env.production');
  const stagingENVFile = join(__dirname, '..', '..', 'apps', app, '.env.staging');
  if (fs.existsSync(productionENVFile)) {
    fs.unlinkSync(productionENVFile);
  }
  fs.cpSync(stagingENVFile, productionENVFile);

  const { default: getPort } = await import('get-port');
  let port;

  let deployedName = '';

  const instanceNamePrefix = `[${kebabCase(app)}]-[${kebabCase(branchName)}]`;

  function stop(name) {
    return new Promise((resolve, reject) => {
      pm2.delete(name, (err, proc) => {
        if (err) reject(err);
        resolve();
      });
    });
  }

  async function startNew() {
    console.log('PM2 start new server');
    if (!port) {
      port = await getPort();
    }
    console.log('PM2 start new server:', port);
    return new Promise((resolve, reject) => {
      pm2.start(
        {
          // script: `npm run start --filter=${app} -- -- --port ${port}`,
          script: `./node_modules/next/dist/bin/next`,
          args: `start --port ${port}`,
          env: {
            DEPLOY_CWD: deployCwd,
          },
          name: `${instanceNamePrefix}:${port}`,
          cwd: join(deployCwd, 'current', 'apps', app),
          log_type: 'json',
        },
        function (err, apps) {
          console.log('PM2 start new server:', err, apps.length);
          if (err) {
            console.error(err);
            reject(err);
            return pm2.disconnect();
          }
          resolve();
        },
      );
    });
  }

  pm2.connect(async function (err) {
    console.log('Connect PM2');
    if (err) {
      console.error(err);
      process.exit(2);
    }

    pm2.list(async (err, list) => {
      console.log(
        'PM2 list',
        err,
        list.map((i) => i.name),
      );

      list.forEach((item) => {
        if (item.name.startsWith(instanceNamePrefix)) {
          deployedName = item.name;
        }
      });

      console.log('PM2 deployedName', deployedName);

      const s = deployedName.split(':');
      port = s.pop();
      if (!deployedName) {
        await startNew();
      } else {
        await stop(deployedName);
        await startNew();
      }

      console.log(
        `--START-DEPLOY-INFO--${JSON.stringify({
          port,
        })}--END-DEPLOY-INFO--`,
      );

      pm2.disconnect();
    });
  });
}

main();
