const { join } = require('path');
const { pick } = require('lodash');
const pm2 = require('pm2');

const { program } = require('commander');

program.option('--app, --app <char>').option('--port, --port <int>').option('--deploy-cwd, --deploy-cwd <char>');

program.parse();
const options = program.opts();
const app = options.app;
const deployCwd = options.deployCwd;
const isGray = process.env.SETUP_ENV === 'gray';
const port = options.port || process.env.PORT;
const deployedName = `${isGray ? '[GRAY]' : '[PROD]'}-` + app + `:${port}`;

const deployENV = ['SETUP_ENV', 'APP_ENV', 'NODE_ENV', 'PORT', 'branch', 'commitHash', 'NETWORK_TYPE', 'REDIS_URL'];

const log = function (...args) {
  console.info(`[${deployedName}]`, ...args);
};

async function main() {
  // const productionENVFile = path.join(__dirname, '..', '.env.production');
  // const stagingENVFile = path.join(__dirname, '..', '.env.staging');
  // if (fs.existsSync(productionENVFile)) {
  //   fs.unlinkSync(productionENVFile);
  // }
  // fs.cpSync(stagingENVFile, productionENVFile);

  const { default: getPort } = await import('get-port');

  function reload(name) {
    log('Reload');
    return new Promise((resolve, reject) => {
      pm2.reload(
        name,
        {
          updateEnv: true,
        },
        (err) => {
          if (err) reject(err);
          log('Reload Success');
          resolve();
        },
      );
    });
  }

  async function startNew() {
    const env = {
      ...pick(process.env, deployENV),
      DEPLOY_CWD: deployCwd,
    };
    log('PM2 start new server', env);
    return new Promise((resolve, reject) => {
      pm2.start(
        {
          // script: `npm`,
          script: `../../node_modules/next/dist/bin/next`,
          args: `start --port ${port}`,
          instances: isGray ? 1 : 1,
          exec_mode: 'cluster',
          env,
          cwd: join(deployCwd, 'current', 'apps', app),
          name: deployedName,
          log_type: 'json',
          // instance_var: 'AS_Instance_Master',
        },
        function (err, apps) {
          log(
            'PM2 start new server success',
            apps.filter((item) => item.name === deployedName).map((item) => item.pm_id),
          );
          if (err) {
            console.error(err);
            reject(err);
            return pm2.disconnect();
          }
          resolve();
        },
      );
    });
  }

  function stopAndDelete(name) {
    return new Promise((resolve, reject) => {
      pm2.stop(name, (err) => {
        if (err) reject(err);
        pm2.delete(name, (err) => {
          if (err) reject(err);
          resolve();
        });
      });
    });
  }

  pm2.connect(async function (err) {
    log('Connect PM2');
    if (err) {
      console.error(err);
      process.exit(2);
    }

    pm2.list(async (err, list) => {
      if (err) {
        log('PM2 list err:', err);
      }
      log(
        'PM2 list apps:',
        list.map((i) => i.name),
      );

      let pm_id;
      let isCluster = true;

      list.forEach((item) => {
        if (deployedName === item.name) {
          pm_id = item.pm_id;
          isCluster = item.pm2_env.exec_mode === 'cluster_mode';
          log('PM2 found pidL:', item.pm_id, item.pm2_env.exec_mode);
        }
      });

      log('PM2 deployedName', deployedName);

      if (pm_id == null) {
        await startNew();
      } else {
        if (!isCluster) {
          log('PM2 not isCluster kill it and restart new');
          await stopAndDelete(deployedName);
          await startNew();
        } else {
          await reload(deployedName);
        }
      }

      log(
        `--START-DEPLOY-INFO--${JSON.stringify({
          port,
        })}--END-DEPLOY-INFO--`,
      );

      pm2.disconnect();
    });
  });
}

main();
