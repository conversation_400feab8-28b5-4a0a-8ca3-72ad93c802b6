const axios = require('axios');
const gitlog = require('gitlog').default;
const branch = process.env.branch || 'main';
const commitHash = process.env.commitHash || 'no-commit';
const { kebabCase } = require('lodash');

function postBotMessage(webhookToken, msg) {
  return axios.post(`https://open.larksuite.com/open-apis/bot/v2/hook/${webhookToken}`, msg);
}

async function stagingNotify(app, port) {
  if (process.env.DEPLOY_NOTIFY_UUID) {
    const commits = gitlog({ number: 5, repo: './' });

    await postBotMessage(process.env.DEPLOY_NOTIFY_UUID, {
      msg_type: 'interactive',
      card: {
        elements: [
          {
            tag: 'hr',
          },
          {
            tag: 'div',
            text: {
              content: `**APP:** ${app}\r**Branch:** ${branch}\r**ETAG:** ${commitHash}\r**X-PORT：** <font color='red'>**${port}**</font>\r**Link:** [https://as-node.3bodylabs.com](https://as-node.3bodylabs.com)`,
              tag: 'lark_md',
            },
          },
          {
            tag: 'hr',
          },
          {
            tag: 'div',
            text: {
              content: `**最近修改记录:** \r${commits
                .map((commit) => `[${commit.subject} ] by **${commit.authorName}**`)
                .join('\r')}`,
              tag: 'lark_md',
            },
          },
        ],
        header: {
          title: {
            content: `📣️ ${app} 更新提示`,
            tag: 'plain_text',
          },
          template: 'grey',
        },
      },
    });
  } else {
    console.warn('未设置 DEPLOY_NOTIFY_UUID');
  }
}

async function productionNotify(app, link, isGray) {
  if (process.env.DEPLOY_NOTIFY_UUID) {
    const commits = gitlog({ number: 5, repo: './' });

    await postBotMessage(process.env.DEPLOY_NOTIFY_UUID, {
      msg_type: 'interactive',
      card: {
        elements: [
          {
            tag: 'hr',
          },
          {
            tag: 'div',
            text: {
              content: `**Branch:** ${branch}\r**ETAG:** ${commitHash}\r${
                isGray ? `**X-GRAY：** <font color='red'>**TRUE**</font>\r` : ''
              }**Link:** [${link}](${link})`,
              tag: 'lark_md',
            },
          },
          {
            tag: 'hr',
          },
          {
            tag: 'div',
            text: {
              content: `**最近修改记录:** \r${commits
                .map((commit) => `[${commit.subject} ] by **${commit.authorName}**`)
                .join('\r')}`,
              tag: 'lark_md',
            },
          },
        ],
        header: {
          title: {
            content: `${isGray ? '❗️' : '❗️❗️'} ${app} ${isGray ? '灰度' : '生产'}更新提示`,
            tag: 'plain_text',
          },
          template: isGray ? 'yellow' : 'red',
        },
      },
    });
  } else {
    console.warn('未设置 DEPLOY_NOTIFY_UUID');
  }
}

module.exports = {
  stagingNotify,
  productionNotify,
};
