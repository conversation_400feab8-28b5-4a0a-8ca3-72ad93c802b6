# SQL 数据提取工具

这个工具用于从 MySQL 备份文件中提取特定模型的数据。

## 功能

- 从 SQL 备份文件中提取指定模型的数据
- 提取 `collections` 表中的相关数据
- 提取 `fields` 表中的相关数据
- 提取表结构定义（DROP TABLE 和 CREATE TABLE 语句）
- 将提取的数据格式化为 SQL 语句并保存到文件

## 使用方法

### 命令行方式

```bash
node extract_sql_data.js <sql文件路径> <模型名称1,模型名称2,...> <输出文件路径>
```

例如：

```bash
node extract_sql_data.js qqt_oa_qa.sql kol_watchlist_tb,market_overview_tb output.sql
```

### 作为模块使用

```javascript
const { extractSqlData, formatSqlData, main } = require('./extract_sql_data');

// 方式一：直接使用主函数
main('qqt_oa_qa.sql', ['kol_watchlist_tb', 'market_overview_tb'], 'output.sql');

// 方式二：分步使用
const fs = require('fs');
const sqlContent = fs.readFileSync('qqt_oa_qa.sql', 'utf8');
const models = ['kol_watchlist_tb', 'market_overview_tb'];

// 提取数据
const extractedData = extractSqlData(sqlContent, models);

// 格式化数据
const formattedSql = formatSqlData(extractedData);

// 写入文件
fs.writeFileSync('output.sql', formattedSql);
```

## 参数说明

- `sql文件路径`：MySQL 备份文件的路径
- `模型名称1,模型名称2,...`：要提取的模型名称，多个模型用逗号分隔
- `输出文件路径`：提取的数据保存的文件路径

## 输出结果

脚本会生成一个 SQL 文件，包含以下内容：

1. 从 `collections` 表中提取的指定模型的数据
2. 从 `fields` 表中提取的指定模型的数据
3. 指定模型的表结构定义（DROP TABLE 和 CREATE TABLE 语句）

## 注意事项

- 脚本使用正则表达式匹配 SQL 语句，可能无法处理所有格式的 SQL 文件
- 如果 SQL 文件中包含特殊字符或格式，可能需要调整正则表达式
- 建议在使用前备份原始 SQL 文件
