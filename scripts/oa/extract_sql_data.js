/**
 * 从 MySQL 备份文件中提取指定模型的数据
 *
 * @param {string} sqlContent - SQL 备份文件的内容
 * @param {string[]} models - 要提取的模型名称数组
 * @returns {Object} - 包含提取的数据的对象
 */
function extractSqlData(sqlContent, models) {
  // 初始化结果对象
  const result = {
    collections: {},
    fields: {},
    tableDefinitions: {},
  };

  // 提取 collections 数据
  const collectionsRegex = /INSERT INTO `collections` VALUES\s*([^;]+);/;
  const collectionsMatch = sqlContent.match(collectionsRegex);

  if (collectionsMatch) {
    const collectionsData = collectionsMatch[1];
    // 解析 collections 数据
    const collectionsValues = collectionsData
      .split('),(')
      .map((item) => {
        // 处理第一个和最后一个元素
        item = item.replace(/^\(|\)$/g, '');
        // 分割字段值
        const values = item.split(',').map((val) => {
          // 处理引号内的值
          if (val.startsWith("'") && val.endsWith("'")) {
            return val.slice(1, -1);
          }
          return val;
        });

        // 提取 name 字段（第二个字段）
        const name = values[1];

        // 如果模型在指定列表中，则保存数据
        if (models.includes(name)) {
          return {
            name,
            data: item,
          };
        }
        return null;
      })
      .filter(Boolean);

    // 将数据添加到结果中
    collectionsValues.forEach((item) => {
      result.collections[item.name] = item.data;
    });
  }

  // 提取 fields 数据
  const fieldsRegex = /INSERT INTO `fields` VALUES\s*([^;]+);/;
  const fieldsMatch = sqlContent.match(fieldsRegex);

  if (fieldsMatch) {
    const fieldsData = fieldsMatch[1];
    // 解析 fields 数据
    const fieldsValues = fieldsData
      .split('),(')
      .map((item) => {
        // 处理第一个和最后一个元素
        item = item.replace(/^\(|\)$/g, '');
        // 分割字段值
        const values = item.split(',').map((val) => {
          // 处理引号内的值
          if (val.startsWith("'") && val.endsWith("'")) {
            return val.slice(1, -1);
          }
          return val;
        });

        // 提取 collectionName 字段（第六个字段）
        const collectionName = values[5];

        // 如果模型在指定列表中，则保存数据
        if (models.includes(collectionName)) {
          return {
            collectionName,
            data: item,
          };
        }
        return null;
      })
      .filter(Boolean);

    // 将数据添加到结果中
    fieldsValues.forEach((item) => {
      if (!result.fields[item.collectionName]) {
        result.fields[item.collectionName] = [];
      }
      result.fields[item.collectionName].push(item.data);
    });
  }

  // 提取表定义
  models.forEach((model) => {
    // 提取 DROP TABLE 语句
    const dropTableRegex = new RegExp(`DROP TABLE IF EXISTS \`${model}\`;[\\s\\S]*?;`, 'i');
    const dropTableMatch = sqlContent.match(dropTableRegex);

    if (dropTableMatch) {
      result.tableDefinitions[model] = {
        dropTable: dropTableMatch[0],
      };

      // 提取 CREATE TABLE 语句
      const createTableRegex = new RegExp(`CREATE TABLE \`${model}\` \\([\\s\\S]*?\\) ENGINE=InnoDB[^;]*;`, 'i');
      const createTableMatch = sqlContent.match(createTableRegex);

      if (createTableMatch) {
        result.tableDefinitions[model].createTable = createTableMatch[0];
      }
    }
  });

  return result;
}

/**
 * 将提取的数据格式化为 SQL 语句
 *
 * @param {Object} data - 提取的数据
 * @returns {string} - 格式化后的 SQL 语句
 */
function formatSqlData(data) {
  let sql = '';

  // 添加 collections 数据
  if (Object.keys(data.collections).length > 0) {
    sql += 'LOCK TABLES `collections` WRITE;\n';
    sql += '/*!40000 ALTER TABLE `collections` DISABLE KEYS */;\n';
    sql += 'INSERT INTO `collections` VALUES ';

    const collectionsValues = Object.values(data.collections);
    sql += collectionsValues.map((item) => `(${item})`).join(',');
    sql += ';\n';
    sql += '/*!40000 ALTER TABLE `collections` ENABLE KEYS */;\n';
    sql += 'UNLOCK TABLES;\n\n';
  }

  // 添加 fields 数据
  if (Object.keys(data.fields).length > 0) {
    sql += 'LOCK TABLES `fields` WRITE;\n';
    sql += '/*!40000 ALTER TABLE `fields` DISABLE KEYS */;\n';
    sql += 'INSERT INTO `fields` VALUES ';

    const fieldsValues = Object.values(data.fields).flat();
    sql += fieldsValues.map((item) => `(${item})`).join(',');
    sql += ';\n';
    sql += '/*!40000 ALTER TABLE `fields` ENABLE KEYS */;\n';
    sql += 'UNLOCK TABLES;\n\n';
  }

  // 添加表定义
  Object.entries(data.tableDefinitions).forEach(([model, definition]) => {
    sql += `--\n-- Table structure for table \`${model}\`\n--\n\n`;
    sql += definition.dropTable + '\n\n';

    if (definition.createTable) {
      sql += definition.createTable + '\n\n';
    }
  });

  return sql;
}

/**
 * 主函数
 *
 * @param {string} sqlFilePath - SQL 文件路径
 * @param {string[]} models - 要提取的模型名称数组
 * @param {string} outputFilePath - 输出文件路径
 */
function main(sqlFilePath, models, outputFilePath) {
  const fs = require('fs');

  // 读取 SQL 文件
  const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

  // 提取数据
  const extractedData = extractSqlData(sqlContent, models);

  // 格式化数据
  const formattedSql = formatSqlData(extractedData);

  // 写入输出文件
  fs.writeFileSync(outputFilePath, formattedSql);

  console.log(`已成功提取 ${models.join(', ')} 的数据到 ${outputFilePath}`);
}

// 如果直接运行此脚本，则执行主函数
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.length < 3) {
    console.log('用法: node extract_sql_data.js <sql文件路径> <模型名称1,模型名称2,...> <输出文件路径>');
    process.exit(1);
  }

  const sqlFilePath = args[0];
  const models = args[1].split(',');
  const outputFilePath = args[2];

  main(sqlFilePath, models, outputFilePath);
}

// 导出函数以便其他模块使用
module.exports = {
  extractSqlData,
  formatSqlData,
  main,
};
