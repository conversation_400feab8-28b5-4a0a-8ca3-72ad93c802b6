const axios = require('axios');
const fs = require('fs');
const path = require('path');

const _sheetKey = 'LWRKsETd8hIp3ut6CVxj9ZGepSe';
let _token;

function numToSSColumnLetter(num) {
  let columnLetter = '',
    t;

  while (num > 0) {
    t = (num - 1) % 26;
    columnLetter = String.fromCharCode(65 + t) + columnLetter;
    num = ((num - t) / 26) | 0;
  }
  return columnLetter || undefined;
}

async function getToken() {
  if (!process.env.FEISHU_I18N_BOT_APP_ID && !process.env.FEISHU_I18N_BOT_APP_SECRET) {
    try {
      const s = fs.readFileSync(path.join(__dirname, '..', '..', '.feishu'), 'utf-8').split('\n');
      if (!s[0] || !s[1]) throw new Error('');
      process.env.FEISHU_I18N_BOT_APP_ID = s[0];
      process.env.FEISHU_I18N_BOT_APP_SECRET = s[1];
    } catch (err) {
      console.error('未查到相关的飞书应用配置');
      process.exit(0);
    }
  }

  const res = await axios.post('https://open.larksuite.com/open-apis/auth/v3/tenant_access_token/internal', {
    app_id: process.env.FEISHU_I18N_BOT_APP_ID,
    app_secret: process.env.FEISHU_I18N_BOT_APP_SECRET,
  });
  if (res.data.code !== 0) {
    throw new Error(res.data.msg);
  }
  _token = res.data.tenant_access_token;

  return res.data.tenant_access_token;
}

async function getSpreadsheets(sheetKey = _sheetKey, token = _token) {
  try {
    const res = await axios.get(`https://open.larksuite.com/open-apis/sheets/v2/spreadsheets/${sheetKey}/metainfo`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    if (res.data.code !== 0) {
      throw new Error(res.data.msg);
    }
    return res.data.data;
  } catch (err) {
    console.error(err.response);
    throw err;
  }
}

async function addSheet(title, token = _token, sheetKey = _sheetKey) {
  try {
    const res = await axios.post(
      `https://open.larksuite.com/open-apis/sheets/v2/spreadsheets/${sheetKey}/sheets_batch_update`,
      {
        requests: [
          {
            addSheet: {
              properties: {
                title,
              },
            },
          },
        ],
      },
      {
        headers: {
          ContentType: 'application/json',
          Authorization: `Bearer ${token}`,
        },
      },
    );
    if (res.data.code !== 0) {
      throw new Error(res.data.msg);
    }
    return res.data.data.replies[0].addSheet.properties;
  } catch (err) {
    console.error(err.response);
    throw err;
  }
}

async function readSheetRows(sheetId, rowCount, columnCount, sheetKey = _sheetKey, token = _token) {
  try {
    const res = await axios.get(
      `https://open.larksuite.com/open-apis/sheets/v2/spreadsheets/${sheetKey}/values/${sheetId}!A1:${numToSSColumnLetter(
        columnCount,
      )}${rowCount}`,
      {
        headers: {
          ContentType: 'application/json',
          Authorization: `Bearer ${token}`,
        },
      },
    );
    if (res.data.code !== 0) {
      throw new Error(res.data.msg);
    }
    return res.data.data.valueRange.values;
  } catch (err) {
    console.error(err.response);
    throw err;
  }
}

async function valuesAppend(sheetId, values, rowIndex, columnIndex, token = _token, sheetKey = _sheetKey) {
  try {
    const res = await axios.post(
      `https://open.larksuite.com/open-apis/sheets/v2/spreadsheets/${sheetKey}/values_append`,
      {
        valueRange: {
          range: `${sheetId}${
            rowIndex && columnIndex
              ? `!${numToSSColumnLetter(columnIndex)}${rowIndex}:${numToSSColumnLetter(columnIndex)}`
              : ''
          }`,
          values,
        },
      },
      {
        headers: {
          ContentType: 'application/json',
          Authorization: `Bearer ${token}`,
        },
      },
    );
    if (res.data.code !== 0) {
      throw new Error(res.data.msg);
    }
    return res.data.data;
  } catch (err) {
    console.error(err.response);
    throw err;
  }
}

module.exports = {
  getToken,
  getSpreadsheets,
  addSheet,
  readSheetRows,
  valuesAppend,
};
