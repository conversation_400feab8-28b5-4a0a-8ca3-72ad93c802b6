// const pLimit = require('p-limit');

module.exports = {
  execQueue: function (actions, maxConcurrent = 1) {
    return import('p-limit').then(({ default: pLimit }) => {
      // var queue = new Queue(maxConcurrent, Infinity);
      const limit = pLimit(maxConcurrent);
      // actions.forEach((a) => queue.add(a));
      // console.log('🚀 ~ file: queue.js ~ line 7 ~ queue', queue.then);
      return Promise.all(actions.map((a) => limit(a)));
    });
  },
  delay: function (ms = 1000) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  },
};
