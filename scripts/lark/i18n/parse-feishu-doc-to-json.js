const _ = require('lodash');

const feishu = require('./libs/feishu');

const feishuDocUrl = process.argv[2];
const targetFile = process.argv[3] || './parse-feishu-doc-output.json';

async function parse() {
  await feishu.getToken();
  const sheetKey = feishuDocUrl.split('/').pop();
  const { sheets } = await feishu.getSpreadsheets(sheetKey);
  // console.log("🚀 ~ file: parse-feishu-doc-to-json.js ~ line 9 ~ parse ~ token", token)
  const sheet = sheets[0];
  const remoteRows = await feishu.readSheetRows(
    sheet.sheetId,
    sheet.rowCount,
    sheet.columnCount,
    sheetKey,
  );

  const keys = remoteRows[0].slice(0);

  const result = [];

  remoteRows.slice(1).forEach((row) => {
    let item = {};
    if (!row.filter(Boolean).length) return;
    row.forEach((v, i) => {
      if (!v) return;
      if (_.isString(v) || _.isNumber(v)) {
        item[keys[i]] = v;
        return;
      }
      if (_.isArray(v) && v[0].type === 'url') {
        item[keys[i]] = v[0].text;
        return;
      }
      throw new Error('未知数据类型: ' + JSON.stringify(v));
    });
    result.push(item);
  });

  require('fs').writeFileSync(targetFile, JSON.stringify(result), 'utf-8');
}

parse();
