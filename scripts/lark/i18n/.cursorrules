1. 把 @cryptoStat 目录下的所有硬编码文本报错中英文搜索出来
2. 使用const { t } = useTranslation();的方式，进行多语言配置，key的命名格式为: crypto_stat.[模块名].[key]
3. 替换完毕后，使用代码块输出被替换的：key、en文本、zh-CN文本，每个配置一行，之间用tab符号分割，方便我复制到excel文件中，输出的key不需要crypto_stat. ，即[模块名].[key]

1.把 @features 目录下的多有页面中用到的组件，所有硬编码文本报错中英文搜索出来
2.使用const { t } = useTranslation();的方式，进行多语言配置，key的命名格式为: features_page.[页面名].[模块名].[key]
3. 配置多语言完毕后，使用markdown代码块输出被替换的：key、en文本、zh-CN文本，每个配置一行，之间用tab符号分割，方便我复制到excel文件中，输出的key不需要前缀features_page. ，即[页面名].[模块名].[key]