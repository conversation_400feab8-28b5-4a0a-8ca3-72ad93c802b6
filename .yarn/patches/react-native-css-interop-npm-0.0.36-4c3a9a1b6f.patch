diff --git a/dist/runtime/web/color-scheme.js b/dist/runtime/web/color-scheme.js
index b1d62c64c5095129b9531263bb04f3c40d163f43..868c9d3e494ab6272bd980628571b9b6a60b1db2 100644
--- a/dist/runtime/web/color-scheme.js
+++ b/dist/runtime/web/color-scheme.js
@@ -14,7 +14,7 @@ if (darkMode === "class") {
         ? "dark"
         : "light";
 }
-const systemColorScheme = (0, observable_1.observable)(appearance.getColorScheme() ?? "light");
+const systemColorScheme = (0, observable_1.observable)(appearance.getColorScheme?.() ?? "light");
 const colorSchemeObservable = (0, observable_1.observable)(initialColor, { fallback: systemColorScheme });
 exports.colorScheme = {
     set(value) {
@@ -41,7 +41,7 @@ exports.colorScheme = {
     toggle() {
         let current = colorSchemeObservable.get();
         if (current === undefined)
-            current = appearance.getColorScheme() ?? "light";
+            current = appearance.getColorScheme?.() ?? "light";
         exports.colorScheme.set(current === "light" ? "dark" : "light");
     },
     [shared_1.INTERNAL_RESET]: (appearance) => {
@@ -52,7 +52,7 @@ exports.colorScheme = {
 function resetAppearanceListeners($appearance) {
     appearance = $appearance;
     appearanceListener?.remove();
-    appearanceListener = appearance.addChangeListener((state) => {
+    appearanceListener = appearance.addChangeListener?.((state) => {
         if (react_native_1.AppState.currentState === "active") {
             systemColorScheme.set(state.colorScheme ?? "light");
         }
