{"openapi": "3.1.0", "info": {"title": "StockBits[TEST]", "version": "0.0.1"}, "paths": {"/api/v1/private/user/info": {"get": {"tags": ["User"], "summary": "User Info", "operationId": "user_info_api_v1_private_user_info_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserMetadataResp"}}}}}}}, "/api/v1/private/watchlist/add": {"post": {"tags": ["WatchList"], "summary": "添加 Watchlist", "operationId": "添加_watchlist_api_v1_private_watchlist_add_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetBaseParam"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/private/watchlist/remove": {"post": {"tags": ["WatchList"], "summary": "取消 Watchlist", "operationId": "取消_watchlist_api_v1_private_watchlist_remove_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetBaseParam"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/private/watchlist/list": {"post": {"tags": ["WatchList"], "summary": "Watchlist 列表", "operationId": "watchlist_列表_api_v1_private_watchlist_list_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WatchListItemResp"}}}}}}}, "/api/v1/private/watchlist/status": {"post": {"tags": ["WatchList"], "summary": "指定资产是否在 Watchlist 中", "operationId": "指定资产是否在_watchlist_中_api_v1_private_watchlist_status_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetBaseParam"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WatchListStatusResp"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/private/watchlist/order": {"post": {"tags": ["WatchList"], "summary": "更新 Watchlist 顺序", "operationId": "更新_watchlist_顺序_api_v1_private_watchlist_order_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WatchListOrderParam"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/public/asset/topbar": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Topbar Asset List", "operationId": "topbar_asset_list_api_v1_public_asset_topbar_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TopBarAssetListResp"}}}}}}}, "/api/v1/public/asset/search": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Search", "operationId": "search_api_v1_public_asset_search_get", "parameters": [{"name": "query_text", "in": "query", "required": true, "schema": {"type": "string", "description": "search keyword", "title": "Query Text"}, "description": "search keyword"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchResultListResp"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/public/asset/market-sentiment": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "市场情绪指标", "operationId": "市场情绪指标_api_v1_public_asset_market_sentiment_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarketsOverviewResp"}}}}}}}, "/api/v1/public/asset/market-trending": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "热门市场排行", "operationId": "热门市场排行_api_v1_public_asset_market_trending_get", "parameters": [{"name": "market", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/Market", "description": "所属市场"}, "description": "所属市场"}, {"name": "sort", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/MarketTrendingSort", "description": "排序"}, "description": "排序"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrendingResp"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/public/asset/hot-searches": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "热搜榜", "operationId": "热搜榜_api_v1_public_asset_hot_searches_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrendingResp"}}}}}}}, "/api/v1/public/asset/search-events": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "搜索命中记录上报", "operationId": "搜索命中记录上报_api_v1_public_asset_search_events_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchEventParam"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/public/asset/asset-detail": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "资产详情", "operationId": "资产详情_api_v1_public_asset_asset_detail_get", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetBase"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetDetailResp"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/public/media/social-medias": {"get": {"tags": ["Media"], "summary": "社交媒体消息", "operationId": "社交媒体消息_api_v1_public_media_social_medias_get", "parameters": [{"name": "sort", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SocialMediaSort", "description": "排序"}, "description": "排序"}, {"name": "market", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/Market"}, {"type": "null"}], "title": "Market"}}, {"name": "symbol", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Symbol"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SocialMediaResp"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/healthcheck": {"get": {"tags": ["HealthCheck"], "summary": "检查", "operationId": "检查_healthcheck_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/oauth/google": {"post": {"tags": ["OAuth"], "summary": "Google Oauth", "operationId": "google_oauth_oauth_google_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoogleOAuthSignInParam"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthSignInResp"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/email/send": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Email Authorize Send", "operationId": "email_authorize_send_auth_email_send_post", "parameters": [{"name": "X-Real-Ip", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Real-Ip"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthEmailSendParam"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/email/signup": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Email Authorize Signup/Signin", "operationId": "email_authorize_signup_signin_auth_email_signup_post", "parameters": [{"name": "X-Real-Ip", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Real-Ip"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthEmailConfirmParam"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthSignInResp"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"AssetBase": {"properties": {"market": {"$ref": "#/components/schemas/Market", "description": ""}, "symbol": {"type": "integer", "title": "Symbol", "description": "symbol"}}, "type": "object", "required": ["market", "symbol"], "title": "AssetBase"}, "AssetBaseParam": {"properties": {"market": {"$ref": "#/components/schemas/Market", "description": ""}, "symbol": {"type": "integer", "title": "Symbol", "description": "symbol"}}, "type": "object", "required": ["market", "symbol"], "title": "AssetBaseParam"}, "AssetDetail": {"properties": {"market": {"$ref": "#/components/schemas/Market", "description": ""}, "symbol": {"type": "integer", "title": "Symbol", "description": "symbol"}, "price": {"type": "string", "title": "Price", "description": "价格"}, "value": {"type": "string", "title": "Value", "description": "成交额"}, "market_cap": {"type": "string", "title": "Market Cap", "description": "市值"}, "fdv": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Fdv", "description": "完全稀释市值(加密)"}, "pe": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Pe", "description": "市盈率(股票)"}, "total_supply": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Total Supply", "description": "最大供给量(加密)"}, "total_share": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Total Share", "description": "总股本(股票)"}}, "type": "object", "required": ["market", "symbol", "price", "value", "market_cap"], "title": "AssetDetail"}, "AssetDetailResp": {"properties": {"status": {"type": "integer", "title": "Status"}, "msg": {"type": "string", "title": "Msg"}, "data": {"$ref": "#/components/schemas/AssetDetail"}}, "type": "object", "required": ["status", "msg", "data"], "title": "AssetDetailResp"}, "AuthEmailConfirmParam": {"properties": {"code": {"type": "string", "title": "Code", "description": "验证码"}}, "type": "object", "required": ["code"], "title": "AuthEmailConfirmParam"}, "AuthEmailSendParam": {"properties": {"inviter": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Inviter", "description": "邀请码"}, "email": {"type": "string", "title": "Email", "description": "google client id"}}, "type": "object", "required": ["email"], "title": "AuthEmailSendParam"}, "AuthSignInResp": {"properties": {"status": {"type": "integer", "title": "Status"}, "msg": {"type": "string", "title": "Msg"}, "data": {"$ref": "#/components/schemas/AuthSignInfo"}}, "type": "object", "required": ["status", "msg", "data"], "title": "AuthSignInResp"}, "AuthSignInfo": {"properties": {"access_token": {"type": "string", "title": "Access Token", "description": "access_token"}, "email": {"type": "string", "title": "Email", "description": "email"}}, "type": "object", "required": ["access_token", "email"], "title": "AuthSignInfo", "description": "登陆"}, "BooleanResult": {"properties": {"result": {"type": "boolean", "title": "Result", "description": ""}}, "type": "object", "required": ["result"], "title": "BooleanResult"}, "CommonResponse": {"properties": {"status": {"type": "integer", "title": "Status"}, "msg": {"type": "string", "title": "Msg"}, "data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"items": {}, "type": "array"}, {"type": "null"}], "title": "Data"}}, "type": "object", "required": ["status", "msg", "data"], "title": "CommonResponse"}, "GoogleOAuthSignInParam": {"properties": {"inviter": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Inviter", "description": "邀请码"}, "google_client_id": {"type": "string", "title": "Google Client Id", "description": "google client id"}, "google_credential": {"type": "string", "title": "Google Credential", "description": "google credential"}}, "type": "object", "required": ["google_client_id", "google_credential"], "title": "GoogleOAuthSignInParam"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "Market": {"type": "string", "enum": ["US", "HK", "CRYPTO"], "title": "Market"}, "MarketOverviewItem": {"properties": {"market": {"$ref": "#/components/schemas/Market", "description": ""}, "symbol": {"type": "integer", "title": "Symbol", "description": "symbol"}, "price": {"type": "string", "title": "Price", "description": "price"}, "change_rate": {"type": "string", "title": "Change Rate", "description": "价格涨跌幅"}, "long_short_ratio": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Long Short Ratio", "description": "Binance多空比例"}, "fear_greed_index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Fear Greed Index", "description": "恐惧贪婪指数"}, "alt_coin_season": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Alt Coin Season", "description": "山寨指数"}, "vix_index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Vix Index", "description": "VIX 恐慌指数"}, "pc_ratio": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Pc Ratio", "description": "看涨看跌比例 Put/Call Ratio"}, "social_sentiment": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Social Sentiment", "description": "社交媒体情绪"}}, "type": "object", "required": ["market", "symbol", "price", "change_rate"], "title": "MarketOverviewItem"}, "MarketTrendingSort": {"type": "string", "enum": ["VALUE_DESC", "CHANGE_RATE_ASC", "CHANGE_RATE_DESC"], "title": "MarketTrendingSort"}, "MarketsOverviewResp": {"properties": {"status": {"type": "integer", "title": "Status"}, "msg": {"type": "string", "title": "Msg"}, "data": {"items": {"$ref": "#/components/schemas/MarketOverviewItem"}, "type": "array", "title": "Data"}}, "type": "object", "required": ["status", "msg", "data"], "title": "MarketsOverviewResp"}, "SearchEventParam": {"properties": {"market": {"$ref": "#/components/schemas/Market", "description": ""}, "symbol": {"type": "integer", "title": "Symbol", "description": "symbol"}, "search_text": {"type": "string", "title": "Search Text", "description": "搜索关键字"}}, "type": "object", "required": ["market", "symbol", "search_text"], "title": "SearchEventParam"}, "SearchResultItem": {"properties": {"market": {"$ref": "#/components/schemas/Market", "description": ""}, "symbol": {"type": "integer", "title": "Symbol", "description": "symbol"}, "price": {"type": "string", "title": "Price", "description": "价格"}, "change_rate": {"type": "string", "title": "Change Rate", "description": "价格涨跌幅"}}, "type": "object", "required": ["market", "symbol", "price", "change_rate"], "title": "SearchResultItem"}, "SearchResultListResp": {"properties": {"status": {"type": "integer", "title": "Status"}, "msg": {"type": "string", "title": "Msg"}, "data": {"items": {"$ref": "#/components/schemas/SearchResultItem"}, "type": "array", "title": "Data"}}, "type": "object", "required": ["status", "msg", "data"], "title": "SearchResultListResp"}, "SocialMediaItem": {"properties": {"market": {"$ref": "#/components/schemas/Market", "description": ""}, "symbol": {"type": "integer", "title": "Symbol", "description": "symbol"}, "title": {"type": "string", "title": "Title", "description": "价格"}, "content": {"type": "string", "title": "Content", "description": "成交量"}, "image": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image", "description": "图片"}, "create_ts": {"type": "integer", "title": "Create Ts", "description": "创作时间"}, "creator_name": {"type": "string", "title": "Creator Name", "description": "创作者名称"}, "creator_picture": {"type": "string", "title": "Creator Picture", "description": "创作者头像"}}, "type": "object", "required": ["market", "symbol", "title", "content", "create_ts", "creator_name", "creator_picture"], "title": "SocialMediaItem"}, "SocialMediaResp": {"properties": {"status": {"type": "integer", "title": "Status"}, "msg": {"type": "string", "title": "Msg"}, "data": {"items": {"$ref": "#/components/schemas/SocialMediaItem"}, "type": "array", "title": "Data"}}, "type": "object", "required": ["status", "msg", "data"], "title": "SocialMediaResp"}, "SocialMediaSort": {"type": "string", "enum": ["NEWEST", "HOTTEST"], "title": "SocialMediaSort"}, "TopBar": {"properties": {"fixed": {"items": {"$ref": "#/components/schemas/TopBarAssetItem"}, "type": "array", "title": "Fixed"}, "floating": {"items": {"$ref": "#/components/schemas/TopBarAssetItem"}, "type": "array", "title": "Floating"}}, "type": "object", "required": ["fixed", "floating"], "title": "TopBar"}, "TopBarAssetItem": {"properties": {"market": {"$ref": "#/components/schemas/Market", "description": ""}, "symbol": {"type": "integer", "title": "Symbol", "description": "symbol"}, "change_rate": {"type": "string", "title": "Change Rate", "description": "价格涨跌幅"}}, "type": "object", "required": ["market", "symbol", "change_rate"], "title": "TopBarAssetItem"}, "TopBarAssetListResp": {"properties": {"status": {"type": "integer", "title": "Status"}, "msg": {"type": "string", "title": "Msg"}, "data": {"$ref": "#/components/schemas/TopBar"}}, "type": "object", "required": ["status", "msg", "data"], "title": "TopBarAssetListResp"}, "TrendingItem": {"properties": {"market": {"$ref": "#/components/schemas/Market", "description": ""}, "symbol": {"type": "integer", "title": "Symbol", "description": "symbol"}, "price": {"type": "string", "title": "Price", "description": "价格"}, "value": {"type": "string", "title": "Value", "description": "成交额"}, "market_cap": {"type": "string", "title": "Market Cap", "description": "市值"}}, "type": "object", "required": ["market", "symbol", "price", "value", "market_cap"], "title": "TrendingItem"}, "TrendingResp": {"properties": {"status": {"type": "integer", "title": "Status"}, "msg": {"type": "string", "title": "Msg"}, "data": {"items": {"$ref": "#/components/schemas/TrendingItem"}, "type": "array", "title": "Data"}}, "type": "object", "required": ["status", "msg", "data"], "title": "TrendingResp"}, "UserMetadata": {"properties": {"email": {"type": "string", "title": "Email"}, "username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Username"}, "introduction": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Introduction"}, "avatar_link": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatar Link"}}, "type": "object", "required": ["email", "username", "introduction", "avatar_link"], "title": "UserMetadata"}, "UserMetadataResp": {"properties": {"status": {"type": "integer", "title": "Status"}, "msg": {"type": "string", "title": "Msg"}, "data": {"$ref": "#/components/schemas/UserMetadata"}}, "type": "object", "required": ["status", "msg", "data"], "title": "UserMetadataResp"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "WatchListItem": {"properties": {"market": {"$ref": "#/components/schemas/Market", "description": ""}, "symbol": {"type": "integer", "title": "Symbol", "description": "symbol"}, "price": {"type": "string", "title": "Price", "description": "价格"}, "change_rate": {"type": "string", "title": "Change Rate", "description": "价格涨跌幅"}, "sort_order": {"type": "integer", "title": "Sort Order", "description": "排序"}}, "type": "object", "required": ["market", "symbol", "price", "change_rate", "sort_order"], "title": "WatchListItem"}, "WatchListItemResp": {"properties": {"status": {"type": "integer", "title": "Status"}, "msg": {"type": "string", "title": "Msg"}, "data": {"items": {"$ref": "#/components/schemas/WatchListItem"}, "type": "array", "title": "Data"}}, "type": "object", "required": ["status", "msg", "data"], "title": "WatchListItemResp"}, "WatchListOrderParam": {"properties": {"market": {"$ref": "#/components/schemas/Market", "description": ""}, "symbol": {"type": "integer", "title": "Symbol", "description": "symbol"}, "new_order": {"type": "integer", "title": "New Order", "description": "顺序"}}, "type": "object", "required": ["market", "symbol", "new_order"], "title": "WatchListOrderParam"}, "WatchListStatusResp": {"properties": {"status": {"type": "integer", "title": "Status"}, "msg": {"type": "string", "title": "Msg"}, "data": {"$ref": "#/components/schemas/BooleanResult"}}, "type": "object", "required": ["status", "msg", "data"], "title": "WatchListStatusResp"}}}}