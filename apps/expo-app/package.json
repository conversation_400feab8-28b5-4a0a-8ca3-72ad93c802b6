{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "EXPO_USE_METRO_WORKSPACE_ROOT=1 npx expo start", "android": "EXPO_USE_METRO_WORKSPACE_ROOT=1 npx expo start --android", "ios": "EXPO_USE_METRO_WORKSPACE_ROOT=1 npx expo start --ios", "web": "EXPO_USE_METRO_WORKSPACE_ROOT=1 npx expo start --web", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "^1.23.1", "@react-navigation/native": "^6.0.2", "@unitools/image-expo": "^0.0.5", "@unitools/link-expo": "^0.0.1", "@unitools/router-expo": "^0.0.1", "babel-preset-expo": "~12.0.0", "expo": "^52.0.39", "expo-font": "~13.0.4", "expo-image": "~2.0.6", "expo-linking": "~7.0.5", "expo-router": "~4.0.19", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.8", "expo-updates": "~0.27.4", "expo-web-browser": "~14.0.2", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.7", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-web": "~0.19.13"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.3.12", "@unitools/babel-plugin-universal-image": "^1.0.0", "babel-plugin-module-resolver": "^5.0.2", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react-test-renderer": "18.2.0", "typescript": "~5.3.3"}, "private": true}