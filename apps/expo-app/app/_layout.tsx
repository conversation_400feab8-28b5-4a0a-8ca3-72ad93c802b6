import FontAwesome from "@expo/vector-icons/FontAwesome";
import {
  DarkTheme,
  DefaultTheme,
} from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { useEffect } from "react";
import { GluestackUIProvider } from "@/components/ui/gluestack-ui-provider";
import { useColorScheme } from "nativewind";
import "../global.css";
import { ThemeProvider, useTheme } from "@/components/ui/ThemeProvider/ThemeProvider";

export {
  // Catch any errors thrown by the Layout component.
  ErrorBoundary,
} from "expo-router";

export const unstable_settings = {
  // Ensure that reloading on `/modal` keeps a back button present.
  initialRouteName: "(tabs)",
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded, error] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
    ...FontAwesome.font,
  });

  // Expo Router uses Error Boundaries to catch errors in the navigation tree.
  useEffect(() => {
    if (error) throw error;
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return <ThemeProvider><RootLayoutNav /></ThemeProvider>;
}

function RootLayoutNav() {
  const { theme, mode } = useTheme();
  return (
    <GluestackUIProvider theme={theme} mode={mode}>

      <Stack 
        screenOptions={{ 
          headerShown: false,
          contentStyle: {
            backgroundColor: theme === 'dark' ? '#121212' : '#FFFFFF'
          }
        }}
      >
        <Stack.Screen name="index" />
        <Stack.Screen name="profile/profile" />
        <Stack.Screen name="auth/signin" />
        <Stack.Screen name="echart" />
        <Stack.Screen name="+not-found" />

      </Stack>

    </GluestackUIProvider>
  );
}
