'use client';
import ResearchReportList from '@quantum/app/components/ResearchReport/ResearchReportList';
import { usePathnameHook } from '@quantum/app/hooks/usePathnameHook';
import { Box } from '@quantum/components/ui/box';
import { HStack } from '@quantum/components/ui/hstack';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import Link from '@unitools/link';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

export default function ReportLayout({
  children,
  menu,
}: {
  children: React.ReactNode;
  menu: {
    symbol: string;
    label: string;
  }[];
}) {
  const { t } = useTranslation();

  const pathname = usePathnameHook();
  const currentRoute = useMemo(() => {
    return menu.find((item) => `/crypto-stat/${item.symbol}` === pathname);
  }, [pathname, menu]);
  return (
    <HStack className="min-h-screen">
      <VStack className="w-[260px] gap-2 border-r border-[#E6E6E6] px-6 mt-6 overflow-auto">
        {menu.map((item) => (
          <Link href={`/crypto-stat/${item.symbol}`} key={item.symbol}>
            <HStack
              className={`h-[52px] items-center gap-2 px-2 text-[#0A0A0A] bg-opacity-[0.03] ${
                pathname === `/crypto-stat/${item.symbol}` ? 'bg-[#05C697] text-[#05C697]' : ''
              } hover:bg-opacity-[0.03] hover:bg-[#05C697] hover:text-[#05C697]`}
            >
              {item.label}
            </HStack>
          </Link>
        ))}
      </VStack>
      <HStack className="flex-1 p-5 gap-6 w-full px-[31px] ">
        <VStack className="flex-1 gap-6">
          {!!currentRoute && (
            <h1 className="text-[24px] font-[700] leading-[32px] text-[#0A0A0A]">
              <Text>{currentRoute?.label}</Text>
            </h1>
          )}

          {children}
        </VStack>
      </HStack>
    </HStack>
  );
}
