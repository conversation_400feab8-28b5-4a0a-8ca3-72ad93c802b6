'use client';

import i18n, { addResourceBundle, i18nLanguages, initI18n } from '@/common/translations/i18n';
import Nav from '@quantum/app/components/Nav';
import { ToastProvider } from '@quantum/app/components/Toast/ToastProvider';
import { authStore } from '@quantum/app/store/auth.store';
import { Box } from '@quantum/components/ui/box';
import { GluestackUIProvider } from '@quantum/components/ui/gluestack-ui-provider';
import { useTheme } from '@quantum/components/ui/ThemeProvider/ThemeProvider';
import { VStack } from '@quantum/components/ui/vstack';
import { useCookieState, useMemoizedFn, useUpdate } from 'ahooks';
import { usePathname, useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { I18nextProvider } from 'react-i18next';
import Footer from '../Footer';
import { isBrowser } from '@/common';

type I18nResources = {
  [lang: string]: {
    [ns: string]: any;
  };
};

import dynamic from 'next/dynamic';

// 动态导入错误边界组件（客户端组件）
const ErrorBoundary = dynamic(() => import('@/components/ErrorBoundary'), {
  ssr: false,
});
export default function PageLayout({
  children,
  i18nResources,
  lang,
  currentDomain,
}: Readonly<{
  children: any;
  i18nResources: I18nResources;
  lang: string;
  currentDomain: string
}>) {
  const pathname = usePathname();
  const { theme, mode } = useTheme();
  // const isInitialized = useRef(false);

  // 服务端渲染时执行
  if (!i18n.isInitialized) {
    initI18n(lang, i18nResources, currentDomain);
  }

  if (!isBrowser()) {
    if (!i18n.hasResourceBundle(lang, 'translation')) {
      addResourceBundle(lang, i18nResources);
    }
    i18n.changeLanguage(lang);
  }

  const updateLang = useMemoizedFn((newLang: string) => {
    const pathnames = pathname.split('/');
    const pathLang = pathnames[1];

    if (i18nLanguages.includes(pathLang)) {
      if (newLang === 'en') {
        pathnames.splice(1, 1);
      } else {
        pathnames[1] = newLang;
      }
    } else {
      pathnames.splice(1, 0, newLang);
    }

    try {
      // 获取当前URL的查询参数部分
      const searchParams = window.location.search;
      // 构建新URL时保留查询参数
      const newPath = (pathnames.join('/') || '/en') + searchParams;
      history.pushState({}, '', newPath);
    } catch (error) {
      console.error(error);
    }
  });

  // // 客户端渲染时执行，避免重复
  useEffect(() => {
    i18n.changeLanguage(lang);
    i18n.on('languageChanged', updateLang);
    return () => {
      i18n.off('languageChanged', updateLang);
    };
  }, [lang, updateLang]);

  const [cookieLocale] = useCookieState('Locale');
  useEffect(() => {
    if (cookieLocale !== lang) {
      i18n.changeLanguage(cookieLocale);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const params = useSearchParams();
  const safeParams = params || new URLSearchParams();
  const inviteCode = safeParams.get('inviteCode');
  authStore.setInviteCode(inviteCode);

  return (
    <GluestackUIProvider theme={theme} mode={mode}>
      <I18nextProvider i18n={i18n} defaultNS={'translation'}>
        <ToastProvider>
          {/* <ErrorBoundary> */}
          <VStack className="w-full min-h-screen">
            <Box className="sticky top-0 z-10 bg-white">
              <Nav />
            </Box>
            <Box className="flex-1">{children}</Box>
            <Footer />
          </VStack>
          {/* </ErrorBoundary> */}
        </ToastProvider>
      </I18nextProvider>
    </GluestackUIProvider>
  );
}
