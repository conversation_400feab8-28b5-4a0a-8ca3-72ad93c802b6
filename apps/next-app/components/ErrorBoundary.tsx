'use client';

import { Button } from '@quantum/components/ui/button';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
  };

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // You can log the error to an error reporting service here
    if (typeof window !== 'undefined') {
      // Send to error monitoring system or log to console
      console.error('React rendering error:', {
        error: error.toString(),
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent,
      });
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null });
    // Refresh the page as a fallback solution
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <VStack className="w-full justify-center items-center py-12 px-4" space="lg">
          <Text className="text-2xl font-bold text-center">Page Loading Issue</Text>
          <Text className="text-gray-600 text-center">
            Sorry, we encountered a problem while loading this page. This may be due to an unstable network connection or a temporary server issue.
          </Text>
          <Text className="text-sm text-gray-500 text-center">
            Error details: {this.state.error?.message || 'Unknown error'}
          </Text>
          <Button onPress={this.handleRetry} className="mt-4">
            Reload
          </Button>
        </VStack>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary; 