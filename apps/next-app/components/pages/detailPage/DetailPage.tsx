import ScrollMessageBar from '@quantum/app/components/ScrollMessageBar';
import { Box } from '@quantum/components/ui/box';
import Detail from '@quantum/app/screens/detail/Detail';
import { AssetDetail } from '@quantum/app/services/asset/asset.types';
import Comments from '@quantum/app/screens/home/<USER>';
import { Market } from '@quantum/app/services/watchlist/watchlist.types';
export default function DetailPage({ defaultData, market, symbol }: { defaultData: AssetDetail | null, market: Market, symbol: string }) {
  return (
    <>
      <ScrollMessageBar key="topbar" />
      <Box className="w-full px-[60px]">
        <Box className="max-w-[1156px] w-full py-[40px] mx-auto">
          <Detail defaultData={defaultData} />

          <Box className="mt-9">
            <Comments market={market} symbol={symbol} />
          </Box>
        </Box>
      </Box>
    </>
  );
}
