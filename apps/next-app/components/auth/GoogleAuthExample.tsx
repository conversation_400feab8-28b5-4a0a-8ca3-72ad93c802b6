'use client';

import React from 'react';
import { GoogleAuth } from './GoogleAuth';
import { VStack } from '@quantum/components/ui/vstack';
import { Text } from '@quantum/components/ui/text';
import { Box } from '@quantum/components/ui/box';

/**
 * Google登录使用示例
 * 展示如何在页面中使用自定义的Google登录按钮
 */
export const GoogleAuthExample: React.FC = () => {
  return (
    <Box className="flex flex-col items-center justify-center min-h-screen p-6 bg-gray-50">
      <VStack className="w-full max-w-md space-y-6">
        <Text className="text-2xl font-bold text-center text-gray-900">
          登录到您的账户
        </Text>
        
        <Text className="text-center text-gray-600">
          使用您的Google账户快速登录
        </Text>
        
        {/* 自定义Google登录按钮 */}
        <GoogleAuth />
        
        <Text className="text-xs text-center text-gray-500">
          点击登录即表示您同意我们的服务条款和隐私政策
        </Text>
      </VStack>
    </Box>
  );
};

export default GoogleAuthExample;
