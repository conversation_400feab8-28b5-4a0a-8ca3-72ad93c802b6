'use client';

import React, { useState } from 'react';
import { Button, ButtonText, ButtonIcon } from '@quantum/components/ui/button';
import { GoogleLogin, GoogleOAuthProvider } from '@react-oauth/google';
import { Spinner } from '@quantum/components/ui/spinner';
import { ToastDescription, ToastTitle, useToast } from '@quantum/components/ui/toast';

/**
 * Google认证按钮组件
 * 点击后会调用Google OAuth登录流程，并将凭证发送到后端API
 */
export const GoogleAuth: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const toast = useToast();

  const handleLoginSuccess = async (response: any) => {
    try {
      setIsLoading(true);
      console.log('登录成功获取凭证：', response);

      const { credential } = response;

      // 将credential发送到Next.js API路由进行验证
      const res = await fetch('/api/auth/google', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ credential }),
      });

      const data = await res.json();

      if (data.success) {
        console.log('Google登录成功，用户信息：', data.user);

        // 保存用户信息到本地存储
        if (typeof window !== 'undefined') {
          localStorage.setItem('user', JSON.stringify(data.user));
        }

        // 显示成功提示
        toast.show({
          title: <ToastTitle>登录成功</ToastTitle>,
          description: <ToastDescription>{`欢迎，${data.user.name || data.user.email}`}</ToastDescription>,
          variant: 'success',
        });

        // 可以在这里添加页面跳转逻辑
        // window.location.href = '/dashboard';
      } else {
        console.error('Google登录失败', data.error);
        toast.show({
          title: <ToastTitle>登录失败</ToastTitle>,
          description: <ToastDescription>{data.error || '验证凭证时出错'}</ToastDescription>,
          variant: 'error',
        });
      }
    } catch (error) {
      console.error('处理Google登录时出错:', error);
      toast.show({
        title: <ToastTitle>登录失败</ToastTitle>,
        description: <ToastDescription>服务器连接错误，请稍后再试</ToastDescription>,
        variant: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoginFailure = () => {
    console.error('Google登录失败');
    toast.show({
      title: <ToastTitle>登录失败</ToastTitle>,
      description: <ToastDescription>Google登录过程中出错，请稍后再试</ToastDescription>,
      variant: 'error',
    });
  };

  return (
    <GoogleOAuthProvider clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || ''}>
      <div className="relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50 rounded-md">
            <Spinner size="small" />
          </div>
        )}
        <GoogleLogin
          onSuccess={handleLoginSuccess}
          onError={handleLoginFailure}
          theme="outline"
          shape="rectangular"
          text="signin_with"
          locale="zh_CN"
          // useOneTap
        />
      </div>
    </GoogleOAuthProvider>
  );
};
