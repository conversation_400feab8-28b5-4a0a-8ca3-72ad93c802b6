import { HStack } from '@quantum/components/ui/hstack';
import { Link } from '@quantum/components/ui/link';
import Image from '@unitools/image';
import logoText from '@quantum/shared/assets/logo-text.svg';
import { Text } from '@quantum/components/ui/text';
import { Button } from '@quantum/components/ui/button';
import { RiRedditLine, RiTwitterXLine } from 'react-icons/ri';
import { Box } from '@quantum/components/ui/box';

export default function Footer() {
  return (
    <HStack className="w-full h-[80px] items-center justify-center gap-10">
      <Link href="/">
        <Image source={logoText} alt="logo" width={100} height={100} />
      </Link>
      <Text>© 2025 All rights reserved</Text>
      <HStack className='gap-2'>
        <a href="https://x.com/stockbits_ai" target="_blank">
          <Box className="w-7 h-7 rounded-[8px] border-gray-200 border flex items-center justify-center">
            <RiTwitterXLine size={14} />
          </Box>
        </a>
        <a href="https://www.reddit.com/r/StockBits" target="_blank">
          <Box className="w-7 h-7 rounded-[8px] border-gray-200 border flex items-center justify-center">
            <RiRedditLine size={14} />
          </Box>
        </a>
      </HStack>
    </HStack>
  );
}
