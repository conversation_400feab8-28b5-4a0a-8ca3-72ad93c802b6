'use client';

import { useState, useEffect } from 'react';
import { Button } from '@quantum/components/ui/button';
import { Text } from '@quantum/components/ui/text';
import { useColorScheme } from 'nativewind';
import { log } from 'console';
import { useTheme } from '@quantum/components/ui/ThemeProvider/ThemeProvider';

export function ThemeToggle() {
  const { theme: colorScheme, toggleTheme: setColorScheme } = useTheme();

  const toggleTheme = () => {
    setColorScheme();
  };

  return (
    <Button variant="ghost" size="icon" onPress={toggleTheme} className="fixed top-4 right-4">
      {colorScheme === 'light' ? <Text>{colorScheme}</Text> : <Text>{colorScheme}</Text>}
    </Button>
  );
}
