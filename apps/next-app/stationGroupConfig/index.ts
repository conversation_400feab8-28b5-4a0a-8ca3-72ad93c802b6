
const i18nMap: any = {
  'localhost:3000': 'locales',
  'as-node.3bodylabs.com': 'locales_site_a',
  'tokenrader.com': 'locales_site_a',
}


export function getI18nPath(domain: any ): string {
  return i18nMap[domain] || 'locales'
}

const gaIDMap: any = {
  'stockbits.ai': 'G-9WLT4L7KNV',
  'tokenrader.com': 'G-PRWS1N1DJ3',
  'bibay.xyz': 'G-4JERRY1XSG',
  'tallnex.com': 'G-CXSS1H61C9',
}


export function getGaID(domain: any ): string {
  return gaIDMap[domain] || ''
}