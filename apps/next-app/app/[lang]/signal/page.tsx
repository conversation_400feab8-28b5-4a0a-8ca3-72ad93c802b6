import CommunityNews from '@quantum/app/screens/communityNews/CommunityNews';
import { Box } from '@quantum/components/ui/box';
import ScrollMessageBar from '@quantum/app/components/ScrollMessageBar';
import { Metadata } from 'next';



const pageTitleLangMap = {
  'zh-CN': '免费的股票和加密市场分析，交易观点和新闻',
  'en': 'Free Stock&Crypto Analysis,Trading Ideas&News',
}


export async function generateMetadata({
    params,
    searchParams,
  }: {
    params: { lang?: 'zh-CN' | 'en' };
    searchParams: { [key: string]: string | string[] | undefined };
  }): Promise<Metadata> {
    const lang = params.lang || 'en';
  return { title: pageTitleLangMap[lang] };
}



export default function News() {
  return (
    <>
      <ScrollMessageBar key='topbar'/>
      <Box className="w-full px-[60px]">
        <Box className="max-w-[1156px] w-full py-[38px] mx-auto">
          <CommunityNews />
        </Box>
      </Box>
    </>
  );
}
