import HomeScreen from '@quantum/app/screens/home';
import { getMarketOverview } from '@quantum/app/services/asset/asset.api';
import { Metadata } from 'next';
async function fetchPageData() {
  try {
    const res = await getMarketOverview();
    return {
      overviewData: res,
    };
  } catch (error) {
    console.error(error);
    return {
      overviewData: null,
    };
  }
}

const pageTitleLangMap = {
  'zh-CN': 'Stockbits | 股票和加密市场行情，分析和交易观点',
  'en': 'Stockbits | Stock&Crypto Markets Price Tracking, Analysis,Trading Ideas',
}


export async function generateMetadata({
    params,
    searchParams,
  }: {
    params: { lang?: 'zh-CN' | 'en' };
    searchParams: { [key: string]: string | string[] | undefined };
  }): Promise<Metadata> {
    const lang = params.lang || 'en';
  return { title: pageTitleLangMap[lang] };
}


export default async function HomePage() {
  const data = await fetchPageData();
  return <HomeScreen overviewData={data.overviewData} />;
}
