import { headers } from 'next/headers'

import initTranslations, { i18nNamespaces } from '@/common/translations/initTranslations';
import PageLayout from '@/components/layout/PageLayout';
import { GoogleAnalytics } from '@next/third-parties/google';
import { ThemeProvider } from '@quantum/components/ui/ThemeProvider/ThemeProvider';
import StyledJsxRegistry from '../registry';
import { Inter } from 'next/font/google';
import Script from 'next/script';
const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
});
import getConfig from 'next/config';

const { publicRuntimeConfig } = getConfig();

import { Metadata } from 'next';
import { getGaID } from '@/stationGroupConfig';

const pageTitleLangMap = {
  'zh-CN': 'Stockbits | 股票和加密市场行情,分析和交易观点',
  en: 'Stockbits | Stock&Crypto Markets Price Tracking, Analysis,Trading Ideas',
};
const pageDescriptionLangMap = {
  'zh-CN': `StockBits is the smartest and free one-stop platform for stock & crypto tracking, analysis, and community trading.
Follow, Track, Trade, Earn on Stock and Crypto Market with StockBits.`,
  en: `Stockbits is the smartest and free one-stop platform for stock & crypto tracking, analysis, and community trading.
Follow, Track, Trade, Earn on Stock and Crypto Market with StockBits.`,
};
const pageKeywordsLangMap = {
  'zh-CN': `Stockbits,Stockbits行情工具,StockBits币股行情,币股行情工具,股票市场,股票价格,股票跟踪,股票分析,股票交易观点,加密市场,加密货币价格,加密跟踪,加密分析,加密交易观点,交易工具,免费交易工具,股票交易工具,加密交易工具`,
  en: `best free portfolio tracker for stocks and crypto, how to track crypto and stocks in one place,stock and crypto portfolio management tool,how to copy pro traders’ stock picks,free crypto trading signals from top traders,best platform to follow stock trading ideas,real-time crypto trading signals from traders,free tool to analyze crypto and stock trends`,
};

const openGraphImage = 'https://stockbits.ai/assets/share.png';
export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const lang = params.lang || 'en';
  return {
    title: pageTitleLangMap[lang],
    description: pageDescriptionLangMap[lang],
    keywords: pageKeywordsLangMap[lang].split(','),
    openGraph: {
      title: pageTitleLangMap[lang],
      description: pageDescriptionLangMap[lang],
      images: [
        {
          url: openGraphImage,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      images: [
        {
          url: openGraphImage,
        },
      ],
    },
    metadataBase: new URL('https://stockbits.ai/'),
    alternates: {
      canonical: '/',
      languages: {
        'en-US': '/en',
        'zh-CN': '/zh-CN',
      },
    },
  };
}

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: { lang: string };
}>) {
  const headersList = await headers()

  const xForwardedHost = headersList.get("x-forwarded-host");
  const currentDomain = headersList.get("x-current-host");
  const gaId = getGaID(xForwardedHost)
  const { lang = 'en' } = params;
  const { resources } = await initTranslations(lang, i18nNamespaces, xForwardedHost || '');

  return (
    <html lang={lang} className={inter.className}>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <meta name="google-site-verification" content="XdVnVciB8W3pW9VY3kIJH-kq8s8Jk8UQTP-0WeFo1Wc" />
        <meta name="etag" content={publicRuntimeConfig.ETAG} />
        {process.env.APP_ENV === 'production' && !!gaId && <GoogleAnalytics gaId={gaId} />}
        <Script
          id="env"
          dangerouslySetInnerHTML={{
            __html: `
            window.__ENV__ = {
              APP_ENV: '${process.env.APP_ENV}',
              SETUP_ENV: '${process.env.SETUP_ENV}',
              VERSION: '${process.env.VERSION}',
              currentDomain: '${currentDomain}',
              xForwardedHost: '${xForwardedHost}',
            }
            `,
          }}
        />
      </head>

      <body>
        <StyledJsxRegistry>
          <ThemeProvider>
            <PageLayout i18nResources={resources} lang={lang} currentDomain={xForwardedHost || ''}>
              {children}
            </PageLayout>
          </ThemeProvider>
        </StyledJsxRegistry>
      </body>
    </html>
  );
}
