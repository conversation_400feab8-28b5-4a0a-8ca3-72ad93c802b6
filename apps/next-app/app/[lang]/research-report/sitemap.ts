import { i18nLanguages } from '@/common/translations/config';
import { getReportList } from '@quantum/app/components/ResearchReport';
import { flatten, uniqBy } from 'lodash';
import { MetadataRoute } from 'next';

const i18nLanguagesCfg = ['', ...i18nLanguages];



async function fetchPageData() {
  const list = await getReportList({
    pageSize: 1000,
  });
  return list || [];
}

export default async function sitemap({ id }: { id: number }): Promise<MetadataRoute.Sitemap> {
  const data = [
    {
      href: 'research-report',
    },
  ];
  const list = await fetchPageData();
  list.forEach((item) => {
    data.push({
      href: `research-report/${item.date}`,
    });
  });
  const ary = flatten(
    i18nLanguagesCfg.map((lang) => {
      return uniqBy(data, (item) => `${item.href}`).map((item) => ({
        url: `https://stockbits.ai${lang ? '/' + lang : ''}/${item.href}`,
        lastModified: new Date(),
      }));
    }),
  );

  return [...ary];
}
