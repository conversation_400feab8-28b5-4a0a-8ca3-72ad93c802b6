import { getReportList } from '@quantum/app/components/ResearchReport';
import ResearchReportListPageCom from '@quantum/app/components/ResearchReport/ResearchReportListPageCom';
import { Metadata } from 'next';


const pageTitleLangMap = {
    'zh-CN': 'AI 研报',
    en: 'AI Research Report',
  };
  export async function generateMetadata({
    params,
    searchParams,
  }: {
    params: { id: string; lang?: 'zh-CN' | 'en' };
    searchParams: { [key: string]: string | string[] | undefined };
  }): Promise<Metadata> {
    const lang = params.lang || 'en';
  
    return { title: `${pageTitleLangMap[lang]}` };
  }


async function fetchPageData() {
  const list = await getReportList({
    pageSize: 1000,
  });
  return list || [];
}

export default async function ResearchReportListPage() {
  const list = await fetchPageData();
  return <ResearchReportListPageCom list={list} />;
}
