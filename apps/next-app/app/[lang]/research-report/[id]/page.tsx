import { getReportList } from '@quantum/app/components/ResearchReport';
import ResearchReportDetailIndex from '@quantum/app/components/ResearchReport/ResearchReportDetailIndex';
import { Metadata } from 'next';

const pageTitleLangMap = {
  'zh-CN': 'AI 研报',
  en: 'AI Research Report',
};
export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { id: string; lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const data = await fetchPageData(params)
  const lang = params.lang || 'en';

  return { title: `${data.date} ${pageTitleLangMap[lang]}` };
}

async function fetchPageData(params: { id: string }) {
  const res = await getReportList({
    date: params.id,
  });
  return res[0] || null;
}



export default async function ResearchReportDetailPage({ params }: { params: { id: string } }) {
  const res = await fetchPageData(params);
  return <ResearchReportDetailIndex detail={res} />;
}
