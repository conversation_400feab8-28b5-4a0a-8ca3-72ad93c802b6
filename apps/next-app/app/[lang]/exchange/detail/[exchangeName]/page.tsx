import ExchangeDetail from "@quantum/app/screens/exchange/ExchangeDetail";
import { Metadata } from "next";



const pageTitleLangMap = {
  'zh-CN': ' | 股票和加密市场行情，分析和交易观点',
  'en': ' | Stock&Crypto Markets Price Tracking, Analysis,Trading Ideas',
}


export async function generateMetadata({
    params,
    searchParams,
  }: {
    params: { lang?: 'zh-CN' | 'en', exchangeName: string };
    searchParams: { [key: string]: string | string[] | undefined };
  }): Promise<Metadata> {
    const lang = params.lang || 'en';
    const exchangeName = params.exchangeName;
  return { title: exchangeName + pageTitleLangMap[lang] };
}


export default function ExchangeDetailPage({ params }: { params: { exchangeName: string } }) {
  return <ExchangeDetail exchangeName={params.exchangeName} />;
}
