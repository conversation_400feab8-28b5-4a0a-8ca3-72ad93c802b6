import { i18nLanguages } from '@/common/translations/config';
import { quickSortListCfg } from '@quantum/app/screens/exchange/rank/config';
import { getExchangeList } from '@quantum/app/services/crypto_exchange/crypto_exchange.api';
import { flatten, uniqBy } from 'lodash';
import { MetadataRoute } from 'next';

const i18nLanguagesCfg = ['', ...i18nLanguages];



async function fetchPageData() {
  const list = await getExchangeList();
  return list || [];
}

export default async function sitemap({ id }: { id: number }): Promise<MetadataRoute.Sitemap> {
  const data = [
    {
      href: 'exchange/rank',
    },
  ];
  quickSortListCfg.forEach((item) => {
    data.push({
      href: `exchange/rank?sort=${item.value}`,
    });
  });
  const list = await fetchPageData();
  list.forEach((item) => {
    data.push({
      href: `exchange/detail/${item.name}`,
    });
  });
  const ary = flatten(
    i18nLanguagesCfg.map((lang) => {
      return uniqBy(data, (item) => `${item.href}`).map((item) => ({
        url: `https://stockbits.ai${lang ? '/' + lang : ''}/${item.href}`,
        lastModified: new Date(),
      }));
    }),
  );

  return [...ary];
}
