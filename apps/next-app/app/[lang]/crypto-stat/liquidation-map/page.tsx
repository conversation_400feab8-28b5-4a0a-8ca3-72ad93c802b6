import LiquidationMapIndex from '@quantum/app/components/cryptoStat/LiquidationMap/index';
import { Metadata } from 'next';

const pageTitleLangMap = {
  'zh-CN': '清算地图',
  'en': 'Liquidation Map',
}
export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { market: string; symbol: string, lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const lang = params.lang || 'en';
  return { title: `${pageTitleLangMap[lang]}` };
}
export default async function LiquidationMapPage() {
  return <LiquidationMapIndex symbol="liquidation-map" />;
}
