
import MicroStrategyCostIndex from '@quantum/app/components/cryptoStat/MicroStrategyCost/index';
import { Metadata } from 'next';

const pageTitleLangMap = {
  'zh-CN': 'ETF 溢价折扣历史',
  'en': 'ETF Premium Discount History',
}
export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { market: string; symbol: string, lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const lang = params.lang || 'en';
  return { title: `${pageTitleLangMap[lang]}` };
}
export default async function MicroStrategyCostPage() {
  return <MicroStrategyCostIndex defaultData={null} symbol="micro-strategy-cost" />;
}
