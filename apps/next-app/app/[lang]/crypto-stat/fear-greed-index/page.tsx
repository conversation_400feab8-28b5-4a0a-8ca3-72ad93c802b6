import FearGreedIndex from '@quantum/app/components/cryptoStat/FearGreedIndex';
import { getFearGreedIndex } from '@quantum/app/services/crypto_stat/crypto_stat.api';
import type { FearGreedIndex as TypeFearGreedIndex } from '@quantum/app/services/crypto_stat/crypto_stat.types';
import Script from 'next/script';
import { Metadata } from 'next';

const pageTitleLangMap = {
  'zh-CN': '恐惧与贪婪指数',
  en: 'Fear and Greed Index',
};
export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { market: string; symbol: string; lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const lang = params.lang || 'en';
  return { title: `${pageTitleLangMap[lang]}` };
}

async function fetchData(): Promise<{ data: TypeFearGreedIndex | null }> {
  try {
    const [data] = await Promise.all([getFearGreedIndex()]);
    return { data };
  } catch (e) {
    return { data: null };
  }
}

async function getServerEnv() {
  return {
    APP_ENV: process.env.APP_ENV,
    SETUP_ENV: process.env.SETUP_ENV,
    VERSION: process.env.VERSION,
  };
}

export default async function FearGreedIndexPage() {
  const { data } = await fetchData();
  const __ENV__ = await getServerEnv();
  return (
    <>
      <Script
        id="fear-greed-index-env"
        dangerouslySetInnerHTML={{
          __html: `
    window.__SERVER_ENV__ = ${JSON.stringify(__ENV__)}
    `,
        }}
      />
      <FearGreedIndex defaultData={data} symbol="fear-greed-index" />
    </>
  );
}
