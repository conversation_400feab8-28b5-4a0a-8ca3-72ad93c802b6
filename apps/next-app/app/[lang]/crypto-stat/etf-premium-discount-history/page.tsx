import EtfPremiumDiscountHistoryIndex from '@quantum/app/components/cryptoStat/EtfPremiumDiscountHistory/index';
import { getEtfBitcoinPremiumDiscountHistory } from '@quantum/app/services/crypto_stat/crypto_stat.api';
import { Metadata } from 'next';

const pageTitleLangMap = {
  'zh-CN': 'ETF 溢价折扣历史',
  en: 'ETF Premium Discount History',
};
export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { market: string; symbol: string; lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const lang = params.lang || 'en';
  return { title: `${pageTitleLangMap[lang]}` };
}

async function fetchData() {
  try {
    const [data] = await Promise.all([getEtfBitcoinPremiumDiscountHistory()]);
    return { data };
  } catch (e) {
    return { data: [] };
  }
}
export default async function EtfPremiumDiscountHistoryPage() {
  const { data } = await fetchData();
  return <EtfPremiumDiscountHistoryIndex defaultData={data} symbol="etf-premium-discount-history" />;
}
