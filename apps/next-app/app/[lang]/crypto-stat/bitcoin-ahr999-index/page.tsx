import BitcoinAhr999Index from '@quantum/app/components/cryptoStat/BitcoinAhr999Index';
import { getAhr999Index } from '@quantum/app/services/crypto_stat/crypto_stat.api';
import { Metadata } from 'next';

const pageTitleLangMap = {
  'zh-CN': '比特币 Ahr999 指数',
  en: 'Bitcoin Ahr999 Index',
};
export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { market: string; symbol: string; lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const lang = params.lang || 'en';
  return { title: `${pageTitleLangMap[lang]}` };
}

async function fetchData() {
  try {
    const [data] = await Promise.all([getAhr999Index()]);
    return { data };
  } catch (e) {
    return {
      data: [],
    };
  }
}
export default async function BitcoinAhr999IndexPage() {
  const { data } = await fetchData();
  return <BitcoinAhr999Index defaultData={data} symbol="bitcoin-ahr999-index" />;
}
