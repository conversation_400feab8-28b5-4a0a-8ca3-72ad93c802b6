import BullMarketPeakIndicatorIndex from '@quantum/app/components/cryptoStat/BullMarketPeakIndicator/index';
import { getBullMarketPeakIndicator } from '@quantum/app/services/crypto_stat/crypto_stat.api';
import { Metadata } from 'next';

const pageTitleLangMap = {
  'zh-CN': '牛市顶峰指标',
  'en': 'Bull Market Peak Indicator',
}
export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { market: string; symbol: string, lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const lang = params.lang || 'en';
  return { title: `${pageTitleLangMap[lang]}` };
}

async function fetchData() {
  try {
    const [data] = await Promise.all([getBullMarketPeakIndicator()]);
    return { data };
  } catch (e) {
    return {
      data: [],
      faqList: [],
      reportList: [],
    };
  }
}

export default async function BullMarketPeakIndicatorPage() {
  const { data } = await fetchData();
  return <BullMarketPeakIndicatorIndex defaultData={data} symbol="bull-market-peak-indicator" />;
}
