import { i18nLanguages } from '@/common/translations/config';
import { cryptoStatMenu } from '@quantum/app/hooks/cryptoStatMenu';
import { flatten, uniqBy } from 'lodash';
import { MetadataRoute } from 'next';

const i18nLanguagesCfg = ['', ...i18nLanguages];


export default async function sitemap({ id }: { id: number }): Promise<MetadataRoute.Sitemap> {
  const data = cryptoStatMenu;
  const ary = flatten(
    i18nLanguagesCfg.map((lang) => {
      return uniqBy(data, (item) => `${item.symbol}`).map((item) => ({
        url: `https://stockbits.ai${lang ? '/' + lang : ''}/crypto-stat/${item.symbol}`,
        lastModified: new Date(),
      }));
    }),
  );

  return [...ary];
}
