import GrayScalePremiumHistoryIndex from '@quantum/app/components/cryptoStat/GrayScalePremiumHistory/index';
import { Metadata } from 'next';

const pageTitleLangMap = {
  'zh-CN': '灰度溢价历史',
  en: 'Gray Scale Premium History',
};
export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { market: string; symbol: string; lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const lang = params.lang || 'en';
  return { title: `${pageTitleLangMap[lang]}` };
}

export default async function GrayScalePremiumHistoryPage() {
  return <GrayScalePremiumHistoryIndex symbol="gray-scale-premium-history" />;
}
