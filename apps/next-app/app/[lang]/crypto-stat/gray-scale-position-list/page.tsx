import GrayScalePositionListIndex from '@quantum/app/components/cryptoStat/GrayScalePositionList';
import {
  getGrayscalePositionsHistory,
  getGrayscalePositionsSymbols,
} from '@quantum/app/services/crypto_stat/crypto_stat.api';
import { Metadata } from 'next';

const pageTitleLangMap = {
  'zh-CN': '灰度持仓列表',
  en: 'Gray Scale Position List',
};
export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { market: string; symbol: string; lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const lang = params.lang || 'en';
  return { title: `${pageTitleLangMap[lang]}` };
}
async function getData() {
  try {
    const { symbols } = await getGrayscalePositionsSymbols();
    const data = await getGrayscalePositionsHistory({
      symbol: symbols[0],
    });
    return {
      symbols,
      data,
    };
  } catch (e) {
    return {
      symbols: [],
      data: [],
    };
  }
}
async function fetchData() {
  try {
    const [result] = await Promise.all([getData()]);
    return { data: result.data, symbols: result.symbols };
  } catch (error) {
    return { data: [], symbols: [] };
  }
}

export default async function GrayScalePositionListPage() {
  const { data, symbols } = await fetchData();
  return <GrayScalePositionListIndex defaultData={data} defaultSymbol={symbols[0]} symbol="gray-scale-position-list" />;
}
