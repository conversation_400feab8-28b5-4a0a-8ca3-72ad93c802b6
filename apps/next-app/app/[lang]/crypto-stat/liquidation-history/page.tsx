import LiquidationHistoryIndex from '@quantum/app/components/cryptoStat/LiquidationHistory/index';
import { getCoinsLiquidationHistory, getSupportedExchanges } from '@quantum/app/services/crypto_stat/crypto_stat.api';
import { Metadata } from 'next';

const pageTitleLangMap = {
  'zh-CN': '清算历史',
  en: 'Liquidation History',
};
export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { market: string; symbol: string; lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const lang = params.lang || 'en';
  return { title: `${pageTitleLangMap[lang]}` };
}

const getData = async () => {
  const marketListRes = await getSupportedExchanges();
  const marketList = marketListRes.exchanges;
  //   const res = await getCoinsLiquidationHistory({
  //     exchange_list: marketList,
  //     symbol: 'BTC',
  //   });
  return {
    data: [],
    marketList,
  };
};

async function fetchData() {
  try {
    const [result] = await Promise.all([getData()]);
    return { data: result.data, marketList: result.marketList };
  } catch (error) {
    return { data: [], marketList: [] };
  }
}
export default async function LiquidationHistoryPage() {
  const { marketList, data } = await fetchData();
  return <LiquidationHistoryIndex marketList={marketList} defaultData={data} symbol="liquidation-history" />;
}
