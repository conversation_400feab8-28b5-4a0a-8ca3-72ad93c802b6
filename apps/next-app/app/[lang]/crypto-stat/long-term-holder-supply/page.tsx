
import LongTermHolderSupplyIndex from '@quantum/app/components/cryptoStat/LongTermHolderSupply/index';
import { Metadata } from 'next';

const pageTitleLangMap = {
  'zh-CN': '隐患持仓列表',
  'en': 'Long Term Holder Supply',
}
export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { market: string; symbol: string, lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const lang = params.lang || 'en';
  return { title: `${pageTitleLangMap[lang]}` };
}
export default async function LongTermHolderSupplyPage() {
  return <LongTermHolderSupplyIndex defaultData={null} symbol="long-term-holder-supply" />;
}
