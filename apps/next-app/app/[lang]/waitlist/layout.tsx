export default function WaitlistLayout({ children }: { children: React.ReactNode }) {
  return (
    <div 
      className="min-h-screen" 
      style={{
        backgroundImage: "url('/assets/waitlist/waitlist-bg-1.png')",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "left top 179px",
        backgroundSize: "auto 787px",
      }}
    >
        <div className="max-w-[1096px] mx-auto px-10">
            {children}
        </div>
    </div>
  );
}
