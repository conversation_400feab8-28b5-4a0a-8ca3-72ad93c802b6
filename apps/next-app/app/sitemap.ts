import { MetadataRoute } from 'next';
import { flatten, uniq, uniqBy } from 'lodash';
import { i18nLanguages } from '@/common/translations/config';
import { getTopBarAssets } from '@quantum/app/services/asset/asset.api';
import cryptoLinks from '@/common/sitemapData/crypto_links.json';
import usStockLinks from '@/common/sitemapData/us_stock_links.json';
import hkStockLinks from '@/common/sitemapData/hk_stock_links.json';

const i18nLanguagesCfg = ['', ...i18nLanguages];

async function fetchLinksData() {
  return [
    ...cryptoLinks,
    ...usStockLinks,
    ...hkStockLinks,
  ]
}

export default async function sitemap({ id }: { id: number }): Promise<MetadataRoute.Sitemap> {
  const data = await fetchLinksData();
  const ary = flatten(
    i18nLanguagesCfg.map((lang) => {
      return uniqBy(data, (item) => `${item.market}-${item.symbol}`).map((item) => ({
        url: `https://stockbits.ai${lang ? '/' + lang : ''}/detail/${item.market}/${item.symbol}`,
        lastModified: new Date(),
      }));
    }),
  );

  return [...defaultSitemap(), ...ary];
}

function defaultSitemap(): MetadataRoute.Sitemap {
  const urls = [
    {
      url: '',
      priority: 1,
    },
    {
      url: '/market',
      priority: 1,
    },
    {
      url: '/signal',
      priority: 1,
    },
    {
      url: '/waitlist',
      priority: 1,
    },
    {
      url: '/waitlist/join',
      priority: 1,
    },
    {
      url: '/portfolio',
      priority: 1,
    },
  ];
  const res = flatten(
    i18nLanguagesCfg.map((lang) => {
      return urls.map((url) => {
        return {
          url: `https://stockbits.ai${lang ? '/' + lang : ''}${url.url}`,
          lastModified: new Date(),
          priority: url.priority,
        };
      });
    }),
  );
  return res;
}
