
import { OAuth2Client } from 'google-auth-library';

// 替换为你的 Google Client ID
const CLIENT_ID = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;

const client = new OAuth2Client(CLIENT_ID);

export async function POST(req: Request) {
  try {
    const { credential } = await req.json();  // 获取客户端传来的 credential

    // 验证 Google 登录凭证
    const ticket = await client.verifyIdToken({
      idToken: credential,
      audience: CLIENT_ID,  // 确保客户端 ID 匹配
    });

    const payload = ticket.getPayload();  // 获取用户信息

    // 返回解析后的 Google 用户信息
    return new Response(
      JSON.stringify({ success: true, user: payload }),
      { status: 200 }
    );
  } catch (error) {
    console.error(error);
    return new Response(
      JSON.stringify({ success: false, error: 'Invalid credential' }),
      { status: 400 }
    );
  }
}