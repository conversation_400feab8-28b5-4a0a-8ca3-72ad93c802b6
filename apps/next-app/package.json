{"name": "next-app", "version": "1.0.0", "private": true, "scripts": {"dev": "cp .env.dev .env.local &&  next dev", "build:staging": "cp .env.staging .env.local && next build", "build:production": "cp .env.production .env.local && next build", "build:gray": "cp .env.gray .env.local && next build", "build": "cp .env.production .env.local && next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@gluestack-ui/nativewind-utils": "latest", "@gluestack-ui/overlay": "latest", "@gluestack-ui/toast": "latest", "@gluestack/ui-next-adapter": "latest", "@next/third-parties": "^15.3.1", "@quantum/app": "workspace:*", "@quantum/components": "workspace:*", "@quantum/shared": "workspace:*", "@react-native-async-storage/async-storage": "^2.1.2", "@react-oauth/google": "^0.12.1", "@unitools/image-next": "^0.0.6", "@unitools/link-next": "^0.0.1", "@unitools/router-next": "^0.0.1", "autoprefixer": "latest", "google-auth-library": "^9.15.1", "i18next": "^24.2.3", "i18next-chained-backend": "^4.6.2", "i18next-http-backend": "^3.0.2", "i18next-resources-to-backend": "^1.2.1", "nativewind": "4.0.36", "negotiator": "^1.0.0", "next": "^14.0.4", "next-auth": "^4.24.11", "next-i18n-router": "^5.5.1", "next-i18next": "^15.4.2", "postcss": "latest", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.4.1", "react-native-web": "latest", "tailwindcss": "3.4.3"}, "devDependencies": {"@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "babel-plugin-react-native-web": "^0.19.10", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "jscodeshift": "0.15.2", "typescript": "^5.3.3"}}