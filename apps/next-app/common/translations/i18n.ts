import i18n from 'i18next';
import Backend from 'i18next-chained-backend';

import HttpApi from 'i18next-http-backend';
import { initReactI18next } from 'react-i18next';
import { i18nNamespaces } from './initTranslations';
import { getI18nPath } from '@/stationGroupConfig';
export { i18nLanguages } from './config';

declare module 'i18next' {
  interface CustomTypeOptions {
    returnNull: false;
    // resources: (typeof resources)[LanguageType.EN];
  }
}

export const addResourceBundle = (lang: string, resources: any) => {
  Object.entries(resources).forEach(([lang, nsRes]: any) => {
    Object.entries(nsRes).forEach(([ns, res]) => {
      i18n.addResourceBundle(lang, ns, res);
    });
  });
};

export const initI18n = (lang: string, resources: any, domain: string) => {

  const i18nPath = getI18nPath(domain)
  i18n
    .use(Backend)
    .use(initReactI18next) // passes i18n down to react-i18next
    .init({
      nsSeparator: '.',
      returnNull: false,
      fallbackLng: 'en',
      load: 'currentOnly',
      lng: lang || 'en', // language to use, more information here: https://www.i18next.com/overview/configuration-options#languages-namespaces-resources
      ns: i18nNamespaces,
      interpolation: {
        escapeValue: false, // react already safes from xss
      },
      // resources,
      backend: {
        backends: [
          // LocalStorageBackend, // primary backend
          HttpApi, // fallback backend
        ],
        backendOptions: [
          // {
          //   /* options for primary backend */
          // },
          {
            /* options for secondary backend */
            loadPath: `/${i18nPath}/{{lng}}/{{ns}}.json`, // http load path for my own fallback
          },
        ],
      },
      // react: {
      //   useSuspense: false,
      // },
    });

  addResourceBundle(lang, resources);
};
export default i18n;
