import { createInstance } from 'i18next';
import { initReactI18next } from 'react-i18next/initReactI18next';
import resourcesToBackend from 'i18next-resources-to-backend';
import { i18n as i18nConfig } from '@/next-i18next.config';
import { getI18nPath } from '@/stationGroupConfig'

export const i18nNamespaces = [
  'translation',
  'menu',
  'verification',
  'crypto_stat',
  'features_page',
  'exchange_page',
  'portfolio_page',
  'error_code',
];

export default async function initTranslations(
  locale: string,
  namespaces: string[],
  domain: string,
  i18nInstance?: any,
  resources?: any,
) {
  i18nInstance = i18nInstance || createInstance();

  i18nInstance.use(initReactI18next);

  if (!resources) {
    const i18nPath = getI18nPath(domain)
    i18nInstance.use(
      resourcesToBackend(
        (language: string, namespace: string) => import(`@/public/${i18nPath}/${language}/${namespace}.json`),
      ),
    );
  }

  await i18nInstance.init({
    lng: locale,
    resources,
    fallbackLng: i18nConfig.defaultLocale,
    supportedLngs: i18nConfig.locales,
    defaultNS: namespaces[0],
    fallbackNS: namespaces[0],
    ns: namespaces,
    preload: resources ? [] : i18nConfig.locales,
  });

  return {
    i18n: i18nInstance,
    resources: {
      [locale]: i18nInstance.services.resourceStore.data[locale],
    },
    t: i18nInstance.t,
  };
}
