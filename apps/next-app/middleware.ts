import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

import { i18n } from './next-i18next.config.js'

import { i18nRouter } from 'next-i18n-router'

export function middleware(request: NextRequest) {
  // 根据路径条件调用相应的 i18nRouter
  let i18nResponse;

  if (
    request.nextUrl.pathname.startsWith('/en/') ||
    request.nextUrl.pathname === '/en'
  ) {
    i18nResponse = i18nRouter(request, {
      ...i18n,
      localeCookie: 'Locale',
      prefixDefault: true,
    });
  } else {
    i18nResponse = i18nRouter(request, {
      ...i18n,
      localeCookie: 'Locale',
    });
  }

  // 添加自定义头部到响应中
  const responseHeaders = new Headers(i18nResponse.headers);
  responseHeaders.set("x-current-host", request.nextUrl.host);

  // 返回修改后的响应
  return new NextResponse(i18nResponse.body, {
    status: i18nResponse.status,
    statusText: i18nResponse.statusText,
    headers: responseHeaders,
  });
}

export const config = {
  // Matcher ignoring `/_next/`, `/api/`, `/locales/`, `/assets/`, root path, `.png`, and `.svg` files
  matcher: [
    '/((?!api|backend-api|_next/static|locales|site_a_locales|_next/image|assets|sitemap.xml|favicon.ico|.*\\.png$|.*\\.svg$).*)',
  ],
}
