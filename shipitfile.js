const path = require('path');
const fs = require('fs');
const rimraf = require('rimraf');
const { kebabCase } = require('lodash');
const os = require('os');
const notify = require('./scripts/deploy/notify-staging-update');
const { program } = require('commander');

program.requiredOption('--app, --app <char>');
program.option('--network, --network <type>', 'MAINNET or TESTNET');

program.parse();
const options = program.opts();

const app = options.app;

if (!app) {
  console.error('--app option not defined');
  process.exit(-1);
}

const BASE_ENV = {
  NODE_ENV: 'production',
  FEISHU_I18N_BOT_APP_ID: process.env.FEISHU_I18N_BOT_APP_ID,
  FEISHU_I18N_BOT_APP_SECRET: process.env.FEISHU_I18N_BOT_APP_SECRET,
  REDIS_URL: process.env.REDIS_URL,
  PORT: process.env.PORT,
  branch: process.env.branch,
  commitHash: process.env.commitHash,
  NETWORK_TYPE: options.network || 'MAINNET',
};

function loadDeployConfig() {
  const globalDeployConfig = require('./deploy.config')(BASE_ENV);
  if (globalDeployConfig[app]) return globalDeployConfig[app];

  Array(10).forEach(() => console.warn('Please setting deploy info in ./deploy.config.js'));
  const jsPath = `./apps/${app}/deploy.js`;
  if (fs.existsSync(jsPath)) {
    return require(jsPath)(BASE_ENV);
  } else {
    return require(`./apps/${app}/deploy.json`);
  }
}

const deployConfig = loadDeployConfig();
if (!deployConfig) {
  console.error(`./apps/${app}/deploy.json not found`);
  process.exit(-1);
}

const PROD_ENV = {
  ...BASE_ENV,
  // REDIS_URL:
  //   'redis://aliens-redis.t5boy8.clustercfg.memorydb.ap-northeast-1.amazonaws.com:6379',
  // STATIC_BASEURL: '',
  APP_ENV: 'production',
};

const STAGE_ENV = {
  ...BASE_ENV,
  // REDIS_URL:
  //   'redis://redis-test.t5boy8.clustercfg.apne1.cache.amazonaws.com:6379',
  // STATIC_BASEURL: '',
  APP_ENV: 'staging',
};

const DEPLOY_PATH = path.join('/opt', 'node', 'quantum-trading', deployConfig.deployPath || app);

const envToStr = (ENV) =>
  Object.keys(ENV)
    .map((key) => (ENV[key] ? `${key}="${ENV[key]}"` : false))
    .filter(Boolean)
    .join(' ');

const workspaceTmpDir = /* path.join(os.tmpdir(), 'shipit'); */ __dirname;

// const ignorePath = ['.turbo', 'node_modules', 'src', '.DS_Store', '.git'];

module.exports = (shipit) => {
  let ENV_S;
  const stagBranchName = kebabCase(process.env.branch.replace(/^origin\//, ''));

  require('shipit-deploy')(shipit);

  shipit.initConfig({
    default: {
      // ignores: ['.git', 'src'],
      deployTo: DEPLOY_PATH,
      repositoryUrl: 'https://github.com/srcalienswap/FE-Quantum-Trading-Terminal.git',
      branch: 'origin/main',
      keepReleases: 3,
      workspace: workspaceTmpDir,
      shallowClone: false,
      keepWorkspace: true,
      // dirToCopy: 'release',
      copy: false,
      deploy: {
        remoteCopy: {
          rsync: [
            `--filter='- apps/${app}/.turbo'`,
            `--filter='- apps/${app}/node_modules'`,
            `--filter='- apps/${app}/src'`,
            `--filter='- apps/${app}/.DS_Store'`,
            `--filter='- apps/${app}/.git'`,
            `--filter='+ apps/${app}/***'`,
            `--filter='- apps/**'`,
            `--filter='- node_modules'`,
            `--filter='- .git'`,
            `--filter='- src'`,
            '--del',
          ],
        },
      },
    },
    production: {
      branch: process.env.branch,
      servers: deployConfig.production.servers.map((host) => ({
        host: host,
        user: 'root',
      })),
      // servers: [{ host: '127.0.0.1', user: 'root', port: '2200' }],
      deployTo: path.join(DEPLOY_PATH, 'production'),
      PORT: deployConfig.production.port,
      deployEnv: {
        SETUP_ENV: 'production',
        ...PROD_ENV,
      },
    },
    gray: {
      servers: deployConfig.gray.servers.map((host) => ({
        host: host,
        user: 'root',
      })),
      // servers: [{ host: '127.0.0.1', user: 'root', port: '2200' }],
      branch: process.env.branch,
      deployTo: path.join(DEPLOY_PATH, 'gray'),
      keepReleases: 1,
      PORT: deployConfig.gray.port,
      deployEnv: {
        SETUP_ENV: 'gray',
        ...PROD_ENV,
      },
    },
    staging: {
      // servers: [{ host: '127.0.0.1', user: 'root', port: '2200' }],
      servers: [{ host: '************', user: 'root' }],
      deployTo: path.join(DEPLOY_PATH, stagBranchName),
      branch: process.env.branch,
      keepReleases: 1,
      deployEnv: STAGE_ENV,
    },
  });

  shipit.blTask('local:sync-i18n', async () => {
    shipit.log('local sync-i18n');
    await shipit.local(`yarn run i18n --app ${app}`, {
      cwd: shipit.workspace,
    });
  });

  shipit.blTask('local:build', async () => {
    shipit.log('local build');
    await shipit.local(`${ENV_S} && yarn workspace ${app} run build:${shipit.environment}`, {
      cwd: shipit.workspace,
    });
  });

  shipit.blTask('remove-deploy-dir', async () => {
    await shipit.remote('rm -rf release', {
      cwd: shipit.releasePath,
    });
  });

  shipit.blTask('remote-install', async () => {
    shipit.log('yarn deploy');
    await shipit.remote('whoami');
    await shipit.remote('source ~/.bashrc && echo $NVM_DIR');
    await shipit.remote('pwd', {
      cwd: shipit.releasePath,
    });
    await shipit.remote('corepack enable', {
      cwd: shipit.releasePath,
    });
    await shipit.remote('yarn config set cacheFolder ~/.yarn/cache', {
      cwd: shipit.releasePath,
    });
    await shipit.remote(`yarn install`, {
      cwd: shipit.releasePath,
    });
  });

  shipit.blTask('start_pm2', async () => {
    shipit.log('Launching pm2');
    if (shipit.environment === 'staging') {
      const res = await shipit
        .remote(
          [
            // set system env
            ENV_S,
            'APP_ENV=staging',
            // run deploy script
            'node ./scripts/deploy/deploy-staging.js',
            `--app ${app}`,
            `--deploy-cwd ${shipit.config.deployTo}`,
            //
            `&&`,
            // save
            `pm2 save`,
          ].join(' '),
          { cwd: path.join(shipit.config.deployTo, 'current') },
        )
        .then((res) => {
          const matcher = /--START-DEPLOY-INFO--(.*?)--END-DEPLOY-INFO--/gi.exec(res[0].stdout);
          if (matcher && matcher[1]) {
            const deployInfo = JSON.parse(matcher[1]);
            console.log('deployInfo:', JSON.stringify(deployInfo));
            notify.stagingNotify(app, deployInfo.port);
          } else {
            process.exit(-1);
          }
        });
    } else {
      const command = [
        // set system env
        ENV_S,
        'APP_ENV=production',
        // run deploy script
        'node ./scripts/deploy/deploy.js',
        `--app ${app}`,
        `--port ${shipit.config.PORT}`,
        `--deploy-cwd ${shipit.config.deployTo}`,
        //
        `&&`,
        // save
        `pm2 save`,
      ].join(' ');

      await shipit.remote(command, { cwd: path.join(shipit.config.deployTo, 'current') }).then((res) => {
        const matcher = /--START-DEPLOY-INFO--(.*?)--END-DEPLOY-INFO--/gi.exec(res[0].stdout);
        if (matcher && matcher[1]) {
          const deployInfo = JSON.parse(matcher[1]);
          console.log('deployInfo:', JSON.stringify(deployInfo));
          notify.productionNotify(app, deployConfig.link, shipit.environment === 'gray');
        } else {
          process.exit(-1);
        }
      });
    }
  });

  shipit.on('deploy', () => {
    shipit.log(`App [${app}] ready to deploy`);
    ENV_S = envToStr(shipit.config.deployEnv);
    shipit.log('💬️ ENV_S:', ENV_S);
    // console.log(shipit.config);
    // process.exit();
  });

  shipit.on('fetched', () => {
    shipit.start(['local:sync-i18n', 'local:build']);
  });

  shipit.on('updated', () => {
    shipit.log('project deploy');
    shipit.start(['remote-install']);
  });

  shipit.on('cleaned', () => {
    shipit.start(['start_pm2']);
  });
};
