{"private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "clean": "turbo run clean && rm -rf node_modules", "run:expo": "cd apps/expo-app && yarn start", "run:next": "cd apps/next-app && yarn dev", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\" --ignore-path .gitignore", "eject": "npx @gluestack-ui/universal-cli@latest eject", "i18n": "FEISHU_I18N_BOT_APP_ID=cli_a76cb5a356b8d02e FEISHU_I18N_BOT_APP_SECRET=VWxNtj9sU94phXPGjxzH0evct3a3owFu node ./scripts/lark/i18n/pull-i18n.js"}, "devDependencies": {"axios": "^1.8.4", "lodash": "^4.17.21", "mkdirp": "^3.0.1", "p-limit": "^6.2.0", "prettier": "^3.1.1", "shipit-cli": "^5.3.0", "shipit-deploy": "^5.3.0", "turbo": "latest"}, "packageManager": "yarn@3.8.7", "engines": {"node": ">=18"}, "dependencies": {"commander": "^12.0.0", "get-port": "^7.1.0", "gitlog": "4", "react-native-markdown-display": "^7.0.2"}, "resolutions": {"react-native-css-interop@0.0.36": "patch:react-native-css-interop@npm%3A0.0.36#./.yarn/patches/react-native-css-interop-npm-0.0.36-4c3a9a1b6f.patch"}}