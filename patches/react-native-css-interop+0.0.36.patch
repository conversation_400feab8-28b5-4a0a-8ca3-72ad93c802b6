diff --git a/node_modules/react-native-css-interop/dist/runtime/web/color-scheme.js b/node_modules/react-native-css-interop/dist/runtime/web/color-scheme.js
index b1d62c6..3aaedc1 100644
--- a/node_modules/react-native-css-interop/dist/runtime/web/color-scheme.js
+++ b/node_modules/react-native-css-interop/dist/runtime/web/color-scheme.js
@@ -1,3 +1,4 @@
+
 "use strict";
 Object.defineProperty(exports, "__esModule", { value: true });
 exports.colorScheme = void 0;
@@ -14,7 +15,7 @@ if (darkMode === "class") {
         ? "dark"
         : "light";
 }
-const systemColorScheme = (0, observable_1.observable)(appearance.getColorScheme() ?? "light");
+const systemColorScheme = (0, observable_1.observable)(appearance?.getColorScheme?.() ?? "light");
 const colorSchemeObservable = (0, observable_1.observable)(initialColor, { fallback: systemColorScheme });
 exports.colorScheme = {
     set(value) {
@@ -41,7 +42,7 @@ exports.colorScheme = {
     toggle() {
         let current = colorSchemeObservable.get();
         if (current === undefined)
-            current = appearance.getColorScheme() ?? "light";
+            current = appearance?.getColorScheme?.() ?? "light";
         exports.colorScheme.set(current === "light" ? "dark" : "light");
     },
     [shared_1.INTERNAL_RESET]: (appearance) => {
@@ -51,8 +52,8 @@ exports.colorScheme = {
 };
 function resetAppearanceListeners($appearance) {
     appearance = $appearance;
-    appearanceListener?.remove();
-    appearanceListener = appearance.addChangeListener((state) => {
+    appearanceListener?.remove?.();
+    appearanceListener = appearance?.addChangeListener?.((state) => {
         if (react_native_1.AppState.currentState === "active") {
             systemColorScheme.set(state.colorScheme ?? "light");
         }
